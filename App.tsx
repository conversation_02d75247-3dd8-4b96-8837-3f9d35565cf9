/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect, useState, useRef} from 'react';
import {Provider as AntProvider} from '@ant-design/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {pageRoute} from './src/routes';
import databaseService from './src/services/DatabaseService';
import PaymentDetectionService from './src/services/PaymentDetectionService';
import {
  AppState,
  AppStateStatus,
  Platform,
  PermissionsAndroid,
  LogBox,
} from 'react-native';
// 创建一个导航引用，可以在组件外部使用
import {createNavigationContainerRef} from '@react-navigation/native';
import {ToastProvider} from './src/context/ToastContext';
import notificationService from './src/services/NotificationService';
import notifee from '@notifee/react-native';
import {ThemeProvider} from './src/context/ThemeContext';

// 忽略特定的黄色警告
LogBox.ignoreLogs([
  'ViewPropTypes will be removed',
  'ColorPropType will be removed',
]);

const Stack = createStackNavigator();
// 创建导航引用
const navRef = createNavigationContainerRef();

const App = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasSeenWelcome, setHasSeenWelcome] = useState(false);
  const appState = useRef(AppState.currentState);
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const pendingNavigation = useRef(null);

  // 在应用启动时检查是否需要自动导出
  useEffect(() => {
    const checkAutoExport = async () => {
      try {
        // 请求通知权限
        await notifee.requestPermission();
        // 检查并执行自动导出
        await notificationService.checkAndAutoExport();
        await notificationService.scheduleDailyReminders();
      } catch (error) {
        console.error('检查自动导出失败:', error);
      }
    };

    checkAutoExport();
  }, []);

  useEffect(() => {
    const initApp = async () => {
      try {
        // 检查权限并启动监听
        const hasPermissions = await PaymentDetectionService.checkPermissions();
        console.log('hasPermissions', hasPermissions);
        // 如果已有权限，开始监听
        if (hasPermissions) {
          PaymentDetectionService.startListening();
        }
        // 初始化数据库
        await databaseService.initDatabase();
        console.log('初始化数据库成功');

        // 检查用户是否已经看过欢迎页
        const hasSeenWelcomeValue = await AsyncStorage.getItem(
          'hasSeenWelcome',
        );
        setHasSeenWelcome(hasSeenWelcomeValue === 'true');
        // 请求通知权限
        const requestNotificationPermissions = async () => {
          if (Platform.OS === 'android' && Platform.Version >= 33) {
            try {
              const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
                {
                  title: '通知权限',
                  message: '我们需要通知权限来提醒您信用卡账单',
                  buttonNeutral: '稍后提醒',
                  buttonNegative: '拒绝',
                  buttonPositive: '允许',
                },
              );
              console.log('通知权限状态:', granted);
            } catch (err) {
              console.warn('请求通知权限失败:', err);
            }
          }
        };
        requestNotificationPermissions();
        // 首次加载时设置通知
        notificationService.scheduleCreditCardReminders();
      } catch (error) {
        console.error('初始化应用失败', error);
      } finally {
        setIsLoading(false);
      }
    };

    initApp();

    // 监听应用状态变化
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    // 监听新的 Intent
    const unsubscribe = PaymentDetectionService.listenForNewIntent(() => {
      console.log('收到新的 Intent');
      handleInitialProps();
    });

    return () => {
      databaseService.closeDatabase();
      PaymentDetectionService.stopListening();
      subscription.remove();
      unsubscribe();
    };
  }, []);

  // 当导航容器准备好时，处理任何待处理的导航
  useEffect(() => {
    if (isNavigationReady && pendingNavigation.current) {
      console.log('执行待处理的导航:', pendingNavigation.current);
      const {routeName, params} = pendingNavigation.current;
      navRef.navigate(routeName, params);
      pendingNavigation.current = null;
    }
  }, [isNavigationReady]);

  const handleAppStateChange = (nextState: AppStateStatus) => {
    // 从后台回到前台时触发
    if (
      appState.current.match(/inactive|background/) &&
      nextState === 'active'
    ) {
      onShow(); // 自定义的显示回调
    }

    appState.current = nextState;
  };

  const onShow = () => {
    console.log('App回到前台');
    handleInitialProps();
    // notificationService.scheduleCreditCardReminders();
  };

  // 安全导航函数 - 如果导航容器准备好，立即导航；否则，存储导航请求
  const safeNavigate = (routeName, params) => {
    console.log('尝试导航到:', routeName, params);
    if (navRef.isReady()) {
      console.log('导航容器已准备好，立即导航');
      navRef.navigate(routeName, params);
    } else {
      console.log('导航容器未准备好，存储导航请求');
      pendingNavigation.current = {routeName, params};
    }
  };

  const handleInitialProps = async () => {
    try {
      // 获取启动参数
      const props = await PaymentDetectionService.getInitialProps();
      console.log('获取到启动参数:', props);

      if (props && props.openAddTransaction) {
        // 创建导航参数
        const params = {
          type: props.transactionType || 'expense',
          amount: parseFloat(props.transactionAmount || '0'),
          platform: props.transactionPlatform || 'alipay',
          autoDetected: props.autoDetected || false,
        };

        console.log('准备导航到添加交易页面:', params);

        // 使用安全导航函数
        setTimeout(() => {
          safeNavigate('addTransaction', params);
        }, 500);
      }
    } catch (error) {
      console.error('处理启动参数失败:', error);
    }
  };

  if (isLoading) {
    // 可以在这里添加一个加载页面或启动屏幕
    return null;
  }

  return (
    <ThemeProvider>
      <ToastProvider>
        <AntProvider>
          <NavigationContainer
            ref={navRef}
            onReady={() => {
              console.log('导航容器已准备好');
              setIsNavigationReady(true);
            }}>
            <Stack.Navigator
              initialRouteName={hasSeenWelcome ? 'home' : 'welcome'}
              screenOptions={{
                headerShown: false,
                cardStyleInterpolator: ({current}) => ({
                  cardStyle: {
                    opacity: current.progress,
                  },
                }),
                detachPreviousScreen: true,
              }}>
              {/* 渲染所有路由 */}
              {pageRoute.map(route => (
                <Stack.Screen
                  key={route.name}
                  name={route.name}
                  component={route.component}
                />
              ))}
            </Stack.Navigator>
          </NavigationContainer>
        </AntProvider>
      </ToastProvider>
    </ThemeProvider>
  );
};

export default App;
