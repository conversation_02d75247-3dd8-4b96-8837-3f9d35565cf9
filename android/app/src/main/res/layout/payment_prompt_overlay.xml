<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageView
                android:id="@+id/platform_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="12dp"
                android:src="@drawable/ic_payment_default" />

            <TextView
                android:id="@+id/title_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="检测到支付"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <Button
                android:id="@+id/close_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@android:color/transparent"
                android:text="×"
                android:textSize="20sp"
                android:textColor="#666666" />
        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F0F0F0"
            android:layout_marginBottom="16dp" />

        <!-- 金额区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="支付金额"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/amount_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥ 0.00"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="#333333" />
        </LinearLayout>

        <!-- 提示文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="是否将此笔支付添加到记账本？"
            android:textSize="16sp"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/cancel_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="取消"
                android:textSize="16sp"
                android:textColor="#666666"
                android:background="#F5F5F5" />

            <Button
                android:id="@+id/confirm_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1.5"
                android:layout_marginStart="8dp"
                android:text="添加到记账本"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:background="#5E72E4" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 