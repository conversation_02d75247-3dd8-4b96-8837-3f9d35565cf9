package com.snow;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.ImageButton;
import android.util.Log;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;

public class PaymentAccessibilityService extends AccessibilityService {
    private static final String TAG = "PaymentAccessibility";
    private static ReactApplicationContext reactContext;
    private WindowManager windowManager;
    private View overlayView;
    private boolean isOverlayShowing = false;
    
    // 改进的重复检测机制
    private String lastPaymentPlatform = "";
    private String lastPaymentAmount = "";
    private long lastPaymentTime = 0;
    private static final long PAYMENT_COOLDOWN = 60000; // 60秒内不重复提示同一笔支付
    private static final long DIFFERENT_AMOUNT_COOLDOWN = 10000; // 不同金额间隔10秒

    // 添加支付事件历史记录
    private List<PaymentEvent> recentPaymentEvents = new ArrayList<>();
    private static final int MAX_RECENT_EVENTS = 10;

    // 支付事件记录类
    private static class PaymentEvent {
        String platform;
        String amount;
        long timestamp;
        String source; // "accessibility" 或 "notification"

        PaymentEvent(String platform, String amount, long timestamp, String source) {
            this.platform = platform;
            this.amount = amount;
            this.timestamp = timestamp;
            this.source = source;
        }
    }

    public static void setReactContext(ReactApplicationContext context) {
        reactContext = context;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        try {
            final int eventType = event.getEventType();
            String packageName = event.getPackageName() != null ? event.getPackageName().toString() : "";
            
            // 增强日志输出
            Log.d(TAG, "收到无障碍事件: 类型=" + eventType + ", 包名=" + packageName + ", 事件文本=" + 
                  (event.getText() != null && !event.getText().isEmpty() ? event.getText().toString() : "无"));
            
            // 只处理支付宝和微信的事件
            if (!packageName.equals("com.tencent.mm") && !packageName.equals("com.eg.android.AlipayGphone")) {
                return;
            }

            // 处理窗口状态变化事件
            if (eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED || 
                eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED) {
                
                AccessibilityNodeInfo rootNode = getRootInActiveWindow();
                if (rootNode == null) {
                    Log.d(TAG, "无法获取根节点");
                    return;
                }

                // 检测支付宝支付成功页面
                if (packageName.equals("com.eg.android.AlipayGphone")) {
                    Log.d(TAG, "检测支付宝页面");
                    detectAlipayPayment(rootNode);
                }
                
                // 检测微信支付成功页面
                else if (packageName.equals("com.tencent.mm")) {
                    Log.d(TAG, "检测微信页面");
                    detectWechatPayment(rootNode);
                }
                
                rootNode.recycle();
            }
        } catch (Exception e) {
            Log.e(TAG, "处理无障碍事件时出错: " + e.getMessage(), e);
        }
    }

    private void detectAlipayPayment(AccessibilityNodeInfo rootNode) {
        try {
            // 打印所有文本节点，帮助分析界面结构
            Log.d(TAG, "开始分析支付宝界面结构");
            printAllTextNodesWithDepth(rootNode, "支付宝", 0);

            // 改进的检测逻辑
            PaymentDetectionResult result = analyzeAlipayPaymentPage(rootNode);

            Log.d(TAG, "支付宝检测结果: " + result.toString());

            if (result.isPaymentSuccessPage && result.amount != null && !result.amount.isEmpty()) {
                Log.d(TAG, "检测到支付宝交易成功页面，金额: " + result.amount);
                // 发送支付事件
                sendPaymentEvent("alipay", result.amount);
                // 显示悬浮窗，询问是否记账
                showPaymentPrompt("alipay", result.amount);
            }
        } catch (Exception e) {
            Log.e(TAG, "检测支付宝支付时出错: " + e.getMessage(), e);
        }
    }

    // 新增：支付检测结果类
    private static class PaymentDetectionResult {
        boolean isPaymentSuccessPage = false;
        String amount = "";
        int confidenceScore = 0;
        String detectionMethod = "";

        @Override
        public String toString() {
            return "PaymentDetectionResult{" +
                    "isPaymentSuccessPage=" + isPaymentSuccessPage +
                    ", amount='" + amount + '\'' +
                    ", confidenceScore=" + confidenceScore +
                    ", detectionMethod='" + detectionMethod + '\'' +
                    '}';
        }
    }

    // 新增：改进的支付宝页面分析方法
    private PaymentDetectionResult analyzeAlipayPaymentPage(AccessibilityNodeInfo rootNode) {
        PaymentDetectionResult result = new PaymentDetectionResult();

        // 获取所有文本节点
        List<AccessibilityNodeInfo> allNodes = new ArrayList<>();
        getAllNodes(rootNode, allNodes);

        // 收集所有文本内容
        List<String> allTexts = new ArrayList<>();
        for (AccessibilityNodeInfo node : allNodes) {
            if (node.getText() != null) {
                String text = node.getText().toString().trim();
                if (!text.isEmpty()) {
                    allTexts.add(text);
                }
            }
        }

        // 方法1：精确匹配支付成功页面特征
        if (detectAlipaySuccessPageByExactMatch(allTexts, result)) {
            result.detectionMethod = "精确匹配";
            return result;
        }

        // 方法2：模糊匹配（降级方案）
        if (detectAlipaySuccessPageByFuzzyMatch(allTexts, result)) {
            result.detectionMethod = "模糊匹配";
            return result;
        }

        return result;
    }

    // 新增：精确匹配支付成功页面
    private boolean detectAlipaySuccessPageByExactMatch(List<String> texts, PaymentDetectionResult result) {
        boolean hasSuccessIndicator = false;
        boolean hasAmountContext = false;
        String detectedAmount = "";

        for (String text : texts) {
            // 精确的成功指示器
            if (text.equals("交易成功") || text.equals("支付成功") || text.equals("付款成功")) {
                hasSuccessIndicator = true;
                Log.d(TAG, "找到精确成功指示器: " + text);
            }

            // 带上下文的金额检测
            String amountWithContext = extractAmountWithContext(text);
            if (!amountWithContext.isEmpty()) {
                hasAmountContext = true;
                detectedAmount = amountWithContext;
                Log.d(TAG, "找到带上下文的金额: " + detectedAmount + " 来源: " + text);
            }
        }

        if (hasSuccessIndicator && hasAmountContext) {
            result.isPaymentSuccessPage = true;
            result.amount = detectedAmount;
            result.confidenceScore = 95;
            return true;
        }

        return false;
    }

    // 新增：模糊匹配支付成功页面
    private boolean detectAlipaySuccessPageByFuzzyMatch(List<String> texts, PaymentDetectionResult result) {
        int successIndicators = 0;
        int contextIndicators = 0;
        String detectedAmount = "";

        for (String text : texts) {
            // 成功相关文本
            if (text.contains("成功") || text.contains("完成")) {
                successIndicators++;
            }

            // 支付上下文
            if (text.contains("订单金额") || text.contains("付款金额") ||
                text.contains("支付时间") || text.contains("付款方式")) {
                contextIndicators++;
            }

            // 提取金额
            if (detectedAmount.isEmpty()) {
                detectedAmount = extractAmountWithContext(text);
            }
        }

        // 降低阈值，但要求有明确的金额
        if (successIndicators >= 1 && contextIndicators >= 1 && !detectedAmount.isEmpty()) {
            result.isPaymentSuccessPage = true;
            result.amount = detectedAmount;
            result.confidenceScore = 70;
            return true;
        }

        return false;
    }

    // 获取所有节点
    private void getAllNodes(AccessibilityNodeInfo node, List<AccessibilityNodeInfo> result) {
        if (node == null) return;
        
        result.add(node);
        
        for (int i = 0; i < node.getChildCount(); i++) {
            getAllNodes(node.getChild(i), result);
        }
    }

    // 打印所有文本节点，帮助调试
    private void printAllTextNodes(AccessibilityNodeInfo node, String appName) {
        printAllTextNodesWithDepth(node, appName, 0);
    }

    private void printAllTextNodesWithDepth(AccessibilityNodeInfo node, String appName, int depth) {
        // 限制递归深度，防止栈溢出
        if (node == null || depth > 10) return;
        
        try {
            if (node.getText() != null && !node.getText().toString().isEmpty()) {
                StringBuilder indent = new StringBuilder();
                for (int i = 0; i < depth; i++) {
                    indent.append("  ");
                }
                Log.d(TAG, appName + " 文本节点: " + indent + node.getText() + 
                      " [类名=" + node.getClassName() + "]");
            }
            
            for (int i = 0; i < node.getChildCount(); i++) {
                AccessibilityNodeInfo child = node.getChild(i);
                if (child != null) {
                    printAllTextNodesWithDepth(child, appName, depth + 1);
                    child.recycle();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "打印文本节点时出错: " + e.getMessage());
        }
    }

    // 打开我们的应用
    private void openOurApp() {
        try {
            Intent launchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(launchIntent);
                Log.d(TAG, "已发送打开应用的意图");
            } else {
                Log.e(TAG, "无法获取应用启动意图");
            }
        } catch (Exception e) {
            Log.e(TAG, "打开应用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void detectWechatPayment(AccessibilityNodeInfo rootNode) {
        try {
            // 打印所有文本节点，帮助分析界面结构
            Log.d(TAG, "开始分析微信界面结构");
            printAllTextNodesWithDepth(rootNode, "微信", 0);

            // 改进的检测逻辑
            PaymentDetectionResult result = analyzeWechatPaymentPage(rootNode);

            Log.d(TAG, "微信检测结果: " + result.toString());

            if (result.isPaymentSuccessPage && result.amount != null && !result.amount.isEmpty()) {
                Log.d(TAG, "检测到微信支付成功页面，金额: " + result.amount);
                // 发送支付事件
                sendPaymentEvent("wechat", result.amount);
                // 显示悬浮窗，询问是否记账
                showPaymentPrompt("wechat", result.amount);
            } else {
                Log.d(TAG, "未检测到微信支付成功页面或金额为空");
            }
        } catch (Exception e) {
            Log.e(TAG, "检测微信支付时出错: " + e.getMessage(), e);
        }
    }

    // 新增：改进的微信页面分析方法
    private PaymentDetectionResult analyzeWechatPaymentPage(AccessibilityNodeInfo rootNode) {
        PaymentDetectionResult result = new PaymentDetectionResult();

        // 获取所有文本节点
        List<AccessibilityNodeInfo> allNodes = new ArrayList<>();
        getAllNodes(rootNode, allNodes);

        // 收集所有文本内容
        List<String> allTexts = new ArrayList<>();
        for (AccessibilityNodeInfo node : allNodes) {
            if (node.getText() != null) {
                String text = node.getText().toString().trim();
                if (!text.isEmpty()) {
                    allTexts.add(text);
                }
            }
        }

        // 方法1：精确匹配微信支付成功页面特征
        if (detectWechatSuccessPageByExactMatch(allTexts, result)) {
            result.detectionMethod = "精确匹配";
            return result;
        }

        // 方法2：模糊匹配（降级方案）
        if (detectWechatSuccessPageByFuzzyMatch(allTexts, result)) {
            result.detectionMethod = "模糊匹配";
            return result;
        }

        return result;
    }

    // 新增：精确匹配微信支付成功页面
    private boolean detectWechatSuccessPageByExactMatch(List<String> texts, PaymentDetectionResult result) {
        boolean hasSuccessIndicator = false;
        boolean hasWechatContext = false;
        boolean hasAmountContext = false;
        String detectedAmount = "";

        for (String text : texts) {
            // 精确的成功指示器
            if (text.equals("支付成功") || text.equals("付款成功") || text.equals("转账成功")) {
                hasSuccessIndicator = true;
                Log.d(TAG, "找到微信精确成功指示器: " + text);
            }

            // 微信特有的上下文
            if (text.contains("收款方") || text.contains("收款时间") ||
                text.contains("交易单号") || text.equals("完成")) {
                hasWechatContext = true;
                Log.d(TAG, "找到微信上下文: " + text);
            }

            // 带上下文的金额检测
            String amountWithContext = extractAmountWithContext(text);
            if (!amountWithContext.isEmpty()) {
                hasAmountContext = true;
                detectedAmount = amountWithContext;
                Log.d(TAG, "找到微信带上下文的金额: " + detectedAmount + " 来源: " + text);
            }
        }

        if (hasSuccessIndicator && hasWechatContext && hasAmountContext) {
            result.isPaymentSuccessPage = true;
            result.amount = detectedAmount;
            result.confidenceScore = 95;
            return true;
        }

        return false;
    }

    // 新增：模糊匹配微信支付成功页面
    private boolean detectWechatSuccessPageByFuzzyMatch(List<String> texts, PaymentDetectionResult result) {
        int successIndicators = 0;
        int contextIndicators = 0;
        String detectedAmount = "";

        for (String text : texts) {
            // 成功相关文本
            if (text.contains("成功") || text.contains("完成")) {
                successIndicators++;
            }

            // 微信支付上下文
            if (text.contains("收款") || text.contains("交易") || text.contains("微信")) {
                contextIndicators++;
            }

            // 提取金额
            if (detectedAmount.isEmpty()) {
                detectedAmount = extractAmountWithContext(text);
            }
        }

        // 降低阈值，但要求有明确的金额
        if (successIndicators >= 1 && contextIndicators >= 1 && !detectedAmount.isEmpty()) {
            result.isPaymentSuccessPage = true;
            result.amount = detectedAmount;
            result.confidenceScore = 70;
            return true;
        }

        return false;
    }

    // 改进的金额提取方法：带上下文验证
    private String extractAmountWithContext(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 优先级1：明确的支付金额上下文
        String[] paymentContexts = {
            "订单金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "付款金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "支付金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "实付款[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "支出[￥¥]?(\\d+(?:\\.\\d{2})?)元?",
            "收入[￥¥]?(\\d+(?:\\.\\d{2})?)元?"
        };

        for (String contextPattern : paymentContexts) {
            Pattern pattern = Pattern.compile(contextPattern);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                String amount = matcher.group(1);
                // 验证金额合理性
                if (isValidAmount(amount)) {
                    Log.d(TAG, "通过上下文提取金额: " + amount + " 来源: " + text);
                    return amount;
                }
            }
        }

        // 优先级2：带货币符号的金额
        Pattern currencyPattern = Pattern.compile("[￥¥]\\s*(\\d+(?:\\.\\d{2})?)");
        Matcher currencyMatcher = currencyPattern.matcher(text);
        if (currencyMatcher.find()) {
            String amount = currencyMatcher.group(1);
            if (isValidAmount(amount)) {
                Log.d(TAG, "通过货币符号提取金额: " + amount + " 来源: " + text);
                return amount;
            }
        }

        // 优先级3：纯数字金额（需要更严格的验证）
        if (text.matches("\\d+\\.\\d{2}")) {
            String amount = text;
            if (isValidAmount(amount) && isLikelyPaymentAmount(amount)) {
                Log.d(TAG, "通过纯数字提取金额: " + amount + " 来源: " + text);
                return amount;
            }
        }

        return "";
    }

    // 验证金额是否合理
    private boolean isValidAmount(String amount) {
        try {
            double value = Double.parseDouble(amount);
            // 金额应该在合理范围内（0.01 到 100000）
            return value >= 0.01 && value <= 100000.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // 判断是否像支付金额（避免提取到其他数字）
    private boolean isLikelyPaymentAmount(String amount) {
        try {
            double value = Double.parseDouble(amount);
            // 排除一些明显不是支付金额的数字
            // 比如：版本号、时间戳、ID等
            return value >= 0.01 && value <= 50000.0 &&
                   !amount.equals("1.00") && // 排除常见的测试金额
                   !amount.equals("0.01");
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // 保留原有方法作为兼容
    private String extractAmount(String text) {
        return extractAmountWithContext(text);
    }

    private void showPaymentPrompt(String platform, String amount) {
        // 改进的重复检测逻辑
        if (isDuplicatePayment(platform, amount, "accessibility")) {
            return;
        }

        // 记录支付事件
        recordPaymentEvent(platform, amount, "accessibility");
        
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                Log.d(TAG, "准备显示悬浮窗: " + platform + " - " + amount);
                
                if (isOverlayShowing) {
                    // 如果已经显示了悬浮窗，不要重复显示
                    Log.d(TAG, "悬浮窗已经显示，不重复显示");
                    return;
                }
                
                // 检查是否有悬浮窗权限
                if (!Settings.canDrawOverlays(this)) {
                    Log.e(TAG, "没有悬浮窗权限，无法显示提示");
                    // 如果没有悬浮窗权限，直接发送事件并返回
                    sendPaymentEvent(platform, amount);
                    return;
                }
                
                // 创建悬浮窗视图
                LayoutInflater inflater = LayoutInflater.from(this);
                overlayView = inflater.inflate(R.layout.payment_prompt_overlay, null);
                
                if (overlayView == null) {
                    Log.e(TAG, "无法创建悬浮窗视图");
                    return;
                }
                
                // 设置文本
                TextView titleText = overlayView.findViewById(R.id.title_text);
                TextView amountText = overlayView.findViewById(R.id.amount_text);
                Button cancelButton = overlayView.findViewById(R.id.cancel_button);
                Button confirmButton = overlayView.findViewById(R.id.confirm_button);
                ImageView platformIcon = overlayView.findViewById(R.id.platform_icon);
                Button closeButton = overlayView.findViewById(R.id.close_button);
                
                if (titleText == null || amountText == null || cancelButton == null || confirmButton == null || platformIcon == null || closeButton == null) {
                    Log.e(TAG, "无法获取悬浮窗视图中的控件");
                    return;
                }
                
                if ("alipay".equals(platform)) {
                    platformIcon.setImageResource(R.drawable.ic_alipay);
                    titleText.setText("检测到支付宝支付");
                } else if ("wechat".equals(platform)) {
                    platformIcon.setImageResource(R.drawable.ic_wechat);
                    titleText.setText("检测到微信支付");
                } else {
                    platformIcon.setImageResource(R.drawable.ic_payment_default);
                    titleText.setText("检测到支付");
                }
                
                // 设置金额
                amountText.setText("¥ " + amount);
                
                // 设置按钮点击事件
                confirmButton.setOnClickListener(v -> {
                    try {
                        // 关闭悬浮窗
                        hideOverlay();
                        
                        // 打开我们的应用并传递参数
                        Intent launchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
                        if (launchIntent != null) {
                            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                            launchIntent.putExtra("openAddTransaction", true);
                            launchIntent.putExtra("transactionType", "expense");
                            launchIntent.putExtra("transactionAmount", amount);
                            launchIntent.putExtra("transactionPlatform", platform);
                            launchIntent.putExtra("autoDetected", true);
                            
                            Log.d(TAG, "准备启动应用并传递参数: " + platform + " - " + amount);
                            startActivity(launchIntent);
                            Log.d(TAG, "已启动应用并传递参数");
                        } else {
                            Log.e(TAG, "无法获取启动Intent");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "启动应用时出错: " + e.getMessage(), e);
                    }
                });
                
                cancelButton.setOnClickListener(v -> {
                    // 关闭悬浮窗
                    hideOverlay();
                });
                
                closeButton.setOnClickListener(v -> {
                    // 关闭按钮的处理逻辑
                    // 可以和 cancelButton 一样的处理
                    hideOverlay();
                });
                
                // 设置悬浮窗参数 - 使用更小的尺寸和更好的位置
                WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT);
                
                // 放在屏幕顶部中间
                params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
                params.y = 100; // 距离顶部100像素
                
                // 显示悬浮窗
                windowManager.addView(overlayView, params);
                isOverlayShowing = true;
                
                Log.d(TAG, "悬浮窗已显示");
                
                // 设置自动关闭定时器（8秒后自动关闭）
                new Handler().postDelayed(this::hideOverlay, 8000);
                
            } catch (Exception e) {
                Log.e(TAG, "显示悬浮窗失败: " + e.getMessage(), e);
                e.printStackTrace();
                
                // 如果显示悬浮窗失败，仍然发送支付事件
                sendPaymentEvent(platform, amount);
            }
        });
    }
    
    private void hideOverlay() {
        if (overlayView != null && isOverlayShowing) {
            try {
                windowManager.removeView(overlayView);
            } catch (Exception e) {
                Log.e(TAG, "移除悬浮窗失败: " + e.getMessage());
            }
            overlayView = null;
            isOverlayShowing = false;
        }
    }

    private void sendPaymentEvent(String platform, String amount) {
        try {
            ReactApplicationContext reactContext = PaymentDetectionModule.getReactContext();
            
            if (reactContext != null && reactContext.hasActiveReactInstance()) {
                Log.d(TAG, "准备发送支付事件: " + platform + " - " + amount);
                
                WritableMap params = Arguments.createMap();
                params.putString("platform", platform);
                params.putString("amount", amount);
                
                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("PaymentSuccess", params);
                
                Log.d(TAG, "支付事件已发送");
            } else {
                Log.e(TAG, "无法发送支付事件: ReactContext 为空或无活动实例");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送支付事件异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onInterrupt() {
        Log.d(TAG, "Accessibility Service interrupted");
        hideOverlay();
    }

    @Override
    public void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "无障碍服务已连接 - 版本 1.0");
        
        // 添加配置
        AccessibilityServiceInfo info = new AccessibilityServiceInfo();
        info.eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED | 
                          AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED;
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC;
        info.notificationTimeout = 100;
        info.packageNames = new String[]{"com.tencent.mm", "com.eg.android.AlipayGphone"};
        info.flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS;
        
        this.setServiceInfo(info);
        Log.d(TAG, "无障碍服务配置已设置");
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        hideOverlay();
    }

    // 改进的重复检测方法
    private boolean isDuplicatePayment(String platform, String amount, String source) {
        long currentTime = System.currentTimeMillis();

        // 检查是否与最近的支付完全相同
        if (platform.equals(lastPaymentPlatform) &&
            amount.equals(lastPaymentAmount) &&
            (currentTime - lastPaymentTime) < PAYMENT_COOLDOWN) {

            Log.d(TAG, "忽略重复的支付提示: " + platform + " - " + amount +
                  ", 距上次提示: " + (currentTime - lastPaymentTime) + "ms");
            return true;
        }

        // 检查是否与最近的事件重复（考虑不同来源）
        for (PaymentEvent event : recentPaymentEvents) {
            if (event.platform.equals(platform) &&
                event.amount.equals(amount) &&
                (currentTime - event.timestamp) < PAYMENT_COOLDOWN) {

                Log.d(TAG, "忽略重复的支付事件: " + platform + " - " + amount +
                      ", 来源: " + source + ", 历史来源: " + event.source +
                      ", 距离时间: " + (currentTime - event.timestamp) + "ms");
                return true;
            }
        }

        // 检查是否在短时间内有不同金额的支付（可能是误检）
        if (!amount.equals(lastPaymentAmount) &&
            platform.equals(lastPaymentPlatform) &&
            (currentTime - lastPaymentTime) < DIFFERENT_AMOUNT_COOLDOWN) {

            Log.d(TAG, "短时间内检测到不同金额，可能是误检: " + platform +
                  " 上次: " + lastPaymentAmount + " 本次: " + amount +
                  ", 间隔: " + (currentTime - lastPaymentTime) + "ms");
            return true;
        }

        return false;
    }

    // 记录支付事件
    private void recordPaymentEvent(String platform, String amount, String source) {
        long currentTime = System.currentTimeMillis();

        // 更新最近处理的支付信息
        lastPaymentPlatform = platform;
        lastPaymentAmount = amount;
        lastPaymentTime = currentTime;

        // 添加到历史记录
        recentPaymentEvents.add(new PaymentEvent(platform, amount, currentTime, source));

        // 清理过期的事件记录
        recentPaymentEvents.removeIf(event ->
            (currentTime - event.timestamp) > PAYMENT_COOLDOWN * 2);

        // 限制记录数量
        if (recentPaymentEvents.size() > MAX_RECENT_EVENTS) {
            recentPaymentEvents.remove(0);
        }

        Log.d(TAG, "记录支付事件: " + platform + " - " + amount + " 来源: " + source);
    }

    private void findPossibleAmountNodes(AccessibilityNodeInfo node, List<AccessibilityNodeInfo> result) {
        if (node == null) return;

        if (node.getText() != null) {
            String text = node.getText().toString();
            // 检查文本是否可能包含金额
            if (text.contains("¥") || text.contains("￥") ||
                text.matches(".*\\d+\\.\\d{2}.*") ||
                text.matches(".*\\d+元.*")) {
                result.add(node);
            }
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            findPossibleAmountNodes(node.getChild(i), result);
        }
    }
}