package com.snow;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.ImageButton;
import android.util.Log;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;

public class PaymentAccessibilityService extends AccessibilityService {
    private static final String TAG = "PaymentAccessibility";
    private static ReactApplicationContext reactContext;
    private WindowManager windowManager;
    private View overlayView;
    private boolean isOverlayShowing = false;
    
    // 添加记录最近处理过的支付信息
    private String lastPaymentPlatform = "";
    private String lastPaymentAmount = "";
    private long lastPaymentTime = 0;
    private static final long PAYMENT_COOLDOWN = 30000; // 30秒内不重复提示同一笔支付

    public static void setReactContext(ReactApplicationContext context) {
        reactContext = context;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        try {
            final int eventType = event.getEventType();
            String packageName = event.getPackageName() != null ? event.getPackageName().toString() : "";
            
            // 增强日志输出
            Log.d(TAG, "收到无障碍事件: 类型=" + eventType + ", 包名=" + packageName + ", 事件文本=" + 
                  (event.getText() != null && !event.getText().isEmpty() ? event.getText().toString() : "无"));
            
            // 只处理支付宝和微信的事件
            if (!packageName.equals("com.tencent.mm") && !packageName.equals("com.eg.android.AlipayGphone")) {
                return;
            }

            // 处理窗口状态变化事件
            if (eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED || 
                eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED) {
                
                AccessibilityNodeInfo rootNode = getRootInActiveWindow();
                if (rootNode == null) {
                    Log.d(TAG, "无法获取根节点");
                    return;
                }

                // 检测支付宝支付成功页面
                if (packageName.equals("com.eg.android.AlipayGphone")) {
                    Log.d(TAG, "检测支付宝页面");
                    detectAlipayPayment(rootNode);
                }
                
                // 检测微信支付成功页面
                else if (packageName.equals("com.tencent.mm")) {
                    Log.d(TAG, "检测微信页面");
                    detectWechatPayment(rootNode);
                }
                
                rootNode.recycle();
            }
        } catch (Exception e) {
            Log.e(TAG, "处理无障碍事件时出错: " + e.getMessage(), e);
        }
    }

    private void detectAlipayPayment(AccessibilityNodeInfo rootNode) {
        try {
            // 打印所有文本节点，帮助分析界面结构
            Log.d(TAG, "开始分析支付宝界面结构");
            printAllTextNodesWithDepth(rootNode, "支付宝", 0);
            
            // 尝试多种方式检测支付成功页面
            
            // 方式1: 检测特定文本组合
            boolean hasTradeSuccess = false;
            boolean hasOrderAmount = false;
            boolean hasPayTime = false;
            boolean hasPayMethod = false;
            String amount = "";
            
            // 遍历所有节点，查找特定文本
            List<AccessibilityNodeInfo> allNodes = new ArrayList<>();
            getAllNodes(rootNode, allNodes);
            
            Log.d(TAG, "支付宝界面节点总数: " + allNodes.size());
            
            for (AccessibilityNodeInfo node : allNodes) {
                if (node.getText() != null) {
                    String text = node.getText().toString().trim();
                    Log.d(TAG, "支付宝节点文本: " + text);
                    
                    // 检测交易成功
                    if (text.contains("交易成功") || text.contains("支付成功") || text.contains("付款成功") || text.contains("转账")) {
                        hasTradeSuccess = true;
                        Log.d(TAG, "找到交易成功节点: " + text);
                    }
                    
                    // 检测订单金额
                    if (text.contains("订单金额") || text.contains("付款金额")) {
                        hasOrderAmount = true;
                        Log.d(TAG, "找到订单金额节点: " + text);
                    }
                    
                    // 检测支付时间
                    if (text.contains("支付时间") || text.contains("付款时间")) {
                        hasPayTime = true;
                        Log.d(TAG, "找到支付时间节点: " + text);
                    }
                    
                    // 检测付款方式
                    if (text.contains("付款方式") || text.contains("支付方式")) {
                        hasPayMethod = true;
                        Log.d(TAG, "找到付款方式节点: " + text);
                    }
                    
                    // 尝试提取金额 - 支持多种格式
                    if (text.matches("\\d+\\.\\d{2}") || text.matches("￥\\s*\\d+\\.\\d{2}") || text.matches("¥\\s*\\d+\\.\\d{2}")) {
                        // 提取数字部分
                        Pattern pattern = Pattern.compile("(\\d+\\.\\d{2})");
                        Matcher matcher = pattern.matcher(text);
                        if (matcher.find()) {
                            amount = matcher.group(1);
                            Log.d(TAG, "找到可能的金额: " + amount);
                        }
                    }
                }
            }
            
            // 方式2: 直接查找包含"成功"的节点
            List<AccessibilityNodeInfo> successNodes = rootNode.findAccessibilityNodeInfosByText("成功");
            boolean hasSuccessNode = !successNodes.isEmpty();
            Log.d(TAG, "成功节点数量: " + successNodes.size());
            
            // 如果同时存在交易成功、订单金额、支付时间和付款方式中的至少两个，则认为是支付成功页面
            int featureCount = 0;
            if (hasTradeSuccess) featureCount++;
            if (hasOrderAmount) featureCount++;
            if (hasPayTime) featureCount++;
            if (hasPayMethod) featureCount++;
            
            boolean isSuccessPage = featureCount >= 2 || hasSuccessNode;
            
            Log.d(TAG, "支付宝检测结果: 交易成功=" + hasTradeSuccess + 
                  ", 订单金额=" + hasOrderAmount +
                  ", 支付时间=" + hasPayTime +
                  ", 付款方式=" + hasPayMethod +
                  ", 成功节点=" + hasSuccessNode +
                  ", 特征计数=" + featureCount +
                  ", 是否成功页面=" + isSuccessPage +
                  ", 金额=" + amount);
            
            if (isSuccessPage && !amount.isEmpty()) {                
                Log.d(TAG, "检测到支付宝交易成功页面，金额: " + amount);
                // 发送支付事件
                sendPaymentEvent("alipay", amount);
                // 显示悬浮窗，询问是否记账
                showPaymentPrompt("alipay", amount);
            }
        } catch (Exception e) {
            Log.e(TAG, "检测支付宝支付时出错: " + e.getMessage(), e);
        }
    }

    // 获取所有节点
    private void getAllNodes(AccessibilityNodeInfo node, List<AccessibilityNodeInfo> result) {
        if (node == null) return;
        
        result.add(node);
        
        for (int i = 0; i < node.getChildCount(); i++) {
            getAllNodes(node.getChild(i), result);
        }
    }

    // 打印所有文本节点，帮助调试
    private void printAllTextNodes(AccessibilityNodeInfo node, String appName) {
        printAllTextNodesWithDepth(node, appName, 0);
    }

    private void printAllTextNodesWithDepth(AccessibilityNodeInfo node, String appName, int depth) {
        // 限制递归深度，防止栈溢出
        if (node == null || depth > 10) return;
        
        try {
            if (node.getText() != null && !node.getText().toString().isEmpty()) {
                StringBuilder indent = new StringBuilder();
                for (int i = 0; i < depth; i++) {
                    indent.append("  ");
                }
                Log.d(TAG, appName + " 文本节点: " + indent + node.getText() + 
                      " [类名=" + node.getClassName() + "]");
            }
            
            for (int i = 0; i < node.getChildCount(); i++) {
                AccessibilityNodeInfo child = node.getChild(i);
                if (child != null) {
                    printAllTextNodesWithDepth(child, appName, depth + 1);
                    child.recycle();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "打印文本节点时出错: " + e.getMessage());
        }
    }

    // 打开我们的应用
    private void openOurApp() {
        try {
            Intent launchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(launchIntent);
                Log.d(TAG, "已发送打开应用的意图");
            } else {
                Log.e(TAG, "无法获取应用启动意图");
            }
        } catch (Exception e) {
            Log.e(TAG, "打开应用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void detectWechatPayment(AccessibilityNodeInfo rootNode) {
        try {
            // 打印所有文本节点，帮助分析界面结构
            Log.d(TAG, "开始分析微信界面结构");
            printAllTextNodesWithDepth(rootNode, "微信", 0);
            
            // 尝试多种方式检测支付成功页面
            
            // 方式1: 检测特定文本组合
            boolean hasPaySuccess = false;
            boolean hasAmount = false;
            boolean hasDoneButton = false;
            boolean hasPaymentInfo = false;
            String amount = "";
            String totalText = "";
            
            // 获取所有节点
            List<AccessibilityNodeInfo> allNodes = new ArrayList<>();
            getAllNodes(rootNode, allNodes);
            
            Log.d(TAG, "微信界面节点总数: " + allNodes.size());
            
            // 遍历所有节点，查找特定文本
            for (AccessibilityNodeInfo node : allNodes) {
                if (node.getText() != null) {
                    String text = node.getText().toString().trim();
                    Log.d(TAG, "微信节点文本: " + text);
                    
                    // 检测支付成功
                    if (text.contains("支付成功") || text.contains("付款成功") || text.contains("转账成功")) {
                        hasPaySuccess = true;
                        Log.d(TAG, "找到支付成功节点: " + text);
                    }
                    
                    // 检测完成按钮
                    if (text.equals("完成")) {
                        hasDoneButton = true;
                        Log.d(TAG, "找到完成按钮节点: " + text);
                    }
                    
                    // 检测支付信息
                    if (text.contains("收款方") || text.contains("收款时间") || text.contains("交易单号")) {
                        hasPaymentInfo = true;
                        Log.d(TAG, "找到支付信息节点: " + text);
                    }
                    
                    // 尝试提取金额 - 支持多种格式
                    if (text.contains("¥") || text.contains("￥") || 
                        text.matches(".*\\d+\\.\\d{2}.*") || 
                        text.contains("元")) {
                        
                        String extractedAmount = extractAmount(text);
                        if (!extractedAmount.isEmpty()) {
                            amount = extractedAmount;
                            hasAmount = true;
                            Log.d(TAG, "从文本提取到金额: " + amount + ", 原文本: " + text);
                        }
                    }
                }
            }
            
            // 方式2: 直接查找包含"支付成功"的节点
            List<AccessibilityNodeInfo> successNodes = rootNode.findAccessibilityNodeInfosByText("支付成功");
            boolean hasSuccessNode = !successNodes.isEmpty();
            Log.d(TAG, "支付成功节点数量: " + successNodes.size());
            
            // 如果同时存在支付成功、金额、完成按钮或支付信息中的至少两个，则认为是支付成功页面
            int featureCount = 0;
            if (hasPaySuccess) featureCount++;
            if (hasAmount) featureCount++;
            if (hasDoneButton) featureCount++;
            if (hasPaymentInfo) featureCount++;
            
            boolean isSuccessPage = featureCount >= 2 || hasSuccessNode;
            
            Log.d(TAG, "微信检测结果: 支付成功=" + hasPaySuccess + 
                  ", 金额=" + hasAmount +
                  ", 完成按钮=" + hasDoneButton +
                  ", 支付信息=" + hasPaymentInfo +
                  ", 成功节点=" + hasSuccessNode +
                  ", 特征计数=" + featureCount +
                  ", 是否成功页面=" + isSuccessPage +
                  ", 金额=" + amount);
            
            if (isSuccessPage && !amount.isEmpty()) {                
                Log.d(TAG, "检测到微信支付成功页面，金额: " + amount);
                // 发送支付事件
                sendPaymentEvent("wechat", amount);
                // 显示悬浮窗，询问是否记账
                showPaymentPrompt("wechat", amount);
            } else {
                Log.d(TAG, "未检测到微信支付成功页面或金额为空");
            }
        } catch (Exception e) {
            Log.e(TAG, "检测微信支付时出错: " + e.getMessage(), e);
        }
    }

    // 辅助方法：从文本中提取金额
    private String extractAmount(String text) {
        // 尝试多种格式匹配
        
        // 1. 匹配 "支出14.70元" 格式
        Pattern supportPattern = Pattern.compile("支出(\\d+\\.\\d{2})元");
        Matcher supportMatcher = supportPattern.matcher(text);
        if (supportMatcher.find()) {
            return supportMatcher.group(1);
        }
        
        // 2. 匹配 ¥123.45 或 123.45元 或 ￥123.45 等格式
        Pattern pattern = Pattern.compile("(¥|￥)?\\s*(\\d+(\\.\\d{2})?)\\s*(元)?");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(2);
        }
        
        // 3. 匹配纯数字金额格式 (如 14.70)
        Pattern numberPattern = Pattern.compile("(\\d+\\.\\d{2})");
        Matcher numberMatcher = numberPattern.matcher(text);
        if (numberMatcher.find()) {
            return numberMatcher.group(1);
        }
        
        return "";
    }

    private void showPaymentPrompt(String platform, String amount) {
        // 检查是否是重复的支付提示
        long currentTime = System.currentTimeMillis();
        if (platform.equals(lastPaymentPlatform) && 
            amount.equals(lastPaymentAmount) && 
            (currentTime - lastPaymentTime) < PAYMENT_COOLDOWN) {
            
            Log.d(TAG, "忽略重复的支付提示: " + platform + " - " + amount + 
                  ", 距上次提示: " + (currentTime - lastPaymentTime) + "ms");
            return;
        }
        
        // 更新最近处理的支付信息
        lastPaymentPlatform = platform;
        lastPaymentAmount = amount;
        lastPaymentTime = currentTime;
        
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                Log.d(TAG, "准备显示悬浮窗: " + platform + " - " + amount);
                
                if (isOverlayShowing) {
                    // 如果已经显示了悬浮窗，不要重复显示
                    Log.d(TAG, "悬浮窗已经显示，不重复显示");
                    return;
                }
                
                // 检查是否有悬浮窗权限
                if (!Settings.canDrawOverlays(this)) {
                    Log.e(TAG, "没有悬浮窗权限，无法显示提示");
                    // 如果没有悬浮窗权限，直接发送事件并返回
                    sendPaymentEvent(platform, amount);
                    return;
                }
                
                // 创建悬浮窗视图
                LayoutInflater inflater = LayoutInflater.from(this);
                overlayView = inflater.inflate(R.layout.payment_prompt_overlay, null);
                
                if (overlayView == null) {
                    Log.e(TAG, "无法创建悬浮窗视图");
                    return;
                }
                
                // 设置文本
                TextView titleText = overlayView.findViewById(R.id.title_text);
                TextView amountText = overlayView.findViewById(R.id.amount_text);
                Button cancelButton = overlayView.findViewById(R.id.cancel_button);
                Button confirmButton = overlayView.findViewById(R.id.confirm_button);
                ImageView platformIcon = overlayView.findViewById(R.id.platform_icon);
                Button closeButton = overlayView.findViewById(R.id.close_button);
                
                if (titleText == null || amountText == null || cancelButton == null || confirmButton == null || platformIcon == null || closeButton == null) {
                    Log.e(TAG, "无法获取悬浮窗视图中的控件");
                    return;
                }
                
                if ("alipay".equals(platform)) {
                    platformIcon.setImageResource(R.drawable.ic_alipay);
                    titleText.setText("检测到支付宝支付");
                } else if ("wechat".equals(platform)) {
                    platformIcon.setImageResource(R.drawable.ic_wechat);
                    titleText.setText("检测到微信支付");
                } else {
                    platformIcon.setImageResource(R.drawable.ic_payment_default);
                    titleText.setText("检测到支付");
                }
                
                // 设置金额
                amountText.setText("¥ " + amount);
                
                // 设置按钮点击事件
                confirmButton.setOnClickListener(v -> {
                    try {
                        // 关闭悬浮窗
                        hideOverlay();
                        
                        // 打开我们的应用并传递参数
                        Intent launchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
                        if (launchIntent != null) {
                            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                            launchIntent.putExtra("openAddTransaction", true);
                            launchIntent.putExtra("transactionType", "expense");
                            launchIntent.putExtra("transactionAmount", amount);
                            launchIntent.putExtra("transactionPlatform", platform);
                            launchIntent.putExtra("autoDetected", true);
                            
                            Log.d(TAG, "准备启动应用并传递参数: " + platform + " - " + amount);
                            startActivity(launchIntent);
                            Log.d(TAG, "已启动应用并传递参数");
                        } else {
                            Log.e(TAG, "无法获取启动Intent");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "启动应用时出错: " + e.getMessage(), e);
                    }
                });
                
                cancelButton.setOnClickListener(v -> {
                    // 关闭悬浮窗
                    hideOverlay();
                });
                
                closeButton.setOnClickListener(v -> {
                    // 关闭按钮的处理逻辑
                    // 可以和 cancelButton 一样的处理
                    hideOverlay();
                });
                
                // 设置悬浮窗参数 - 使用更小的尺寸和更好的位置
                WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT);
                
                // 放在屏幕顶部中间
                params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
                params.y = 100; // 距离顶部100像素
                
                // 显示悬浮窗
                windowManager.addView(overlayView, params);
                isOverlayShowing = true;
                
                Log.d(TAG, "悬浮窗已显示");
                
                // 设置自动关闭定时器（8秒后自动关闭）
                new Handler().postDelayed(this::hideOverlay, 8000);
                
            } catch (Exception e) {
                Log.e(TAG, "显示悬浮窗失败: " + e.getMessage(), e);
                e.printStackTrace();
                
                // 如果显示悬浮窗失败，仍然发送支付事件
                sendPaymentEvent(platform, amount);
            }
        });
    }
    
    private void hideOverlay() {
        if (overlayView != null && isOverlayShowing) {
            try {
                windowManager.removeView(overlayView);
            } catch (Exception e) {
                Log.e(TAG, "移除悬浮窗失败: " + e.getMessage());
            }
            overlayView = null;
            isOverlayShowing = false;
        }
    }

    private void sendPaymentEvent(String platform, String amount) {
        try {
            ReactApplicationContext reactContext = PaymentDetectionModule.getReactContext();
            
            if (reactContext != null && reactContext.hasActiveReactInstance()) {
                Log.d(TAG, "准备发送支付事件: " + platform + " - " + amount);
                
                WritableMap params = Arguments.createMap();
                params.putString("platform", platform);
                params.putString("amount", amount);
                
                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("PaymentSuccess", params);
                
                Log.d(TAG, "支付事件已发送");
            } else {
                Log.e(TAG, "无法发送支付事件: ReactContext 为空或无活动实例");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送支付事件异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onInterrupt() {
        Log.d(TAG, "Accessibility Service interrupted");
        hideOverlay();
    }

    @Override
    public void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "无障碍服务已连接 - 版本 1.0");
        
        // 添加配置
        AccessibilityServiceInfo info = new AccessibilityServiceInfo();
        info.eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED | 
                          AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED;
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC;
        info.notificationTimeout = 100;
        info.packageNames = new String[]{"com.tencent.mm", "com.eg.android.AlipayGphone"};
        info.flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS;
        
        this.setServiceInfo(info);
        Log.d(TAG, "无障碍服务配置已设置");
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        hideOverlay();
    }

    private void findPossibleAmountNodes(AccessibilityNodeInfo node, List<AccessibilityNodeInfo> result) {
        if (node == null) return;
        
        if (node.getText() != null) {
            String text = node.getText().toString();
            // 检查文本是否可能包含金额
            if (text.contains("¥") || text.contains("￥") || 
                text.matches(".*\\d+\\.\\d{2}.*") || 
                text.matches(".*\\d+元.*")) {
                result.add(node);
            }
        }
        
        for (int i = 0; i < node.getChildCount(); i++) {
            findPossibleAmountNodes(node.getChild(i), result);
        }
    }
}