package com.snow;

import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;
import android.os.Bundle;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;

public class PaymentDetectionModule extends ReactContextBaseJavaModule {
    private static final String TAG = "PaymentDetection";
    private final ReactApplicationContext reactContext;
    
    // 添加静态变量来存储 ReactContext
    private static ReactApplicationContext staticReactContext;
    
    // 定义常量
    private static final String ENABLED_ACCESSIBILITY_SERVICES = "enabled_accessibility_services";
    private static final String ENABLED_NOTIFICATION_LISTENERS = "enabled_notification_listeners";
    
    // 使用字符串常量代替直接引用类
    private static final String ACCESSIBILITY_SERVICE_CLASS = "com.snow.PaymentAccessibilityService";
    private static final String NOTIFICATION_SERVICE_CLASS = "com.snow.PaymentNotificationService";

    public PaymentDetectionModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        // 将 reactContext 保存到静态变量中
        staticReactContext = reactContext;
    }

    @Override
    public String getName() {
        return "PaymentDetection";
    }

    @ReactMethod
    public void checkAccessibilityPermission(Promise promise) {
        try {
            boolean enabled = isAccessibilityServiceEnabled();
            Log.d(TAG, "无障碍服务是否启用: " + enabled);
            promise.resolve(enabled);
        } catch (Exception e) {
            Log.e(TAG, "检查无障碍权限失败: " + e.getMessage(), e);
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void checkNotificationPermission(Promise promise) {
        try {
            boolean enabled = isNotificationListenerEnabled();
            Log.d(TAG, "通知监听服务是否启用: " + enabled);
            promise.resolve(enabled);
        } catch (Exception e) {
            Log.e(TAG, "检查通知权限失败: " + e.getMessage(), e);
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void openAccessibilitySettings() {
        Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        reactContext.startActivity(intent);
    }

    @ReactMethod
    public void openNotificationSettings() {
        Intent intent = new Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        reactContext.startActivity(intent);
    }

    @ReactMethod
    public void checkOverlayPermission(Promise promise) {
        try {
            boolean enabled = Settings.canDrawOverlays(reactContext);
            promise.resolve(enabled);
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void openOverlaySettings() {
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        reactContext.startActivity(intent);
    }

    @ReactMethod
    public void simulatePaymentSuccess(String platform, String amount) {
        try {
            Log.d("PaymentDetection", "模拟支付成功: " + platform + " - " + amount);

            WritableMap params = Arguments.createMap();
            params.putString("platform", platform);
            params.putString("amount", amount);

            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("PaymentSuccess", params);

            Log.d("PaymentDetection", "模拟支付事件已发送");
        } catch (Exception e) {
            Log.e("PaymentDetection", "模拟支付事件失败: " + e.getMessage());
        }
    }

    @ReactMethod
    public void testPaymentDetection(String testType, Promise promise) {
        try {
            switch (testType) {
                case "alipay":
                    // 模拟支付宝支付成功
                    simulatePaymentSuccess("alipay", "12.34");
                    promise.resolve("支付宝测试完成");
                    break;
                case "wechat":
                    // 模拟微信支付成功
                    simulatePaymentSuccess("wechat", "56.78");
                    promise.resolve("微信测试完成");
                    break;
                case "permissions":
                    // 检查所有权限状态
                    WritableMap permissionStatus = Arguments.createMap();
                    permissionStatus.putBoolean("accessibility", isAccessibilityServiceEnabled());
                    permissionStatus.putBoolean("notification", isNotificationListenerEnabled());
                    permissionStatus.putBoolean("overlay", Settings.canDrawOverlays(reactContext));
                    promise.resolve(permissionStatus);
                    break;
                default:
                    promise.reject("INVALID_TEST_TYPE", "不支持的测试类型: " + testType);
                    break;
            }
        } catch (Exception e) {
            promise.reject("TEST_ERROR", "测试失败: " + e.getMessage());
        }
    }

    @ReactMethod
    public void getInitialProps(Promise promise) {
        try {
            MainActivity activity = (MainActivity) getCurrentActivity();
            if (activity != null) {
                Bundle props = activity.getInitialProps();
                if (props != null) {
                    WritableMap map = Arguments.createMap();
                    try {
                        map.putBoolean("openAddTransaction", props.getBoolean("openAddTransaction", false));
                        map.putString("transactionType", props.getString("transactionType", "expense"));
                        map.putString("transactionAmount", props.getString("transactionAmount", "0.00"));
                        map.putString("transactionPlatform", props.getString("transactionPlatform", "alipay"));
                        map.putBoolean("autoDetected", props.getBoolean("autoDetected", false));
                        
                        Log.d(TAG, "成功获取并转换启动参数");
                        promise.resolve(map);
                        
                        // 使用后清除，避免重复使用
                        props.clear();
                    } catch (Exception e) {
                        Log.e(TAG, "转换启动参数时出错: " + e.getMessage(), e);
                        promise.resolve(null);
                    }
                } else {
                    Log.d(TAG, "没有启动参数");
                    promise.resolve(null);
                }
            } else {
                Log.d(TAG, "无法获取Activity");
                promise.resolve(null);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取启动参数失败: " + e.getMessage(), e);
            promise.reject("ERROR", "获取启动参数失败: " + e.getMessage());
        }
    }

    public static void notifyNewIntent(com.facebook.react.bridge.ReactContext context) {
        if (context != null) {
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("NewIntent", null);
        }
    }

    @ReactMethod
    public void startListening() {
        Log.d(TAG, "开始监听支付事件");
        // 这里不需要做什么，因为服务是由系统管理的
        // 只要服务已经在系统设置中启用，它就会自动运行
        
        // 但我们可以保存 ReactContext 以便服务使用
        PaymentAccessibilityService.setReactContext(reactContext);
        PaymentNotificationService.setReactContext(reactContext);
        
        Log.d(TAG, "已设置 ReactContext 到服务");
    }

    private boolean isAccessibilityServiceEnabled() {
        String serviceName = reactContext.getPackageName() + "/" + ACCESSIBILITY_SERVICE_CLASS;
        String enabledServices = Settings.Secure.getString(
                reactContext.getContentResolver(),
                ENABLED_ACCESSIBILITY_SERVICES);
        
        Log.d(TAG, "已启用的无障碍服务: " + enabledServices);
        Log.d(TAG, "我们的无障碍服务名称: " + serviceName);
        
        return enabledServices != null && enabledServices.contains(serviceName);
    }

    private boolean isNotificationListenerEnabled() {
        String serviceName = reactContext.getPackageName() + "/" + NOTIFICATION_SERVICE_CLASS;
        String enabledServices = Settings.Secure.getString(
                reactContext.getContentResolver(),
                ENABLED_NOTIFICATION_LISTENERS);
        
        Log.d(TAG, "已启用的通知监听服务: " + enabledServices);
        Log.d(TAG, "我们的通知监听服务名称: " + serviceName);
        
        return enabledServices != null && enabledServices.contains(serviceName);
    }
    
    // 修改静态方法，返回静态变量
    public static ReactApplicationContext getReactContext() {
        return staticReactContext;
    }
}