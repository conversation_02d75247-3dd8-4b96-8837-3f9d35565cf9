package com.snow

import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.bridge.ReactContext
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {
    private val TAG = "MainActivity"
    private var initialProps: Bundle? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(null) // 使用null而不是savedInstanceState，避免状态恢复问题
        
        try {
            // 获取启动 Intent
            val intent = intent
            if (intent != null && intent.extras != null) {
                // 检查是否有从支付服务传递过来的参数
                if (intent.hasExtra("openAddTransaction")) {
                    Log.d(TAG, "检测到从支付服务传递的参数")
                    
                    initialProps = Bundle()
                    initialProps?.putBoolean("openAddTransaction", intent.getBooleanExtra("openAddTransaction", false))
                    initialProps?.putString("transactionType", intent.getStringExtra("transactionType") ?: "expense")
                    initialProps?.putString("transactionAmount", intent.getStringExtra("transactionAmount") ?: "0.00")
                    initialProps?.putString("transactionPlatform", intent.getStringExtra("transactionPlatform") ?: "alipay")
                    initialProps?.putBoolean("autoDetected", intent.getBooleanExtra("autoDetected", false))
                    
                    Log.d(TAG, "参数已保存: ${initialProps.toString()}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理启动参数时出错: ${e.message}", e)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        
        try {
            // 处理新的 Intent
            if (intent.extras != null) {
                // 检查是否有从支付服务传递过来的参数
                if (intent.hasExtra("openAddTransaction")) {
                    Log.d(TAG, "检测到从支付服务传递的新参数")
                    
                    initialProps = Bundle()
                    initialProps?.putBoolean("openAddTransaction", intent.getBooleanExtra("openAddTransaction", false))
                    initialProps?.putString("transactionType", intent.getStringExtra("transactionType") ?: "expense")
                    initialProps?.putString("transactionAmount", intent.getStringExtra("transactionAmount") ?: "0.00")
                    initialProps?.putString("transactionPlatform", intent.getStringExtra("transactionPlatform") ?: "alipay")
                    initialProps?.putBoolean("autoDetected", intent.getBooleanExtra("autoDetected", false))
                    
                    Log.d(TAG, "新参数已保存: ${initialProps.toString()}")
                    
                    // 通知 JS 有新的参数
                    try {
                        reactInstanceManager?.currentReactContext?.let { context ->
                            PaymentDetectionModule.notifyNewIntent(context as ReactContext)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "通知JS新参数时出错: ${e.message}", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理新Intent时出错: ${e.message}", e)
        }
    }

    /**
     * Returns the name of the main component registered from JavaScript. This is used to schedule
     * rendering of the component.
     */
    override fun getMainComponentName(): String {
        return "snow"
    }

    /**
     * Returns the instance of the {@link ReactActivityDelegate}. Here we use a util class {@link
     * DefaultReactActivityDelegate} which allows you to easily enable Fabric and Concurrent React
     * (aka React 18) with two boolean flags.
     */
    override fun createReactActivityDelegate(): ReactActivityDelegate {
        return DefaultReactActivityDelegate(
            this,
            mainComponentName,
            // If you opted-in for the New Architecture, we enable the Fabric Renderer.
            DefaultNewArchitectureEntryPoint.fabricEnabled
        )
    }
    
    // 添加一个方法来获取初始参数
    fun getInitialProps(): Bundle? {
        return initialProps
    }
}
