package com.snow;

import android.app.Notification;
import android.os.Bundle;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PaymentNotificationService extends NotificationListenerService {
    private static final String TAG = "PaymentNotification";
    private static ReactApplicationContext reactContext;

    // 添加重复检测机制
    private String lastPaymentPlatform = "";
    private String lastPaymentAmount = "";
    private long lastPaymentTime = 0;
    private static final long PAYMENT_COOLDOWN = 60000; // 60秒内不重复提示同一笔支付

    public static void setReactContext(ReactApplicationContext context) {
        reactContext = context;
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        try {
            String packageName = sbn.getPackageName();
            
            // 只处理支付宝和微信的通知
            if (!packageName.equals("com.tencent.mm") && !packageName.equals("com.eg.android.AlipayGphone")) {
                return;
            }

            Log.d(TAG, "收到通知: 包名=" + packageName);

            Notification notification = sbn.getNotification();
            Bundle extras = notification.extras;
            
            if (extras != null) {
                String title = extras.getString(Notification.EXTRA_TITLE, "");
                String text = extras.getString(Notification.EXTRA_TEXT, "");
                
                Log.d(TAG, "通知内容: 标题=" + title + ", 文本=" + text);
                
                // 支付宝通知检测 - 改进版
                if (packageName.equals("com.eg.android.AlipayGphone")) {
                    if (isAlipayPaymentNotification(title, text)) {
                        String amount = extractAmountFromNotification(title + " " + text);
                        if (!amount.isEmpty() && !amount.equals("0.00")) {
                            Log.d(TAG, "检测到支付宝支付通知: 金额=" + amount);
                            sendPaymentEvent("alipay", amount);
                        } else {
                            Log.d(TAG, "支付宝通知金额提取失败或为0: title=" + title + ", text=" + text);
                        }
                    }
                }
                // 微信通知检测 - 改进版
                else if (packageName.equals("com.tencent.mm")) {
                    if (isWechatPaymentNotification(title, text)) {
                        String amount = extractAmountFromNotification(title + " " + text);
                        if (!amount.isEmpty() && !amount.equals("0.00")) {
                            Log.d(TAG, "检测到微信支付通知: 金额=" + amount);
                            sendPaymentEvent("wechat", amount);
                        } else {
                            Log.d(TAG, "微信通知金额提取失败或为0: title=" + title + ", text=" + text);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "处理通知时出错: " + e.getMessage());
        }
    }

    // 改进的支付宝通知检测
    private boolean isAlipayPaymentNotification(String title, String text) {
        // 精确匹配支付成功通知
        if (title.contains("支付宝") && (
            text.contains("支付成功") ||
            text.contains("付款成功") ||
            text.contains("交易成功"))) {
            return true;
        }

        // 检查是否包含明确的支付金额信息
        if ((title.contains("支付宝") || title.contains("付款")) &&
            (text.contains("￥") || text.contains("¥") || text.contains("元"))) {
            // 进一步验证是否真的是支付通知
            return text.contains("成功") || text.contains("完成") || text.contains("已付");
        }

        return false;
    }

    // 改进的微信通知检测
    private boolean isWechatPaymentNotification(String title, String text) {
        // 精确匹配微信支付成功通知
        if ((title.contains("微信支付") || title.contains("微信钱包")) && (
            text.contains("支付成功") ||
            text.contains("付款成功") ||
            text.contains("交易成功"))) {
            return true;
        }

        // 检查是否包含明确的支付金额信息
        if (title.contains("微信") &&
            (text.contains("￥") || text.contains("¥") || text.contains("元"))) {
            // 进一步验证是否真的是支付通知
            return text.contains("成功") || text.contains("完成") || text.contains("已付");
        }

        return false;
    }

    // 改进的金额提取方法
    private String extractAmountFromNotification(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 优先匹配带上下文的金额
        String[] contextPatterns = {
            "支付金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "付款金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "交易金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "金额[：:]?\\s*[￥¥]?(\\d+(?:\\.\\d{2})?)",
            "[￥¥]\\s*(\\d+(?:\\.\\d{2})?)",
            "(\\d+\\.\\d{2})元"
        };

        for (String patternStr : contextPatterns) {
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                String amount = matcher.group(1);
                if (isValidNotificationAmount(amount)) {
                    return amount;
                }
            }
        }

        return "";
    }

    // 验证通知中的金额是否合理
    private boolean isValidNotificationAmount(String amount) {
        try {
            double value = Double.parseDouble(amount);
            // 通知中的金额应该在合理范围内
            return value >= 0.01 && value <= 100000.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // 保留原有方法作为兼容
    private String extractAmount(String text) {
        return extractAmountFromNotification(text);
    }

    // 检查是否是重复的支付通知
    private boolean isDuplicatePaymentNotification(String platform, String amount) {
        long currentTime = System.currentTimeMillis();

        if (platform.equals(lastPaymentPlatform) &&
            amount.equals(lastPaymentAmount) &&
            (currentTime - lastPaymentTime) < PAYMENT_COOLDOWN) {

            Log.d(TAG, "忽略重复的支付通知: " + platform + " - " + amount +
                  ", 距上次通知: " + (currentTime - lastPaymentTime) + "ms");
            return true;
        }

        // 更新最近处理的支付信息
        lastPaymentPlatform = platform;
        lastPaymentAmount = amount;
        lastPaymentTime = currentTime;

        return false;
    }

    private void sendPaymentEvent(String platform, String amount) {
        try {
            // 检查重复
            if (isDuplicatePaymentNotification(platform, amount)) {
                return;
            }

            ReactApplicationContext reactContext = PaymentDetectionModule.getReactContext();

            if (reactContext != null && reactContext.hasActiveReactInstance()) {
                Log.d(TAG, "准备发送通知支付事件: " + platform + " - " + amount);

                WritableMap params = Arguments.createMap();
                params.putString("platform", platform);
                params.putString("amount", amount);

                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("PaymentSuccess", params);

                Log.d(TAG, "通知支付事件已发送");
            } else {
                Log.e(TAG, "无法发送通知支付事件: ReactContext 为空或无活动实例");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送通知支付事件异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onListenerConnected() {
        super.onListenerConnected();
        Log.d(TAG, "通知监听服务已连接 - 版本 1.0");
    }
}