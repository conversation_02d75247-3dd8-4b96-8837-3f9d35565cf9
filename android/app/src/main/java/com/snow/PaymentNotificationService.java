package com.snow;

import android.app.Notification;
import android.os.Bundle;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PaymentNotificationService extends NotificationListenerService {
    private static final String TAG = "PaymentNotification";
    private static ReactApplicationContext reactContext;

    public static void setReactContext(ReactApplicationContext context) {
        reactContext = context;
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        try {
            String packageName = sbn.getPackageName();
            
            // 只处理支付宝和微信的通知
            if (!packageName.equals("com.tencent.mm") && !packageName.equals("com.eg.android.AlipayGphone")) {
                return;
            }

            Log.d(TAG, "收到通知: 包名=" + packageName);

            Notification notification = sbn.getNotification();
            Bundle extras = notification.extras;
            
            if (extras != null) {
                String title = extras.getString(Notification.EXTRA_TITLE, "");
                String text = extras.getString(Notification.EXTRA_TEXT, "");
                
                Log.d(TAG, "通知内容: 标题=" + title + ", 文本=" + text);
                
                // 支付宝通知检测
                if (packageName.equals("com.eg.android.AlipayGphone")) {
                    // 更广泛的匹配条件
                    boolean isPaymentNotification = 
                        title.contains("支付宝") || 
                        title.contains("付款") || 
                        title.contains("收款") ||
                        text.contains("支付成功") || 
                        text.contains("付款成功") || 
                        text.contains("交易成功") ||
                        text.contains("已付款") ||
                        text.contains("元") ||
                        text.contains("￥");
                    
                    if (isPaymentNotification) {
                        // 尝试提取金额
                        String amount = extractAmount(text);
                        if (amount.isEmpty()) {
                            amount = "0.00";
                        }
                        
                        Log.d(TAG, "检测到支付宝支付: 金额=" + amount);
                        sendPaymentEvent("alipay", amount);
                    }
                }
                // 微信通知检测
                else if (packageName.equals("com.tencent.mm")) {
                    // 更广泛的匹配条件
                    boolean isPaymentNotification = 
                        title.contains("微信支付") || 
                        title.contains("微信收款") || 
                        title.contains("微信钱包") ||
                        text.contains("支付成功") || 
                        text.contains("付款成功") || 
                        text.contains("交易成功") ||
                        text.contains("已付款") ||
                        text.contains("元") ||
                        text.contains("￥");
                    
                    if (isPaymentNotification) {
                        // 尝试提取金额
                        String amount = extractAmount(text);
                        if (amount.isEmpty()) {
                            amount = "0.00";
                        }
                        
                        Log.d(TAG, "检测到微信支付: 金额=" + amount);
                        sendPaymentEvent("wechat", amount);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "处理通知时出错: " + e.getMessage());
        }
    }

    private String extractAmount(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        
        // 尝试多种正则表达式匹配金额
        String[] patterns = {
            "(\\d+\\.\\d{2})元",
            "￥(\\d+\\.\\d{2})",
            "¥(\\d+\\.\\d{2})",
            "(\\d+\\.\\d{2})\\s*元",
            "金额\\s*[:：]\\s*(\\d+\\.\\d{2})",
            "金额\\s*(\\d+\\.\\d{2})",
            "(\\d+\\.\\d{2})"
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        
        return "";
    }

    private void sendPaymentEvent(String platform, String amount) {
        try {
            ReactApplicationContext reactContext = PaymentDetectionModule.getReactContext();
            
            if (reactContext != null && reactContext.hasActiveReactInstance()) {
                Log.d(TAG, "准备发送通知支付事件: " + platform + " - " + amount);
                
                WritableMap params = Arguments.createMap();
                params.putString("platform", platform);
                params.putString("amount", amount);
                
                reactContext
                    .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("PaymentSuccess", params);
                
                Log.d(TAG, "通知支付事件已发送");
            } else {
                Log.e(TAG, "无法发送通知支付事件: ReactContext 为空或无活动实例");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送通知支付事件异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onListenerConnected() {
        super.onListenerConnected();
        Log.d(TAG, "通知监听服务已连接 - 版本 1.0");
    }
}