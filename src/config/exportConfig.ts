// 导出配置文件
export const EXPORT_CONFIG = {
  // 分批处理配置
  BATCH_SIZE: 1000, // 每批处理的记录数
  
  // 进度更新间隔（毫秒）
  PROGRESS_UPDATE_INTERVAL: 10,
  
  // 内存管理配置
  MAX_MEMORY_USAGE_MB: 50, // 最大内存使用量（MB）
  
  // 文件大小限制
  MAX_FILE_SIZE_MB: 100, // 最大文件大小（MB）
  
  // 性能配置
  ENABLE_PROGRESS_CALLBACK: true, // 是否启用进度回调
  ENABLE_MEMORY_MONITORING: false, // 是否启用内存监控（开发模式）
  
  // 错误处理配置
  MAX_RETRY_ATTEMPTS: 3, // 最大重试次数
  RETRY_DELAY_MS: 1000, // 重试延迟（毫秒）
};

// 根据数据量动态调整批处理大小
export const getOptimalBatchSize = (totalRecords: number): number => {
  if (totalRecords < 1000) {
    return totalRecords; // 小数据量直接处理
  } else if (totalRecords < 10000) {
    return 500; // 中等数据量使用较小批次
  } else if (totalRecords < 50000) {
    return 1000; // 大数据量使用标准批次
  } else {
    return 2000; // 超大数据量使用较大批次
  }
};

// 估算内存使用量（粗略估算）
export const estimateMemoryUsage = (recordCount: number): number => {
  // 假设每条记录平均占用 200 字节
  const BYTES_PER_RECORD = 200;
  return (recordCount * BYTES_PER_RECORD) / (1024 * 1024); // 转换为 MB
};

// 检查是否需要分批处理
export const shouldUseBatchProcessing = (totalRecords: number): boolean => {
  const estimatedMemoryMB = estimateMemoryUsage(totalRecords);
  return estimatedMemoryMB > EXPORT_CONFIG.MAX_MEMORY_USAGE_MB || totalRecords > 5000;
};

// 导出状态枚举
export enum ExportStatus {
  PREPARING = 'preparing',
  PROCESSING = 'processing',
  GENERATING_FILE = 'generating_file',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// 导出进度信息接口
export interface ExportProgress {
  status: ExportStatus;
  currentBatch: number;
  totalBatches: number;
  processedRecords: number;
  totalRecords: number;
  percentage: number;
  message: string;
}

// 生成进度消息
export const generateProgressMessage = (progress: ExportProgress): string => {
  switch (progress.status) {
    case ExportStatus.PREPARING:
      return '正在准备导出数据...';
    case ExportStatus.PROCESSING:
      return `正在导出数据... ${progress.percentage}% (${progress.processedRecords}/${progress.totalRecords})`;
    case ExportStatus.GENERATING_FILE:
      return '正在生成文件...';
    case ExportStatus.COMPLETED:
      return `导出完成，共 ${progress.totalRecords} 条记录`;
    case ExportStatus.FAILED:
      return '导出失败';
    default:
      return '正在处理...';
  }
};
