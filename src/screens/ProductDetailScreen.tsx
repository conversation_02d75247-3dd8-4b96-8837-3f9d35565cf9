import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useIsFocused} from '@react-navigation/native';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import {useToast} from '../context/ToastContext';
import ConfirmDialog from '../components/ConfirmDialog';
import PriceHistoryChart from '../components/PriceHistoryChart';
import { useTheme } from '../context/ThemeContext';

const ProductDetailScreen = ({navigation, route}) => {
  const {colors} = useTheme();
  const {productId} = route.params;
  const [product, setProduct] = useState<any>(null);
  const [prices, setPrices] = useState<any[]>([]);
  const [purchasePrice, setpurchasePrice] = useState('');
  const [loading, setLoading] = useState(true);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );
  const isFocused = useIsFocused();
  const {showToast} = useToast();
  const [purchaseConfirmVisible, setPurchaseConfirmVisible] = useState(false);
  const [pendingPurchasePrice, setPendingPurchasePrice] = useState(null);
  const [showAllPrices, setShowAllPrices] = useState(false);

  useEffect(() => {
    if (isFocused) {
      loadProductData();
    }
  }, [isFocused, productId]);

  const loadProductData = async () => {
    try {
      setLoading(true);
      const productData = await databaseService.getProductById(productId);
      if (productData) {
        // 加载价格数据
        const priceData = await databaseService.getProductPrices(productId);
        console.log('priceData', priceData);
        setProduct({
          ...productData,
          prices: priceData, // 确保将价格数据添加到product对象中
        });
        setPrices(priceData);
        const hasPurchasePrice = priceData.find(
          price => price.isPurchasePrice === true,
        );
        if (hasPurchasePrice) {
          setpurchasePrice(hasPurchasePrice.price);
        }
      } else {
        showToast('无法找到商品信息', 'error');
        navigation.goBack();
      }
    } catch (error) {
      console.error('加载商品数据失败:', error);
      showToast('加载商品数据失败', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleEditProduct = () => {
    navigation.navigate('productEdit', {productId});
  };

  const showDeleteConfirm = () => {
    setConfirmDialogMessage(`确定要删除"${product?.name}"吗？此操作无法撤销。`);
    setConfirmDialogAction(() => () => handleDeleteProduct());
    setConfirmDialogVisible(true);
  };

  const handleDeleteProduct = async () => {
    try {
      await databaseService.deleteProduct(productId);
      showToast('商品已删除', 'success');
      navigation.goBack();
    } catch (error) {
      console.error('删除商品失败:', error);
      showToast('删除商品失败', 'error');
    }
  };

  const handlePurchaseProduct = async price => {
    try {
      // 直接创建账单，而不是跳转到添加账单页面
      await databaseService.createTransactionFromProduct(
        productId,
        product.name,
        price.price,
        price.id,
      );

      // 更新当前产品状态为已购买
      setProduct(prev => ({
        ...prev,
        purchased: 1,
      }));

      showToast('商品已添加到账单', 'success');
      loadProductData();
    } catch (error) {
      console.error('添加到账单失败:', error);
      showToast('添加到账单失败', 'error');
    }
  };

  const renderPriceItem = price => {
    const createdTime = dayjs(price.createdAt).format('YYYY-MM-DD');
    const isUpdated = price.createdAt !== price.updatedAt;

    return (
      <View key={price.id} style={styles.priceItem}>
        <View style={styles.priceInfo}>
          <Text style={styles.priceValue}>¥{(price.price || 0).toFixed(2)}</Text>
          <View style={styles.priceMetaRow}>
            {price.platform && typeof price.platform === 'string' && price.platform.trim() && (
              <View style={styles.platformTag}>
                <Icon name="cart-shopping" size={10} color="#666666" style={styles.platformIcon} />
                <Text style={styles.platformText}>{price.platform}</Text>
              </View>
            )}
            <Text style={styles.priceTime}>{createdTime}{isUpdated && ' *'}</Text>
          </View>
        </View>

        {!product.purchased && (
          <TouchableOpacity
            style={[styles.purchaseButton, {backgroundColor: colors.primary}]}
            onPress={() => {
              setPendingPurchasePrice(price);
              setPurchaseConfirmVisible(true);
            }}>
            <Icon name="cart-plus" size={12} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 删除图片相关函数

  // 价格概览统计
  const renderPriceOverview = () => {
    if (prices.length === 0) {return null;}

    const priceValues = prices.map(p => p.price || 0).filter(p => p > 0);
    if (priceValues.length === 0) {return null;}

    const minPrice = Math.min(...priceValues);
    const maxPrice = Math.max(...priceValues);
    const avgPrice = priceValues.reduce((sum, price) => sum + price, 0) / priceValues.length;
    const latestPrice = prices[0]?.price || 0; // 假设第一个是最新的

    return (
      <View style={styles.priceOverview}>
        <View style={styles.overviewItem}>
          <Text style={styles.overviewLabel}>最低价</Text>
          <Text style={[styles.overviewValue, styles.minPrice]}>¥{minPrice.toFixed(2)}</Text>
        </View>
        <View style={styles.overviewItem}>
          <Text style={styles.overviewLabel}>最高价</Text>
          <Text style={[styles.overviewValue, styles.maxPrice]}>¥{maxPrice.toFixed(2)}</Text>
        </View>
        <View style={styles.overviewItem}>
          <Text style={styles.overviewLabel}>平均价</Text>
          <Text style={styles.overviewValue}>¥{avgPrice.toFixed(2)}</Text>
        </View>
        <View style={styles.overviewItem}>
          <Text style={styles.overviewLabel}>最新价</Text>
          <Text style={[styles.overviewValue, styles.latestPrice]}>¥{latestPrice.toFixed(2)}</Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <PageContainer headerTitle="商品详情" backgroundColor="#F5F7FA">
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </PageContainer>
    );
  }

  if (!product) {
    return (
      <PageContainer headerTitle="商品详情" backgroundColor="#F5F7FA">
        <View style={styles.errorContainer}>
          <Icon name="exclamation-circle" size={40} color="#FF3B30" />
          <Text style={styles.errorText}>无法找到商品信息</Text>
        </View>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      headerTitle="商品详情"
      backgroundColor="#F5F7FA"
      rightComponent={
        // 只有未购买时才显示编辑按钮
        !product?.purchased ? (
        <TouchableOpacity onPress={handleEditProduct}>
          <Icon name="edit" size={20} color={colors.primary} />
        </TouchableOpacity>
        ) : null
      }
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 优化后的商品信息头部 */}
        <View style={styles.productHeader}>
          {/* 左侧状态指示器 */}
          <View style={[
            styles.statusIndicator,
            product.purchased ? styles.purchasedIndicator : styles.unpurchasedIndicator,
          ]} />

          {/* 商品信息主体 */}
          <View style={styles.headerContent}>
            <View style={styles.titleRow}>
              <Text style={styles.productName}>{product.name || '未命名商品'}</Text>
              <View style={[
                styles.statusBadge,
                product.purchased ? styles.purchasedBadge : styles.unpurchasedBadge,
              ]}>
                <Icon
                  name={product.purchased ? 'check-circle' : 'circle'}
                  size={14}
                  color={product.purchased ? '#4CD964' : colors.primary}
                  style={{marginRight: 4}}
                />
                <Text style={[
                  styles.statusText,
                  product.purchased ? {color: '#4CD964'} : {color: colors.primary},
                ]}>
                  {product.purchased ? '已购买' : '待购买'}
                </Text>
              </View>
            </View>

            {/* 购买价格显示 */}
            {product.purchased && purchasePrice && (typeof purchasePrice === 'number' ? purchasePrice > 0 : parseFloat(purchasePrice) > 0) ? (
              <View style={styles.purchasePriceRow}>
                <Text style={styles.purchasePriceLabel}>购买价格</Text>
                <Text style={styles.purchasePriceValue}>¥{(typeof purchasePrice === 'number' ? purchasePrice : parseFloat(purchasePrice) || 0).toFixed(2)}</Text>
              </View>
            ) : null}

            {/* 创建时间 */}
            <View style={styles.metaRow}>
              <Icon name="calendar-plus" size={12} color="#999999" />
              <Text style={styles.metaText}>
                {dayjs(product.createdAt).format('YYYY年MM月DD日 HH:mm')}
              </Text>
            </View>
          </View>
        </View>

          {/* <View style={styles.infoSection}>
            <Text style={styles.sectionTitle}>日期信息</Text>

            <View style={styles.dateContainer}>
              <View style={styles.dateItem}>
                <Icon name="calendar-plus" size={16} color="#999999" style={styles.dateIcon} />
                <View>
                  <Text style={styles.dateLabel}>创建时间</Text>
                  <Text style={styles.dateValue}>
                    {dayjs(product.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </View>
              </View>

              <View style={styles.dateItem}>
                <Icon name="calendar-check" size={16} color="#999999" style={styles.dateIcon} />
                <View>
                  <Text style={styles.dateLabel}>更新时间</Text>
                  <Text style={styles.dateValue}>
                    {dayjs(product.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </View>
              </View>
            </View>
          </View> */}

        {/* 商品备注 */}
        {product.notes && product.notes.trim() ? (
          <View style={styles.notesCard}>
            <View style={styles.cardHeader}>
              <Icon name="note-sticky" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>备注信息</Text>
            </View>
            <Text style={styles.notesContent}>{product.notes}</Text>
          </View>
        ) : null}

        {/* 价格记录 - 优化布局 */}
        <View style={styles.priceCard}>
          <View style={styles.cardHeader}>
            <Icon name="chart-line" size={16} color={colors.primary} />
            <Text style={styles.cardTitle}>价格记录</Text>
            <View style={styles.priceStats}>
              <Text style={styles.priceStatsText}>{prices.length || 0} 条记录</Text>
            </View>
          </View>

          {prices.length > 0 ? (
            <>
              {/* 价格统计概览 */}
              {renderPriceOverview() || null}

              {/* 价格列表 */}
              <View style={styles.priceList}>
                {(showAllPrices ? prices : prices.slice(0, 3)).map(renderPriceItem)}
              </View>

              {/* 展开/收起按钮 */}
              {prices.length > 3 && (
                <TouchableOpacity
                  style={styles.toggleButton}
                  onPress={() => setShowAllPrices(!showAllPrices)}
                  activeOpacity={0.7}>
                  <Text style={styles.toggleButtonText}>
                    {showAllPrices ? '收起' : `查看全部 ${prices.length} 条记录`}
                  </Text>
                  <Icon
                    name={showAllPrices ? 'chevron-up' : 'chevron-down'}
                    size={14}
                    color="#007AFF"
                    style={styles.toggleIcon}
                  />
                </TouchableOpacity>
              )}
            </>
          ) : (
            <View style={styles.noPricesContainer}>
              <Icon name="tag" size={24} color="#DDDDDD" />
              <Text style={styles.noPricesText}>暂无价格记录</Text>
              <Text style={styles.noPricesHint}>点击编辑按钮添加价格信息</Text>
            </View>
          )}
        </View>

        {/* 价格历史图表 */}
        {prices && prices.length > 0 ? <PriceHistoryChart prices={prices} /> : null}

        {/* 底部操作区 */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={showDeleteConfirm}>
            <Icon
              name="trash"
              size={16}
              color="#FFFFFF"
              style={styles.buttonIcon}
            />
            <Text style={styles.deleteButtonText}>删除商品</Text>
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={[styles.editButton, {backgroundColor: colors.primary}]}
            onPress={handleEditProduct}>
            <Icon
              name="edit"
              size={16}
              color="#FFFFFF"
              style={styles.buttonIcon}
            />
            <Text style={styles.editButtonText}>编辑商品</Text>
          </TouchableOpacity> */}
        </View>
      </ScrollView>

      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onCancel={() => setConfirmDialogVisible(false)}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        cancelText="取消"
        confirmText="确定"
      />

      <ConfirmDialog
        visible={purchaseConfirmVisible}
        title="确认购买"
        message="购买后将无法更改，是否确认购买？"
        onCancel={() => setPurchaseConfirmVisible(false)}
        onConfirm={() => {
          setPurchaseConfirmVisible(false);
          if (pendingPurchasePrice) {
            handlePurchaseProduct(pendingPurchasePrice);
            setPendingPurchasePrice(null);
          }
        }}
        cancelText="取消"
        confirmText="确定"
      />

      {/* 删除图片预览组件 */}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#999999',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
  // 优化后的商品头部样式
  productHeader: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  statusIndicator: {
    width: 4,
    borderRadius: 2,
    marginRight: 16,
  },
  purchasedIndicator: {
    backgroundColor: '#4CD964',
  },
  unpurchasedIndicator: {
    backgroundColor: '#007AFF',
  },
  headerContent: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  purchasePriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  purchasePriceLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  purchasePriceValue: {
    fontSize: 16,
    color: '#4CD964',
    fontWeight: '700',
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  metaText: {
    fontSize: 12,
    color: '#999999',
    marginLeft: 6,
  },
  productName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    flex: 1,
    marginRight: 16,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  purchasedBadge: {
    backgroundColor: '#E8F9E8',
    borderWidth: 1,
    borderColor: '#4CD964',
  },
  unpurchasedBadge: {
    backgroundColor: '#E8F4FF',
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // 备注卡片样式
  notesCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333333',
    marginLeft: 8,
    flex: 1,
  },
  notesContent: {
    fontSize: 15,
    color: '#666666',
    lineHeight: 22,
  },
  // 价格统计样式
  priceStats: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priceStatsText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  priceOverview: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  overviewItem: {
    flex: 1,
    alignItems: 'center',
  },
  overviewLabel: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 4,
  },
  overviewValue: {
    fontSize: 14,
    fontWeight: '700',
    color: '#333333',
  },
  minPrice: {
    color: '#4CD964',
  },
  maxPrice: {
    color: '#FF3B30',
  },
  latestPrice: {
    color: '#007AFF',
  },
  // 信息分段
  infoSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  dateContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateIcon: {
    marginRight: 12,
  },
  dateLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  dateValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  // 备注信息
  notesContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  // 价格卡片
  priceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  priceList: {
    gap: 6,
  },
  priceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    padding: 12,
    borderRadius: 8,
  },
  priceInfo: {
    flex: 1,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  priceMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  platformTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F4FF',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  platformIcon: {
    marginRight: 3,
  },
  platformText: {
    fontSize: 11,
    color: '#007AFF',
    fontWeight: '500',
  },
  priceTime: {
    fontSize: 11,
    color: '#999999',
    fontWeight: '500',
    // marginLeft: 'auto',
  },
  purchaseButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  purchaseButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 6,
  },
  toggleButtonText: {
    fontSize: 13,
    color: '#007AFF',
    fontWeight: '500',
  },
  toggleIcon: {
    marginLeft: 4,
  },
  noPricesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
  },
  noPricesText: {
    marginTop: 10,
    color: '#999999',
    fontSize: 16,
    fontWeight: '600',
  },
  noPricesHint: {
    marginTop: 4,
    color: '#BBBBBB',
    fontSize: 14,
    textAlign: 'center',
  },
  // 底部操作区
  actionContainer: {
    flexDirection: 'row',
    marginBottom: 30,
    gap: 12,
  },
  deleteButton: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#FF3B30',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProductDetailScreen;
