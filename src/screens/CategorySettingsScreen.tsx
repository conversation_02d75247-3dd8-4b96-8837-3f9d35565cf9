import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useFocusEffect} from '@react-navigation/native';
import PageContainer from '../components/PageContainer';
import {useToast} from '../context/ToastContext';
import databaseService, {Category} from '../services/DatabaseService';
import ConfirmDialog from '../components/ConfirmDialog';
import CustomModal from '../components/CustomModal';
import {COLORS} from '../utils/color';
import { useTheme } from '../context/ThemeContext';
const CategorySettingsScreen = ({navigation}) => {
  const {colors} = useTheme();
  const {showToast} = useToast();

  const [categories, setCategories] = useState<Category[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<Category[]>([]);
  const [incomeCategories, setIncomeCategories] = useState<Category[]>([]);
  const [activeTab, setActiveTab] = useState<'expense' | 'income'>('expense');
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [categoryId, setCategoryId] = useState<string | null>(null);

  // 可用的图标列表
  const availableIcons = [
    'utensils',
    'bag-shopping',
    'truck',
    'house',
    'film',
    'hospital',
    'book',
    'plane',
    'mobile-screen',
    'shirt',
    'spa',
    'ellipsis',
    'money-bill',
    'award',
    'chart-line',
    'briefcase',
    'gift',
    'rotate-left',
    'car',
    'gamepad',
    'mug-hot',
    'paw',
    'baby',
    'graduation-cap',
    'dumbbell',
    'music',
    'palette',
    'basketball',
    'heart',
    'wifi',
  ];

  // 添加默认分类列表常量
  const DEFAULT_EXPENSE_CATEGORIES = [
    '餐饮',
    '购物',
    '交通',
    '住房',
    '娱乐',
    '医疗',
    '教育',
    '旅行',
    '通讯',
    '服装',
    '美容',
    '其他',
  ];

  const DEFAULT_INCOME_CATEGORIES = [
    '工资',
    '奖金',
    '理财',
    '兼职',
    '礼金',
    '退款',
    '其他',
  ];

  // 添加 FlatList 引用
  const expenseFlatListRef = useRef<FlatList>(null);
  const incomeFlatListRef = useRef<FlatList>(null);

  // 使用 useFocusEffect 确保每次页面获得焦点时都重新加载数据
  useFocusEffect(
    useCallback(() => {
      console.log('CategorySettingsScreen 获得焦点，重新加载分类数据');
      loadCategories();
      return () => {
        // 可选的清理函数
      };
    }, []), // 空依赖数组，表示只依赖于页面焦点变化
  );

  // 保留原有的 useEffect 以确保组件首次渲染时也加载数据
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    setIsLoading(true);
    try {
      const allCategories = await databaseService.getAllCategories();
      console.log('重新加载分类数据:', allCategories);

      // 过滤掉无效的分类（没有 isExpense 属性的分类）
      const validCategories = allCategories.filter(
        c => c && typeof c.isExpense !== 'undefined',
      );

      setCategories(validCategories);

      // 分离支出和收入分类
      setExpenseCategories(validCategories.filter(c => c.isExpense));
      setIncomeCategories(validCategories.filter(c => !c.isExpense));
    } catch (error) {
      console.error('加载分类失败', error);
      showToast('加载分类失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 添加新分类
  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      showToast('请输入分类名称', 'warning');
      return;
    }
    if (!selectedIcon) {
      showToast('请选择图标', 'warning');
      return;
    }

    try {
      // 检查是否已存在同名分类
      const existingCategory = categories.find(
        c =>
          c.name === newCategoryName.trim() &&
          c.isExpense === (activeTab === 'expense'),
      );

      if (existingCategory) {
        showToast('已存在同名分类', 'warning');
        return;
      }

      // 创建新分类
      const newCategory: Category = {
        name: newCategoryName.trim(),
        isExpense: activeTab === 'expense',
        icon: selectedIcon,
      };

      // 保存到数据库
      await databaseService.addCategory(newCategory);

      // 重新加载分类
      await loadCategories();

      // 重置表单
      setNewCategoryName('');
      setSelectedIcon('');
      setIsAddModalVisible(false);

      // 添加成功后滚动到底部
      setTimeout(() => {
        if (activeTab === 'expense' && expenseFlatListRef.current) {
          expenseFlatListRef.current.scrollToEnd({animated: true});
        } else if (activeTab === 'income' && incomeFlatListRef.current) {
          incomeFlatListRef.current.scrollToEnd({animated: true});
        }
      }, 300); // 延迟一点时间确保列表已更新
    } catch (error) {
      console.error('添加分类失败', error);
      showToast('添加分类失败', 'error');
    }
  };

  // 删除分类
  const handleDeleteCategory = async () => {
    try {
      if (!categoryId) {
        console.error('没有选择要删除的分类');
        showToast('请选择要删除的分类', 'warning');
        setIsDeleteModalVisible(false);
        return;
      }

      // 获取要删除的分类信息
      const categoryToDelete = categories.find(
        c => c && c.id && String(c.id) === String(categoryId),
      );

      if (!categoryToDelete) {
        console.error(`找不到ID为 ${categoryId} 的分类`);
        showToast('分类不存在', 'warning');
        setIsDeleteModalVisible(false);
        return;
      }

      // 检查是否为默认分类
      const isDefaultCategory =
        (categoryToDelete.isExpense &&
          DEFAULT_EXPENSE_CATEGORIES.includes(categoryToDelete.name)) ||
        (!categoryToDelete.isExpense &&
          DEFAULT_INCOME_CATEGORIES.includes(categoryToDelete.name));

      if (isDefaultCategory) {
        showToast('默认分类不能删除', 'warning');
        setIsDeleteModalVisible(false);
        return;
      }

      // 显示加载状态
      setIsLoading(true);

      // 执行删除操作
      await databaseService.deleteCategory(categoryId);

      // 重新从数据库加载分类，确保数据一致性
      await loadCategories();

      showToast('分类已删除', 'success');
      setIsDeleteModalVisible(false);
    } catch (error) {
      console.error('删除分类失败', error);
      showToast('删除分类失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 渲染分类项
  const renderCategoryItem = (item: Category) => {
    // 确保 item 存在且有 isExpense 属性
    if (!item || typeof item.isExpense === 'undefined') {
      console.warn('无效的分类项:', item);
      return null;
    }

    // 判断是否为默认分类
    const isDefaultCategory =
      (item.isExpense && DEFAULT_EXPENSE_CATEGORIES.includes(item.name)) ||
      (!item.isExpense && DEFAULT_INCOME_CATEGORIES.includes(item.name));

    return (
      <View style={styles.categoryItem}>
        <View style={styles.categoryInfo}>
          <View style={[styles.categoryIcon]}>
            <Icon name={item.icon} size={22} color={COLORS.text.gray} />
          </View>
          <View>
            <Text style={styles.categoryName}>{item.name}</Text>
            {isDefaultCategory && (
              <Text style={styles.defaultCategoryTag}>默认分类</Text>
            )}
          </View>
        </View>

        {/* 只有非默认分类才显示删除按钮 */}
        {!isDefaultCategory && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => {
              setIsDeleteModalVisible(true);
              setCategoryId(item.id);
            }}>
            <Icon name="delete-left" size={18} color={COLORS.functional.error} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 渲染图标选择器
  const renderIconPicker = () => {
    return (
      <View style={styles.iconPickerContainer}>
        <Text style={styles.modalLabel}>选择图标</Text>
        <ScrollView
          style={styles.iconScrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.iconGrid}>
            {availableIcons.map(icon => (
              <TouchableOpacity
                key={icon}
                style={[
                  styles.iconItem,
                  selectedIcon === icon && {...styles.selectedIconItem, backgroundColor: colors.primary},
                ]}
                activeOpacity={0.7}
                onPress={() => setSelectedIcon(icon)}>
                <Icon
                  name={icon}
                  size={24}
                  color={
                    selectedIcon === icon
                      ? COLORS.background.white
                      : COLORS.primary
                  }
                />
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  // 自定义左侧按钮
  const leftComponent = (
    <TouchableOpacity onPress={() => navigation.goBack()}>
      <Icon name="chevron-left" size={20} color={colors.primary} />
    </TouchableOpacity>
  );

  // 自定义右侧按钮
  const rightComponent = (
    <TouchableOpacity onPress={() => setIsAddModalVisible(true)}>
      <Icon name="plus" size={24} color={colors.primary} />
    </TouchableOpacity>
  );

  return (
    <PageContainer
      headerTitle="分类设置"
      leftComponent={leftComponent}
      rightComponent={rightComponent}
      backgroundColor={COLORS.secondary}>
      {/* 类型选择器 */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'expense' && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab('expense')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'expense' && styles.activeTabText,
            ]}>
            支出分类
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'income' && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab('income')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'income' && styles.activeTabText,
            ]}>
            收入分类
          </Text>
        </TouchableOpacity>
      </View>

      {/* 分类列表 */}
      {activeTab === 'expense' ? (
        <FlatList
          ref={expenseFlatListRef}
          data={expenseCategories}
          keyExtractor={item =>
            item && item.id ? item.id.toString() : Math.random().toString()
          }
          renderItem={({item}) => (item ? renderCategoryItem(item) : null)}
          contentContainerStyle={styles.categoryListContent}
          style={styles.categoryList}
        />
      ) : (
        <FlatList
          ref={incomeFlatListRef}
          data={incomeCategories}
          keyExtractor={item => item.id?.toString() || Math.random().toString()}
          renderItem={({item}) => renderCategoryItem(item)}
          contentContainerStyle={styles.categoryListContent}
          style={styles.categoryList}
        />
      )}

      {/* 添加分类模态框 */}
      <CustomModal
        visible={isAddModalVisible}
        onClose={() => {
          setIsAddModalVisible(false);
          setNewCategoryName('');
          setSelectedIcon('');
        }}
        animationType="fade"
        position="center"
        avoidKeyboard={true}>
        <View>
          <Text style={styles.modalTitle}>
            添加{activeTab === 'expense' ? '支出' : '收入'}分类
          </Text>

          <Text style={styles.modalLabel}>分类名称</Text>
          <TextInput
            style={styles.modalInput}
            value={newCategoryName}
            onChangeText={setNewCategoryName}
            placeholder="请输入分类名称"
            maxLength={10}
          />

          {renderIconPicker()}

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setIsAddModalVisible(false);
                setNewCategoryName('');
                setSelectedIcon('');
              }}>
              <Text style={styles.modalButtonText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, {backgroundColor: colors.primary}]}
              onPress={handleAddCategory}>
              <Text style={styles.modalButtonTextPrimary}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>

      <ConfirmDialog
        visible={isDeleteModalVisible}
        title="删除分类"
        message="确定要删除分类吗？"
        onCancel={() => setIsDeleteModalVisible(false)}
        onConfirm={() => handleDeleteCategory()}
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  // 标签页样式 - 更圆润的边角和柔和的阴影
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    backgroundColor: COLORS.background.light,
    padding: 4,
    shadowColor: COLORS.shadow.color,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 10,
    marginHorizontal: 2,
  },
  activeTabButton: {
    backgroundColor: COLORS.background.white,
    shadowColor: COLORS.shadow.color,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.gray,
  },
  activeTabText: {
    color: COLORS.primary,
    fontWeight: '600',
  },

  // 分类列表样式 - 增加适当的内边距和间距
  categoryList: {
    flex: 1,
    backgroundColor: COLORS.background.white,
  },
  categoryListContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border.medium,
    marginHorizontal: 2,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    backgroundColor: COLORS.background.light,
  },
  categoryName: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 10,
    borderRadius: 20,
  },

  // 模态框样式 - 符合iOS设计语言
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  modalContent: {
    width: '85%',
    backgroundColor: COLORS.background.white,
    borderRadius: 14,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 24,
    textAlign: 'center',
    color: COLORS.text.primary,
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    color: COLORS.text.primary,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: COLORS.border.medium,
    borderRadius: 10,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 24,
    backgroundColor: COLORS.background.lightGray,
  },

  // 图标选择器样式 - 改进布局和交互效果
  iconPickerContainer: {
    marginBottom: 24,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  iconItem: {
    width: 44,
    height: 44,
    borderRadius: 10,
    backgroundColor: COLORS.background.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
    marginVertical: 6,
    shadowColor: COLORS.shadow.color,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  selectedIconItem: {
    shadowColor: COLORS.primary,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },

  // 按钮样式 - 更符合iOS设计语言
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 6,
    borderRadius: 10,
  },
  modalButtonPrimary: {
    borderRadius: 10,
    shadowColor: COLORS.primary,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  modalButtonText: {
    fontSize: 16,
    color: COLORS.text.gray,
    fontWeight: '500',
  },
  modalButtonTextPrimary: {
    fontSize: 16,
    color: COLORS.background.white,
    fontWeight: '600',
  },
  defaultCategoryTag: {
    fontSize: 12,
    color: COLORS.text.lightGray,
    marginTop: 2,
  },
  iconScrollView: {
    maxHeight: 220,
    marginBottom: 4,
  },
});

export default CategorySettingsScreen;
