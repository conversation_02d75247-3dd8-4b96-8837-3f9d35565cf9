import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import {useToast} from '../context/ToastContext';
import notificationService from '../services/NotificationService';
import { useTheme } from '../context/ThemeContext';

const CreditCardAddScreen = ({navigation}) => {
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [bankName, setBankName] = useState('');
  const [lastThreeDigits, setLastThreeDigits] = useState('');
  const [billingDay, setBillingDay] = useState('');
  const [paymentDueDay, setPaymentDueDay] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLORS.primary);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 卡片颜色选项 - 使用更符合iOS风格的颜色
  const colorOptions = [
    COLORS.primary,
    '#007AFF', // iOS 蓝色
    '#34C759', // iOS 绿色
    '#FF9500', // iOS 橙色
    '#FF2D55', // iOS 粉色
    '#AF52DE', // iOS 紫色
    '#5856D6', // iOS 深紫色
    '#FF3B30', // iOS 红色
  ];

  const validate = () => {
    if (!bankName.trim()) {
      showToast('请输入银行名称', 'error');
      return false;
    }

    if (!lastThreeDigits.trim() || lastThreeDigits.length !== 3 || !/^\d{3}$/.test(lastThreeDigits)) {
      showToast('请输入正确的信用卡后三位数字', 'error');
      return false;
    }

    const billDay = parseInt(billingDay);
    if (isNaN(billDay) || billDay < 1 || billDay > 31) {
      showToast('请输入有效的账单日（1-31）', 'error');
      return false;
    }

    const dueDay = parseInt(paymentDueDay);
    if (isNaN(dueDay) || dueDay < 1 || dueDay > 31) {
      showToast('请输入有效的还款日（1-31）', 'error');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validate()) {return;}

    setIsSubmitting(true);
    try {
      await databaseService.addCreditCard({
        bankName,
        lastThreeDigits,
        billingDay: parseInt(billingDay),
        paymentDueDay: parseInt(paymentDueDay),
        color: selectedColor,
      });

      // 添加成功后安排通知
      await notificationService.scheduleCreditCardReminders();

      showToast('信用卡添加成功', 'success');
      navigation.goBack();
    } catch (error) {
      console.error('添加信用卡失败', error);
      showToast('添加信用卡失败', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageContainer headerTitle="添加信用卡" backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container}>
        {/* 卡片预览 */}
        <View style={[styles.cardPreview, {backgroundColor: selectedColor}]}>
          {/* 卡片装饰元素 */}
          {/* <View style={styles.cardDecorations}>
            <View style={styles.cardChip} />
            <Icon name="wifi" size={16} color="rgba(255,255,255,0.6)" style={styles.wifiIcon} />
          </View> */}

          <View style={styles.cardContent}>
            <View style={styles.cardHeader}>
              <Text style={styles.previewBankName}>{bankName || '银行名称'}</Text>
              <Icon name="credit-card" size={20} color="rgba(255,255,255,0.8)" />
            </View>

            <Text style={styles.previewCardNumber}>
              **** **** **** {lastThreeDigits.padStart(3, '*')}
            </Text>

            <View style={styles.previewDates}>
              <View style={styles.previewDateItem}>
                <Icon name="calendar" size={12} color="rgba(255,255,255,0.8)" />
                <Text style={styles.previewDateLabel}>
                  账单日 {billingDay || '--'}号
                </Text>
              </View>
              <View style={styles.previewDateItem}>
                <Icon name="clock" size={12} color="rgba(255,255,255,0.8)" />
                <Text style={styles.previewDateLabel}>
                  还款日 {paymentDueDay || '--'}号
                </Text>
              </View>
            </View>
          </View>

          {/* 卡片光泽效果 */}
          <View style={styles.cardGloss} />
        </View>

        {/* 颜色选择器 */}
        <View style={styles.colorSelector}>
          <View style={styles.sectionHeader}>
            <Icon name="palette" size={16} color={colors.primary} />
            <Text style={styles.colorTitle}>选择卡片颜色</Text>
          </View>
          <View style={styles.colorOptions}>
            {colorOptions.map((color, index) => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.colorOption,
                  {backgroundColor: color},
                  selectedColor === color && styles.selectedColorOption,
                ]}
                onPress={() => setSelectedColor(color)}
                activeOpacity={0.8}>
                {selectedColor === color && (
                  <Icon name="check" size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 表单 */}
        <View style={styles.formContainer}>
          <View style={styles.sectionHeader}>
            <Icon name="circle-info" size={16} color={colors.primary} />
            <Text style={styles.sectionTitle}>基本信息</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>银行名称</Text>
            <View style={styles.inputContainer}>
              <Icon name="building-columns" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="请输入银行名称"
                value={bankName}
                onChangeText={setBankName}
                maxLength={20}
                placeholderTextColor={COLORS.text.placeholder}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>信用卡后三位</Text>
            <View style={styles.inputContainer}>
              <Icon name="hashtag" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="请输入信用卡后三位数字"
                value={lastThreeDigits}
                onChangeText={value => {
                  // 只允许输入数字，且最多3位
                  if (/^\d{0,3}$/.test(value)) {
                    setLastThreeDigits(value);
                  }
                }}
                keyboardType="numeric"
                maxLength={3}
                placeholderTextColor={COLORS.text.placeholder}
              />
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, {flex: 1, marginRight: 8}]}>
              <Text style={styles.label}>账单日</Text>
              <View style={styles.inputContainer}>
                <Icon name="calendar-days" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="每月几号"
                  value={billingDay}
                  onChangeText={value => {
                    // 只允许输入1-31的数字
                    if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                      setBillingDay(value);
                    }
                  }}
                  keyboardType="numeric"
                  maxLength={2}
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>号</Text>
              </View>
            </View>

            <View style={[styles.formGroup, {flex: 1, marginLeft: 8}]}>
              <Text style={styles.label}>还款日</Text>
              <View style={styles.inputContainer}>
                <Icon name="clock" size={16} color={COLORS.text.gray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="最后还款"
                  value={paymentDueDay}
                  onChangeText={value => {
                    // 只允许输入1-31的数字
                    if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                      setPaymentDueDay(value);
                    }
                  }}
                  keyboardType="numeric"
                  maxLength={2}
                  placeholderTextColor={COLORS.text.placeholder}
                />
                <Text style={styles.inputSuffix}>号</Text>
              </View>
            </View>
          </View>

          <View style={styles.tipsContainer}>
            <Icon name="circle-info" size={14} color={COLORS.text.gray} />
            <Text style={styles.tipsText}>
              添加信用卡信息后，系统将在账单日和还款日前自动提醒您，帮助您避免逾期。
            </Text>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
        style={[styles.saveButton, isSubmitting && styles.disabledButton, {backgroundColor: colors.primary}]}
        onPress={handleSave}
        disabled={isSubmitting}>
        <Text style={styles.saveButtonText}>
          {isSubmitting ? '保存中...' : '确认添加'}
        </Text>
      </TouchableOpacity>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  cardPreview: {
    height: 220,
    borderRadius: 20,
    margin: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  cardDecorations: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  cardChip: {
    width: 32,
    height: 24,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  wifiIcon: {
    transform: [{rotate: '90deg'}],
  },
  cardContent: {
    flex: 1,
    padding: 24,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  previewBankName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    letterSpacing: -0.6,
  },
  previewCardNumber: {
    fontSize: 20,
    color: '#FFFFFF',
    opacity: 0.95,
    letterSpacing: 2,
    fontFamily: 'Menlo',
    marginTop: 16,
  },
  previewDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  previewDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewDateLabel: {
    fontSize: 13,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
    marginLeft: 6,
    letterSpacing: -0.2,
  },
  cardGloss: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  colorSelector: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.4,
  },
  colorTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.4,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  colorOption: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 3,
    borderColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedColorOption: {
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    transform: [{scale: 1.1}],
  },
  formContainer: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  formGroup: {
    marginBottom: 24,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 10,
    letterSpacing: -0.3,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    padding: 0,
  },
  inputSuffix: {
    fontSize: 16,
    color: COLORS.text.gray,
    fontWeight: '500',
    marginLeft: 8,
  },
  tipsContainer: {
    flexDirection: 'row',
    backgroundColor: '#F2F2F7', // iOS系统浅灰色
    borderRadius: 10,
    padding: 14,
    alignItems: 'flex-start',
  },
  tipsText: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginLeft: 10,
    flex: 1,
    lineHeight: 18,
    letterSpacing: -0.2,
  },
  saveButton: {
    margin: 16,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
});

export default CreditCardAddScreen;
