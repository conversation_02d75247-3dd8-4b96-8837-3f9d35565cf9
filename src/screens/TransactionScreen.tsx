import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View, Text, StyleSheet, Modal, TouchableWithoutFeedback } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { TransactionWithCategory } from '../types/TransactionTypes';
import { getDefaultCategoryColor } from '../utils/CategoryUtils';
import { Family } from '../services/DatabaseService';
import { databaseService } from '../services/DatabaseService';
import { COLORS } from '../constants/Colors';

const [filterFamily, setFilterFamily] = useState<number | null>(null);
const [availableFamilies, setAvailableFamilies] = useState<Family[]>([]);
const [filterModalVisible, setFilterModalVisible] = useState(false);

useEffect(() => {
  const loadFamilies = async () => {
    try {
      const familyInfo = await databaseService.getFamilyInfo();
      if (familyInfo) {
        setAvailableFamilies([familyInfo]);
      }
    } catch (error) {
      console.error('加载家庭信息失败', error);
    }
  };

  loadFamilies();
}, []);

const renderTransactionItem = ({item}: {item: TransactionWithCategory}) => {
  const isExpense = item.type === 'expense';

  return (
    <TouchableOpacity
      style={styles.transactionItem}
      onPress={() => handleTransactionPress(item)}
      activeOpacity={0.7}>
      <View style={styles.transactionLeft}>
        <View
          style={[
            styles.categoryIcon,
            {backgroundColor: item.categoryColor || getDefaultCategoryColor(item.type)},
          ]}>
          <Icon
            name={item.categoryIcon || 'question'}
            size={18}
            color="#FFFFFF"
          />
        </View>
        <View style={styles.transactionInfo}>
          <View style={styles.categoryNameContainer}>
            <Text style={styles.categoryName}>{item.categoryName}</Text>

            {/* 添加家庭标识 */}
            {item.familyId && (
              <View style={styles.familyBadge}>
                <Icon name="house-user" size={12} color="#7E57C2" style={{marginRight: 4}} />
                <Text style={styles.familyBadgeText}>家庭账单</Text>
              </View>
            )}
          </View>

          {item.note ? (
            <Text style={styles.transactionNote} numberOfLines={1}>
              {item.note}
            </Text>
          ) : null}
          <Text style={styles.transactionDate}>{formatDate(item.date)}</Text>
        </View>
      </View>
      <Text
        style={[
          styles.transactionAmount,
          isExpense ? styles.expenseText : styles.incomeText,
        ]}>
        {isExpense ? '-￥' : '+￥'}
        {item.amount.toFixed(2)}
      </Text>
    </TouchableOpacity>
  );
};

// 添加家庭标识相关样式
const styles = StyleSheet.create({
  // ... 现有样式 ...

  categoryNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  familyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(126, 87, 194, 0.1)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  familyBadgeText: {
    fontSize: 12,
    color: '#7E57C2',
    fontWeight: '500',
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.background.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  filterModalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    padding: 20,
  },
  filterModalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: COLORS.background.light,
  },
  selectedFilterOption: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  filterOptionIcon: {
    marginRight: 16,
  },
  filterOptionInfo: {
    flex: 1,
  },
  filterOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  selectedFilterOptionText: {
    color: COLORS.primary,
  },
  filterOptionDesc: {
    fontSize: 14,
    color: COLORS.text.gray,
  },
});
