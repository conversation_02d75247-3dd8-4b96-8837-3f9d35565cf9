import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {CreditCard} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import {useToast} from '../context/ToastContext';
import dayjs from 'dayjs';
import CustomModal from '../components/CustomModal';
import notificationService from '../services/NotificationService';
import { useTheme } from '../context/ThemeContext';

const CreditCardDetailScreen = ({navigation, route}) => {
  const {cardId} = route.params;
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  const [creditCard, setCreditCard] = useState<CreditCard>({
    bankName: '',
    lastThreeDigits: '',
    billingDay: 1,
    paymentDueDay: 1,
    color: COLORS.primary,
  });

  // 编辑时的表单状态
  const [bankName, setBankName] = useState('');
  const [lastThreeDigits, setLastThreeDigits] = useState('');
  const [billingDay, setBillingDay] = useState('');
  const [paymentDueDay, setPaymentDueDay] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLORS.primary);

  // 卡片颜色选项 - 使用更符合iOS风格的颜色
  const colorOptions = [
    COLORS.primary,
    '#007AFF', // iOS 蓝色
    '#34C759', // iOS 绿色
    '#FF9500', // iOS 橙色
    '#FF2D55', // iOS 粉色
    '#AF52DE', // iOS 紫色
    '#5856D6', // iOS 深紫色
    '#FF3B30', // iOS 红色
  ];

  const loadCreditCard = useCallback(async () => {
    setIsLoading(true);
    try {
      const card = await databaseService.getCreditCardById(cardId);
      if (card) {
        setCreditCard(card);

        // 初始化编辑表单
        setBankName(card.bankName);
        setLastThreeDigits(card.lastThreeDigits);
        setBillingDay(card.billingDay.toString());
        setPaymentDueDay(card.paymentDueDay.toString());
        setSelectedColor(card.color || COLORS.primary);
      } else {
        showToast('未找到信用卡信息', 'error');
        navigation.goBack();
      }
    } catch (error) {
      console.error('加载信用卡信息失败', error);
      showToast('加载信用卡信息失败', 'error');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  }, [cardId, navigation, showToast]);

  useEffect(() => {
    loadCreditCard();
  }, []);

  // 计算下次账单日和还款日
  const calculateNextDates = (billingDay: number, paymentDueDay: number) => {
    let now = dayjs();
    let nextBillingDate, nextPaymentDate;

    // 计算下次账单日
    if (now.date() > billingDay) {
      // 如果当前日期已经过了本月账单日，则下次账单日在下个月
      nextBillingDate = now.add(1, 'month').set('date', billingDay);
    } else {
      // 否则下次账单日就在本月
      nextBillingDate = now.set('date', billingDay);
    }

    // 计算下次还款日
    if (now.date() > paymentDueDay) {
      // 如果当前日期已经过了本月还款日，则下次还款日在下个月
      nextPaymentDate = now.add(1, 'month').set('date', paymentDueDay);
    } else {
      // 否则下次还款日就在本月
      nextPaymentDate = now.set('date', paymentDueDay);
    }

    return {
      nextBillingDate: nextBillingDate.format('YYYY-MM-DD'),
      nextPaymentDate: nextPaymentDate.format('YYYY-MM-DD'),
      daysUntilPayment: nextPaymentDate.diff(now, 'day'),
    };
  };

  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  const validate = () => {
    if (!bankName.trim()) {
      showToast('请输入银行名称', 'error');
      return false;
    }

    if (!lastThreeDigits.trim() || lastThreeDigits.length !== 3 || !/^\d{3}$/.test(lastThreeDigits)) {
      showToast('请输入正确的信用卡后三位数字', 'error');
      return false;
    }

    const billDay = parseInt(billingDay);
    if (isNaN(billDay) || billDay < 1 || billDay > 31) {
      showToast('请输入有效的账单日（1-31）', 'error');
      return false;
    }

    const dueDay = parseInt(paymentDueDay);
    if (isNaN(dueDay) || dueDay < 1 || dueDay > 31) {
      showToast('请输入有效的还款日（1-31）', 'error');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validate()) {return;}

    setIsSubmitting(true);
    try {
      const updatedCard: CreditCard = {
        id: cardId,
        bankName,
        lastThreeDigits,
        billingDay: parseInt(billingDay),
        paymentDueDay: parseInt(paymentDueDay),
        color: selectedColor,
      };

      await databaseService.updateCreditCard(updatedCard);

      // 更新成功后重新安排通知
      await notificationService.scheduleCreditCardReminders();

      showToast('信用卡信息已更新', 'success');
      setIsEditing(false);
      loadCreditCard(); // 重新加载最新数据
    } catch (error) {
      console.error('更新信用卡失败', error);
      showToast('更新信用卡失败', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDelete = () => {
    setDeleteModalVisible(true);
  };

  const handleDelete = async () => {
    setDeleteModalVisible(false);
    setIsSubmitting(true);

    try {
      await databaseService.deleteCreditCard(cardId);

      // 删除成功后重新安排通知
      await notificationService.scheduleCreditCardReminders();

      showToast('信用卡已删除', 'success');
      navigation.goBack();
    } catch (error) {
      console.error('删除信用卡失败', error);
      showToast('删除信用卡失败', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <PageContainer headerTitle="信用卡详情">
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </PageContainer>
    );
  }

  const {nextBillingDate, nextPaymentDate, daysUntilPayment} = calculateNextDates(
    creditCard.billingDay,
    creditCard.paymentDueDay
  );

  // 渲染详情模式
  const renderViewMode = () => (
    <ScrollView style={styles.container}>
      {/* 卡片预览 */}
      <View style={[styles.cardPreview, {backgroundColor: creditCard.color}]}>
        {/* 卡片装饰元素 */}
        {/* <View style={styles.cardDecorations}>
          <View style={styles.cardChip} />
          <Icon name="wifi" size={16} color="rgba(255,255,255,0.6)" style={styles.wifiIcon} />
        </View> */}

        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <Text style={styles.previewBankName}>{creditCard.bankName}</Text>
            <Icon name="credit-card" size={20} color="rgba(255,255,255,0.8)" />
          </View>

          <Text style={styles.previewCardNumber}>
            **** **** **** {creditCard.lastThreeDigits}
          </Text>

          <View style={styles.previewDates}>
            <View style={styles.previewDateItem}>
              <Icon name="calendar" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.previewDateLabel}>账单日 {creditCard.billingDay}号</Text>
            </View>
            <View style={styles.previewDateItem}>
              <Icon name="clock" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.previewDateLabel}>还款日 {creditCard.paymentDueDay}号</Text>
            </View>
          </View>
        </View>

        {/* 卡片光泽效果 */}
        <View style={styles.cardGloss} />
      </View>

      {/* 还款信息 */}
      <View style={styles.infoContainer}>
        <View style={styles.infoHeader}>
          <Icon name="calendar-days" size={20} color={colors.primary} />
          <Text style={styles.infoHeaderText}>还款信息</Text>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>下次账单日</Text>
            <Text style={styles.infoValue}>{nextBillingDate}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>下次还款日</Text>
            <Text style={styles.infoValue}>{nextPaymentDate}</Text>
          </View>
        </View>

        <View style={styles.countdownContainer}>
          <View style={styles.countdownHeader}>
            <Icon
              name={daysUntilPayment <= 3 ? 'triangle-exclamation' :
                    daysUntilPayment <= 7 ? 'clock' : 'check-circle'}
              size={16}
              color={daysUntilPayment <= 3 ? '#FF3B30' :
                     daysUntilPayment <= 7 ? '#FF9500' : '#34C759'}
            />
            <Text style={styles.countdownLabel}>距离还款日还有</Text>
          </View>

          <View style={[
            styles.countdownBadge,
            {backgroundColor: daysUntilPayment <= 3 ? '#FFE5E5' :
                              daysUntilPayment <= 7 ? '#FFF3E0' : '#E8F5E8'},
          ]}>
            <Text style={[
              styles.countdownValue,
              daysUntilPayment <= 3 ? styles.urgentDays :
              daysUntilPayment <= 7 ? styles.warningDays :
              styles.normalDays,
            ]}>
              {daysUntilPayment}
            </Text>
            <Text style={[
              styles.countdownUnit,
              daysUntilPayment <= 3 ? styles.urgentDays :
              daysUntilPayment <= 7 ? styles.warningDays :
              styles.normalDays,
            ]}>
              天
            </Text>
          </View>

          {daysUntilPayment <= 3 && (
            <View style={styles.alertContainer}>
              <Icon name="exclamation-triangle" size={14} color="#FF3B30" />
              <Text style={styles.urgentText}>请尽快安排还款!</Text>
            </View>
          )}
          {daysUntilPayment > 3 && daysUntilPayment <= 7 && (
            <View style={styles.alertContainer}>
              <Icon name="info-circle" size={14} color="#FF9500" />
              <Text style={styles.warningText}>请提前准备还款资金</Text>
            </View>
          )}
        </View>
      </View>

      {/* 卡片详情 */}
      <View style={styles.infoContainer}>
        <View style={styles.infoHeader}>
          <Icon name="credit-card" size={20} color={colors.primary} />
          <Text style={styles.infoHeaderText}>卡片详情</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>银行名称</Text>
          <Text style={styles.detailValue}>{creditCard.bankName}</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>卡号后三位</Text>
          <Text style={styles.detailValue}>{creditCard.lastThreeDigits}</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>账单日</Text>
          <Text style={styles.detailValue}>每月 {creditCard.billingDay} 日</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>还款日</Text>
          <Text style={styles.detailValue}>每月 {creditCard.paymentDueDay} 日</Text>
        </View>
      </View>

      {/* 操作按钮 */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.editButton, {backgroundColor: colors.primary}]}
          onPress={toggleEditMode}
          activeOpacity={0.8}>
          <Icon name="pen-to-square" size={16} color="#FFFFFF" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>编辑信息</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={confirmDelete}
          activeOpacity={0.8}>
          <Icon name="trash" size={16} color="#FFFFFF" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>删除卡片</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  // 渲染编辑模式
  const renderEditMode = () => (
    <ScrollView style={styles.container}>
      {/* 卡片预览 */}
      <View style={[styles.cardPreview, {backgroundColor: selectedColor}]}>
        {/* 卡片装饰元素 */}
        {/* <View style={styles.cardDecorations}>
          <View style={styles.cardChip} />
          <Icon name="wifi" size={16} color="rgba(255,255,255,0.6)" style={styles.wifiIcon} />
        </View> */}

        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <Text style={styles.previewBankName}>{bankName || '银行名称'}</Text>
            <Icon name="credit-card" size={20} color="rgba(255,255,255,0.8)" />
          </View>

          <Text style={styles.previewCardNumber}>
            **** **** **** {lastThreeDigits.padStart(3, '*')}
          </Text>

          <View style={styles.previewDates}>
            <View style={styles.previewDateItem}>
              <Icon name="calendar" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.previewDateLabel}>账单日 {billingDay || '--'}号</Text>
            </View>
            <View style={styles.previewDateItem}>
              <Icon name="clock" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.previewDateLabel}>还款日 {paymentDueDay || '--'}号</Text>
            </View>
          </View>
        </View>

        {/* 卡片光泽效果 */}
        <View style={styles.cardGloss} />
      </View>

      {/* 颜色选择器 */}
      <View style={styles.colorSelector}>
        <Text style={styles.colorTitle}>选择卡片颜色</Text>
        <View style={styles.colorOptions}>
          {colorOptions.map(color => (
            <TouchableOpacity
              key={color}
              style={[
                styles.colorOption,
                {backgroundColor: color},
                selectedColor === color && styles.selectedColorOption,
              ]}
              onPress={() => setSelectedColor(color)}
            />
          ))}
        </View>
      </View>

      {/* 表单 */}
      <View style={styles.formContainer}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>银行名称</Text>
          <TextInput
            style={styles.input}
            placeholder="请输入银行名称"
            value={bankName}
            onChangeText={setBankName}
            maxLength={20}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>信用卡后三位</Text>
          <TextInput
            style={styles.input}
            placeholder="请输入信用卡后三位数字"
            value={lastThreeDigits}
            onChangeText={value => {
              // 只允许输入数字，且最多3位
              if (/^\d{0,3}$/.test(value)) {
                setLastThreeDigits(value);
              }
            }}
            keyboardType="numeric"
            maxLength={3}
          />
        </View>

        <View style={styles.formRow}>
          <View style={[styles.formGroup, {flex: 1, marginRight: 10}]}>
            <Text style={styles.label}>账单日</Text>
            <TextInput
              style={styles.input}
              placeholder="每月几号出账单"
              value={billingDay}
              onChangeText={value => {
                // 只允许输入1-31的数字
                if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                  setBillingDay(value);
                }
              }}
              keyboardType="numeric"
              maxLength={2}
            />
          </View>

          <View style={[styles.formGroup, {flex: 1}]}>
            <Text style={styles.label}>还款日</Text>
            <TextInput
              style={styles.input}
              placeholder="最后还款日"
              value={paymentDueDay}
              onChangeText={value => {
                // 只允许输入1-31的数字
                if (/^\d{0,2}$/.test(value) && (parseInt(value) <= 31 || value === '')) {
                  setPaymentDueDay(value);
                }
              }}
              keyboardType="numeric"
              maxLength={2}
            />
          </View>
        </View>

        <View style={styles.tipsContainer}>
          <Icon name="circle-info" size={14} color={COLORS.text.gray} />
          <Text style={styles.tipsText}>
            更新信用卡信息后，系统将根据新设置的账单日和还款日重新计算提醒时间。
          </Text>
        </View>
      </View>

      {/* 保存按钮 */}
      <View style={styles.editActionButtons}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => {
            // 重置表单数据
            setBankName(creditCard.bankName);
            setLastThreeDigits(creditCard.lastThreeDigits);
            setBillingDay(creditCard.billingDay.toString());
            setPaymentDueDay(creditCard.paymentDueDay.toString());
            setSelectedColor(creditCard.color || COLORS.primary);
            setIsEditing(false);
          }}>
          <Text style={styles.cancelButtonText}>取消</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.saveButton, isSubmitting && styles.disabledButton, {backgroundColor: colors.primary}]}
          onPress={handleSave}
          disabled={isSubmitting}>
          <Text style={styles.saveButtonText}>
            {isSubmitting ? '保存中...' : '保存更改'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  return (
    <PageContainer headerTitle="信用卡详情" backgroundColor={COLORS.background.light}>
      {isEditing ? renderEditMode() : renderViewMode()}

      {/* 删除确认弹窗 */}
      <CustomModal
        visible={deleteModalVisible}
        onClose={() => setDeleteModalVisible(false)}
        animationType="fade"
        position="center"
        customStyle={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>确认删除</Text>
          <Text style={styles.modalMessage}>
            您确定要删除这张信用卡吗？删除后将不再收到相关还款提醒。
          </Text>
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelModalButton]}
              onPress={() => setDeleteModalVisible(false)}>
              <Text style={styles.cancelModalText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.deleteModalButton]}
              onPress={handleDelete}>
              <Text style={styles.deleteModalText}>删除</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardPreview: {
    height: 220,
    borderRadius: 20,
    margin: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  cardDecorations: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  cardChip: {
    width: 32,
    height: 24,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  wifiIcon: {
    transform: [{rotate: '90deg'}],
  },
  cardContent: {
    flex: 1,
    padding: 24,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  previewBankName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    letterSpacing: -0.6,
  },
  previewCardNumber: {
    fontSize: 20,
    color: '#FFFFFF',
    opacity: 0.95,
    letterSpacing: 2,
    fontFamily: 'Menlo',
    marginTop: 16,
  },
  previewDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  previewDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewDateLabel: {
    fontSize: 13,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
    marginLeft: 6,
    letterSpacing: -0.2,
  },
  cardGloss: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  infoContainer: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 2,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  infoHeaderText: {
    fontSize: 17,
    fontWeight: '600',
    marginLeft: 10,
    color: COLORS.text.primary,
    letterSpacing: -0.5,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '400',
  },
  infoValue: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    letterSpacing: -0.5,
  },
  countdownContainer: {
    alignItems: 'center',
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
    marginTop: 8,
  },
  countdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  countdownLabel: {
    fontSize: 15,
    color: COLORS.text.gray,
    fontWeight: '500',
    marginLeft: 8,
    letterSpacing: -0.2,
  },
  countdownBadge: {
    flexDirection: 'row',
    alignItems: 'baseline',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    marginBottom: 12,
  },
  countdownValue: {
    fontSize: 36,
    fontWeight: '700',
    letterSpacing: -1,
  },
  countdownUnit: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 4,
    letterSpacing: -0.3,
  },
  normalDays: {
    color: '#34C759',
  },
  warningDays: {
    color: '#FF9500',
  },
  urgentDays: {
    color: '#FF3B30',
  },
  alertContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  urgentText: {
    color: '#FF3B30',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    letterSpacing: -0.2,
  },
  warningText: {
    color: '#FF9500',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    letterSpacing: -0.2,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  detailLabel: {
    fontSize: 15,
    color: COLORS.text.gray,
    fontWeight: '400',
  },
  detailValue: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    letterSpacing: -0.5,
  },
  actionButtons: {
    margin: 16,
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    flex: 1,
    marginRight: 8,
  },
  deleteButton: {
    backgroundColor: COLORS.functional.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    flex: 1,
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  colorSelector: {
    margin: 16,
    marginTop: 8,
  },
  colorTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 14,
    marginBottom: 14,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3,
    elevation: 4,
  },
  formContainer: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 2,
  },
  formGroup: {
    marginBottom: 20,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  input: {
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 17,
    color: COLORS.text.primary,
  },
  tipsContainer: {
    flexDirection: 'row',
    backgroundColor: '#F2F2F7',
    borderRadius: 10,
    padding: 14,
    alignItems: 'flex-start',
  },
  tipsText: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginLeft: 10,
    flex: 1,
    lineHeight: 18,
    letterSpacing: -0.2,
  },
  editActionButtons: {
    margin: 16,
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    backgroundColor: '#F2F2F7',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    color: COLORS.text.primary,
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  saveButton: {
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  modalContainer: {
    padding: 0,
    borderRadius: 14,
    width: '85%',
    maxWidth: 320,
    overflow: 'hidden',
  },
  modalContent: {
    padding: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  modalMessage: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
    letterSpacing: -0.2,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  cancelModalButton: {
    backgroundColor: '#F2F2F7',
  },
  deleteModalButton: {
    backgroundColor: COLORS.functional.error,
  },
  cancelModalText: {
    color: COLORS.text.primary,
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  deleteModalText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
});

export default CreditCardDetailScreen;
