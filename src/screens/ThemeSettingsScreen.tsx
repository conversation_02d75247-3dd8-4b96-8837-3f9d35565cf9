import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme, THEME_CONFIGS, ThemeKey } from '../context/ThemeContext';
import PageContainer from '../components/PageContainer';
import { useToast } from '../context/ToastContext';
import { COLORS } from '../utils/color';

const { width } = Dimensions.get('window');
const ThemeSettingsScreen = ({ navigation: _navigation }) => {
  const { currentTheme, changeTheme, colors, themeConfig } = useTheme();
  const { showToast } = useToast();

  // 处理主题切换
  const handleThemeChange = (theme: ThemeKey) => {
    changeTheme(theme);
    const config = THEME_CONFIGS[theme];
    showToast(`已切换为${config.name}主题`, 'success');
  };

  // 按分类组织主题
  const themesByCategory = Object.entries(THEME_CONFIGS).reduce((acc, [key, config]) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push({ key: key as ThemeKey, config });
    return acc;
  }, {} as Record<string, Array<{ key: ThemeKey; config: typeof THEME_CONFIGS[ThemeKey] }>>);

  const categoryNames = {
    classic: '经典系列',
    vibrant: '活力系列',
    nature: '自然系列',
    warm: '温暖系列',
    elegant: '优雅系列',
    passionate: '热情系列',
    fresh: '清新系列',
    dark: '深邃系列',
  };

  return (
    <PageContainer headerTitle="主题设置" backgroundColor={COLORS.secondary}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 当前主题预览 */}
        <View style={styles.currentThemeSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            当前主题
          </Text>
          <View style={styles.currentThemeCard}>
            <LinearGradient
              colors={themeConfig.gradient}
              style={styles.currentThemeGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.currentThemeContent}>
                <Text style={styles.currentThemeName}>{themeConfig.name}</Text>
                <Text style={styles.currentThemeDesc}>{themeConfig.description}</Text>
              </View>
            </LinearGradient>
          </View>
        </View>

        {/* 主题选择 */}
        {Object.entries(themesByCategory).map(([category, themes]) => (
          <View key={category} style={styles.categorySection}>
            <Text style={[styles.categoryTitle, { color: colors.text.primary }]}>
              {categoryNames[category] || category}
            </Text>
            <View style={styles.themeGrid}>
              {themes.map(({ key, config }) => (
                <TouchableOpacity
                  key={key}
                  style={[
                    styles.themeCard,
                    currentTheme === key && styles.selectedThemeCard,
                  ]}
                  onPress={() => handleThemeChange(key)}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={config.gradient}
                    style={styles.themeGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    {currentTheme === key && (
                      <View style={styles.selectedIndicator}>
                        <Icon name="check" size={16} color="#FFFFFF" />
                      </View>
                    )}
                  </LinearGradient>
                  <View style={styles.themeInfo}>
                    <Text style={[styles.themeName, currentTheme === key && { color: colors.primary }]}>
                      {config.name}
                    </Text>
                    <Text style={styles.themeDesc} numberOfLines={1}>
                      {config.description}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  currentThemeSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  currentThemeCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  currentThemeGradient: {
    padding: 20,
    minHeight: 100,
    justifyContent: 'center',
  },
  currentThemeContent: {
    alignItems: 'center',
  },
  currentThemeName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  currentThemeDesc: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  categorySection: {
    marginBottom: 28,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    marginLeft: 4,
  },
  themeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  themeCard: {
    width: (width - 48) / 2, // 每行两个，考虑padding
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  selectedThemeCard: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  themeGradient: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  themeInfo: {
    padding: 12,
  },
  themeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  themeDesc: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
});

export default ThemeSettingsScreen;
