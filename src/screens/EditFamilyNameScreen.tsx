import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import PageContainer from '../components/PageContainer';
import {COLORS} from '../utils/color';
import databaseService, {Family} from '../services/DatabaseService';
import {useToast} from '../context/ToastContext';
import { useTheme } from '../context/ThemeContext';

const EditFamilyNameScreen = ({route, navigation}) => {
  const {family} = route.params;
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [familyName, setFamilyName] = useState(family ? family.name : '');
  const [isLoading, setIsLoading] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    checkAdminStatus();
  }, []);

  // 检查当前用户是否为管理员
  const checkAdminStatus = async () => {
    try {
      const deviceId = await databaseService.getDeviceId();
      const [result] = await databaseService.database!.executeSql(
        `SELECT * FROM family_members 
         WHERE family_id = ? AND device_id = ? AND role = 'admin'`,
        [family.id, deviceId],
      );

      setIsAdmin(result.rows.length > 0);

      if (result.rows.length === 0) {
        showToast('只有家庭管理员可以修改家庭名称', 'warning');
        navigation.goBack();
      }
    } catch (error) {
      console.error('检查管理员状态失败', error);
      showToast('检查管理员状态失败', 'error');
      navigation.goBack();
    }
  };

  // 保存家庭名称
  const saveFamilyName = async () => {
    if (!familyName.trim()) {
      showToast('请输入家庭名称', 'warning');
      return;
    }

    try {
      setIsLoading(true);
      await databaseService.updateFamilyName(family.id, familyName.trim());
      setIsLoading(false);
      showToast('家庭名称已更新', 'success');
      navigation.goBack();
    } catch (error) {
      setIsLoading(false);
      console.error('更新家庭名称失败', error);
      showToast('更新家庭名称失败', 'error'); // 调用 showToast 函数来显示错误提示
    }
  };

  const renderSaveButton = () => (
    <TouchableOpacity
      style={[styles.saveButton, {backgroundColor: colors.primary}]}
      onPress={saveFamilyName}
      disabled={isLoading || !isAdmin}>
      <Text style={styles.saveButtonText}>保存</Text>
    </TouchableOpacity>
  );

  return (
    <PageContainer
      headerTitle="修改家庭名称"
      rightComponent={renderSaveButton()}
      backgroundColor={COLORS.secondary}>
      <View style={styles.container}>
        <Text style={styles.label}>家庭名称</Text>
        <TextInput
          style={styles.input}
          placeholder="请输入家庭名称"
          value={familyName}
          onChangeText={setFamilyName}
          maxLength={20}
          autoFocus
          editable={!isLoading && isAdmin}
        />

        {isLoading && (
          <ActivityIndicator
            size="large"
            color={COLORS.primary}
            style={styles.loading}
          />
        )}

        <Text style={styles.tip}>家庭名称将在所有成员设备上同步显示</Text>
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: COLORS.secondary,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 10,
  },
  input: {
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    marginBottom: 20,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loading: {
    marginVertical: 20,
  },
  tip: {
    fontSize: 14,
    color: COLORS.text.gray,
    textAlign: 'center',
    marginTop: 20,
  },
});

export default EditFamilyNameScreen;
