import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import {useToast} from '../context/ToastContext';
import PageContainer from '../components/PageContainer';
import CustomCalendar from '../components/CustomCalendar';
import databaseService, {LoanRecord} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import {useTheme} from '../context/ThemeContext';
import ConfirmDialog from '../components/ConfirmDialog';

const LoanRecordEditScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  const {record, onSaved} = route.params || {};
  const isEditMode = !!record;
  const {showToast} = useToast();

  // 表单状态
  const [type, setType] = useState(isEditMode ? record.type : 'lend');
  const [amount, setAmount] = useState(isEditMode ? String(record.amount) : '');
  const [note, setNote] = useState(isEditMode ? record.note : '');
  const [loanDate, setLoanDate] = useState(
    isEditMode ? dayjs(record.loanDate) : dayjs(),
  );
  const [repayments, setRepayments] = useState(
    isEditMode ? record.repayments : [],
  );
  const [repayAmount, setRepayAmount] = useState('');
  const [repayDate, setRepayDate] = useState(dayjs());
  const [showLoanDateModal, setShowLoanDateModal] = useState(false);
  const [showRepayDateModal, setShowRepayDateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 保存
  const handleSave = async () => {
    if (!amount || isNaN(Number(amount))) {
      showToast('请输入有效金额', 'warning');
      return;
    }
    const data: LoanRecord = {
      id: record?.id,
      type,
      amount: Number(amount),
      note,
      loanDate: loanDate.toISOString(),
      repayments,
    };
    setLoading(true);
    try {
      if (record?.id) {
        await databaseService.updateLoanRecord(data);
      } else {
        await databaseService.addLoanRecord(data);
      }
      if (onSaved) {
        onSaved();
      }
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // 添加还款
  const handleAddRepayment = () => {
    if (!repayAmount || isNaN(Number(repayAmount))) {
      showToast('请输入有效还款金额', 'warning');
      return;
    }
    setRepayments([
      ...repayments,
      {
        amount: Number(repayAmount),
        date: repayDate.toISOString(),
      },
    ]);
    setRepayAmount('');
    setRepayDate(dayjs());
  };

  // 删除还款
  const handleDeleteRepayment = idx => {
    setRepayments(repayments.filter((_, i) => i !== idx));
  };

  // 删除借款
  const handleDelete = async () => {
    setShowDeleteDialog(false);
    if (!record?.id) {
      return;
    }
    setLoading(true);
    try {
      await databaseService.deleteLoanRecord(record.id);
      if (onSaved) {
        onSaved();
      }
      navigation.navigate('loanRecords');
    } finally {
      setLoading(false);
    }
  };

  const totalRepaid = repayments.reduce((sum, r) => sum + Number(r.amount), 0);

  // 获取类型配置
  const getTypeConfig = (type: string) => {
    return type === 'lend'
      ? {
          icon: 'hand-holding-dollar',
          color: '#34C759',
          bgColor: '#E8F5E8',
          label: '借出',
          gradientColors: ['#34C759', '#28A745'],
        }
      : {
          icon: 'hand-holding-heart',
          color: '#FF9500',
          bgColor: '#FFF3E0',
          label: '借入',
          gradientColors: ['#FF9500', '#FF8C00'],
        };
  };

  const typeConfig = getTypeConfig(type);

  return (
    <PageContainer
      headerTitle={isEditMode ? '编辑借款记录' : '新增借款记录'}
      backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 头部卡片 */}
        <LinearGradient
          colors={typeConfig.gradientColors}
          style={styles.headerCard}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Icon name={typeConfig.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{typeConfig.label}记录</Text>
              <Text style={styles.headerSubtitle}>
                {amount ? `￥${amount}` : '请填写金额'}
              </Text>
            </View>
          </View>
        </LinearGradient>

        <View style={styles.formContainer}>
          {/* 类型选择卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="tags" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>借款类型</Text>
            </View>
            <View style={styles.typeBtns}>
              <TouchableOpacity
                style={[
                  styles.typeBtn,
                  type === 'lend' && [
                    styles.typeBtnActive,
                    {backgroundColor: '#34C759'},
                  ],
                ]}
                onPress={() => setType('lend')}>
                <Icon
                  name="hand-holding-dollar"
                  size={16}
                  color={type === 'lend' ? '#fff' : '#34C759'}
                  style={styles.typeBtnIcon}
                />
                <Text
                  style={[
                    styles.typeBtnText,
                    type === 'lend' && styles.typeBtnTextActive,
                  ]}>
                  借出
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeBtn,
                  type === 'borrow' && [
                    styles.typeBtnActive,
                    {backgroundColor: '#FF9500'},
                  ],
                ]}
                onPress={() => setType('borrow')}>
                <Icon
                  name="hand-holding-heart"
                  size={16}
                  color={type === 'borrow' ? '#fff' : '#FF9500'}
                  style={styles.typeBtnIcon}
                />
                <Text
                  style={[
                    styles.typeBtnText,
                    type === 'borrow' && styles.typeBtnTextActive,
                  ]}>
                  借入
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* 基本信息卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="circle-info" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>基本信息</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>金额</Text>
              <View style={styles.inputContainer}>
                <Icon
                  name="dollar-sign"
                  size={16}
                  color={COLORS.text.gray}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={amount}
                  onChangeText={setAmount}
                  placeholder="请输入金额"
                  keyboardType="decimal-pad"
                  placeholderTextColor={COLORS.text.placeholder}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>备注</Text>
              <View style={styles.inputContainer}>
                <Icon
                  name="pen"
                  size={16}
                  color={COLORS.text.gray}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={[styles.input, styles.multilineInput]}
                  value={note}
                  onChangeText={setNote}
                  placeholder="请填写借款人、借款用途等信息"
                  placeholderTextColor={COLORS.text.placeholder}
                  multiline
                  numberOfLines={3}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>借款时间</Text>
              <TouchableOpacity
                style={styles.selector}
                onPress={() => setShowLoanDateModal(true)}>
                <Icon name="calendar" size={16} color={colors.primary} />
                <Text style={styles.selectorText}>
                  {loanDate.format('YYYY年MM月DD日')}
                </Text>
                <Icon name="chevron-right" size={14} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>
          </View>
          {/* 还款记录卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon
                name="money-bill-transfer"
                size={16}
                color={colors.primary}
              />
              <Text style={styles.cardTitle}>还款记录</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>新增还款</Text>
              <View style={styles.repayInputRow}>
                <View style={styles.repayAmountContainer}>
                  <Icon
                    name="dollar-sign"
                    size={14}
                    color={COLORS.text.gray}
                    style={styles.repayInputIcon}
                  />
                  <TextInput
                    style={styles.repayAmountInput}
                    keyboardType="decimal-pad"
                    value={repayAmount}
                    onChangeText={setRepayAmount}
                    placeholder="还款金额"
                    placeholderTextColor={COLORS.text.placeholder}
                  />
                </View>
                <TouchableOpacity
                  style={styles.repayDateSelector}
                  onPress={() => setShowRepayDateModal(true)}>
                  <Icon name="calendar" size={14} color={colors.primary} />
                  <Text style={styles.repayDateText}>
                    {repayDate.format('MM/DD')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.addRepayBtn,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={handleAddRepayment}>
                  <Icon name="plus" size={14} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {repayments.length > 0 && (
            <View style={styles.formCard}>
              <View style={styles.cardHeader}>
                <Icon name="list-check" size={16} color={colors.primary} />
                <Text style={styles.cardTitle}>已还款记录</Text>
              </View>
              <View style={styles.repaymentsList}>
                {repayments.map((r, idx) => (
                  <View key={idx} style={styles.repayItem}>
                    <View style={styles.repayItemLeft}>
                      <Icon name="calendar-check" size={14} color="#34C759" />
                      <View style={styles.repayItemInfo}>
                        <Text style={styles.repayText}>￥{r.amount}</Text>
                        <Text style={styles.repayDate}>
                          {dayjs(r.date).format('MM月DD日')}
                        </Text>
                      </View>
                    </View>
                    <TouchableOpacity
                      style={styles.deleteRepayBtn}
                      onPress={() => handleDeleteRepayment(idx)}>
                      <Icon name="trash" size={14} color="#FF3B30" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* 统计信息卡片 */}
          <View style={styles.formCard}>
            <View style={styles.cardHeader}>
              <Icon name="chart-line" size={16} color={colors.primary} />
              <Text style={styles.cardTitle}>统计信息</Text>
            </View>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>借款金额</Text>
                <Text style={styles.statValue}>￥{amount || '0'}</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>已还金额</Text>
                <Text style={[styles.statValue, {color: '#34C759'}]}>
                  ￥{totalRepaid}
                </Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>剩余金额</Text>
                <Text style={[styles.statValue, {color: '#FF9500'}]}>
                  ￥{amount ? (Number(amount) - totalRepaid).toFixed(2) : '0'}
                </Text>
              </View>
            </View>
          </View>
          {/* 操作按钮 */}
          <View style={styles.actionButtons}>
            {isEditMode && (
              <TouchableOpacity
                style={styles.deleteBtn}
                onPress={() => setShowDeleteDialog(true)}>
                <Icon
                  name="trash"
                  size={16}
                  color="#FFFFFF"
                  style={styles.buttonIcon}
                />
                <Text style={styles.deleteBtnText}>删除记录</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={[
                styles.saveBtn,
                {backgroundColor: colors.primary, opacity: loading ? 0.7 : 1},
                isEditMode && styles.saveBtnWithDelete,
              ]}
              onPress={handleSave}
              disabled={loading}>
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <Icon
                    name={isEditMode ? 'floppy-disk' : 'plus'}
                    size={16}
                    color="#FFFFFF"
                    style={styles.buttonIcon}
                  />
                  <Text style={styles.saveBtnText}>
                    {isEditMode ? '保存修改' : '添加记录'}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* 借款时间选择弹窗 */}
      <Modal
        visible={showLoanDateModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowLoanDateModal(false)}>
        <TouchableWithoutFeedback onPress={() => setShowLoanDateModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, styles.dateModalContent]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择借款日期</Text>
                <TouchableOpacity
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#F2F2F7',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Icon name="xmark" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              <CustomCalendar
                initialDate={loanDate.toDate()}
                onSelectDate={date => {
                  setLoanDate(dayjs(date));
                }}
              />
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Text style={styles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={() => setShowLoanDateModal(false)}>
                  <Text style={styles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
      {/* 还款时间选择弹窗 */}
      <Modal
        visible={showRepayDateModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowRepayDateModal(false)}>
        <TouchableWithoutFeedback onPress={() => setShowRepayDateModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, styles.dateModalContent]}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择还款日期</Text>
                <TouchableOpacity
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#F2F2F7',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Icon name="xmark" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              <CustomCalendar
                initialDate={repayDate.toDate()}
                onSelectDate={date => {
                  setRepayDate(dayjs(date));
                }}
              />
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Text style={styles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={() => setShowRepayDateModal(false)}>
                  <Text style={styles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* 删除借款确认弹窗 */}
      <ConfirmDialog
        visible={showDeleteDialog}
        title="删除借款记录"
        message="确定要删除该借款记录吗？此操作不可恢复。"
        onCancel={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  // 头部卡片样式
  headerCard: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
  },
  formContainer: {
    padding: 16,
    paddingTop: 8,
    paddingBottom: 40,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 10,
    letterSpacing: -0.3,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  // 卡片头部样式
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  cardTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginLeft: 8,
    letterSpacing: -0.4,
  },
  // 输入框样式
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    padding: 0,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  selectorText: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    marginLeft: 12,
    fontWeight: '500',
  },
  // 类型按钮样式
  typeBtns: {
    flexDirection: 'row',
    gap: 12,
  },
  typeBtn: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: COLORS.background.light,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  typeBtnActive: {
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  typeBtnIcon: {
    marginRight: 8,
  },
  typeBtnText: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.secondary,
    letterSpacing: -0.3,
  },
  typeBtnTextActive: {
    color: '#FFFFFF',
  },
  // 还款相关样式
  repayInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  repayAmountContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  repayInputIcon: {
    marginRight: 8,
  },
  repayAmountInput: {
    flex: 1,
    fontSize: 15,
    color: COLORS.text.primary,
    padding: 0,
  },
  repayDateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    minWidth: 80,
  },
  repayDateText: {
    fontSize: 14,
    color: COLORS.text.primary,
    marginLeft: 6,
    fontWeight: '500',
  },
  addRepayBtn: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // 还款记录列表样式
  repaymentsList: {
    gap: 8,
  },
  repayItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.background.light,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  repayItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  repayItemInfo: {
    marginLeft: 10,
    flex: 1,
  },
  repayText: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '600',
    marginBottom: 2,
  },
  repayDate: {
    fontSize: 13,
    color: COLORS.text.gray,
    fontWeight: '400',
  },
  deleteRepayBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFE5E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 统计信息样式
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '400',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
    letterSpacing: -0.5,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 16,
  },
  // 操作按钮样式
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  deleteBtn: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    backgroundColor: '#FF3B30',
    shadowColor: '#FF3B30',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  deleteBtnText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  saveBtn: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  saveBtnWithDelete: {
    flex: 2,
  },
  saveBtnText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
  buttonIcon: {
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '92%',
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  dateModalContent: {
    width: '92%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5EA',
  },
  cancelButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0,
    backgroundColor: '#F2F2F7',
    borderRadius: 14,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 17,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    marginLeft: 8,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
  },
});

export default LoanRecordEditScreen;
