import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import PageContainer from '../components/PageContainer';
import databaseService, {AccountBook} from '../services/DatabaseService';
import {useToast} from '../context/ToastContext';
import {useFocusEffect} from '@react-navigation/native';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import ConfirmDialog from '../components/ConfirmDialog';

const {width: screenWidth} = Dimensions.get('window');

const MyAccountBooksScreen = ({navigation}) => {
  const {colors} = useTheme();
  const {showToast} = useToast();

  const [accountBooks, setAccountBooks] = useState<AccountBook[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBook, setEditingBook] = useState<AccountBook | null>(null);
  const [bookName, setBookName] = useState('');
  const [bookDescription, setBookDescription] = useState('');
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [bookToDelete, setBookToDelete] = useState<AccountBook | null>(null);

  // 加载账本列表
  const loadAccountBooks = async () => {
    try {
      setLoading(true);
      const books = await databaseService.getAllAccountBooks();
      console.log('账本列表:', books);
      setAccountBooks(books);
    } catch (error) {
      console.error('加载账本列表失败:', error);
      showToast('加载账本列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 页面获得焦点时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadAccountBooks();
    }, []),
  );

  // 打开新增/编辑弹窗
  const openModal = (book?: AccountBook) => {
    if (book) {
      setEditingBook(book);
      setBookName(book.name);
      setBookDescription(book.description || '');
    } else {
      setEditingBook(null);
      setBookName('');
      setBookDescription('');
    }
    setModalVisible(true);
  };

  // 关闭弹窗
  const closeModal = () => {
    setModalVisible(false);
    setEditingBook(null);
    setBookName('');
    setBookDescription('');
  };

  // 保存账本
  const saveAccountBook = async () => {
    if (!bookName.trim()) {
      showToast('请输入账本名称', 'warning');
      return;
    }

    try {
      if (editingBook) {
        // 编辑账本
        await databaseService.updateAccountBook({
          ...editingBook,
          name: bookName.trim(),
          description: bookDescription.trim(),
        });
        showToast('账本更新成功', 'success');
      } else {
        // 新增账本
        await databaseService.addAccountBook({
          name: bookName.trim(),
          description: bookDescription.trim(),
          isDefault: false,
        });
        showToast('账本创建成功', 'success');
      }
      
      closeModal();
      loadAccountBooks();
    } catch (error) {
      console.error('保存账本失败:', error);
      showToast('保存账本失败', 'error');
    }
  };

  // 删除账本
  const deleteAccountBook = async () => {
    if (!bookToDelete) return;

    try {
      await databaseService.deleteAccountBook(bookToDelete.id!);
      showToast('账本删除成功', 'success');
      setConfirmDialogVisible(false);
      setBookToDelete(null);
      loadAccountBooks();
    } catch (error) {
      console.error('删除账本失败:', error);
      showToast(error.message || '删除账本失败', 'error');
      setConfirmDialogVisible(false);
      setBookToDelete(null);
    }
  };

  // 确认删除
  const confirmDelete = (book: AccountBook) => {
    setBookToDelete(book);
    setConfirmDialogVisible(true);
  };

  // 查看账本账单
  const viewAccountBookBills = (book: AccountBook) => {
    navigation.navigate('bills', {
      accountBookId: book.id,
      accountBookName: book.name,
    });
  };

  // 获取账本图标颜色
  const getBookIconColor = (index: number) => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
    return colors[index % colors.length];
  };

  // 获取账本渐变色
  const getBookGradient = (index: number) => {
    const gradients = [
      ['#FF6B6B', '#FF8E8E'],
      ['#4ECDC4', '#44A08D'],
      ['#45B7D1', '#2196F3'],
      ['#96CEB4', '#85C1A3'],
      ['#FFEAA7', '#FDCB6E'],
      ['#DDA0DD', '#D63384'],
      ['#98D8C8', '#74B9FF'],
      ['#F7DC6F', '#F39C12'],
    ];
    return gradients[index % gradients.length];
  };

  // 渲染账本项
  const renderAccountBookItem = ({item, index}: {item: AccountBook; index: number}) => (
    <TouchableOpacity
      style={styles.bookItem}
      onPress={() => viewAccountBookBills(item)}
      activeOpacity={0.9}>

      {/* 渐变背景 */}
      <LinearGradient
        colors={getBookGradient(index)}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={styles.bookGradient}>

        {/* 装饰性图案 */}
        <View style={styles.decorativePattern}>
          <View style={[styles.decorativeCircle, styles.circle1]} />
          <View style={[styles.decorativeCircle, styles.circle2]} />
          <View style={[styles.decorativeCircle, styles.circle3]} />
        </View>

        {/* 主要内容 */}
        <View style={styles.bookContent}>
          <View style={styles.bookHeader}>
            <View style={styles.bookIconContainer}>
              <Icon
                name="book"
                size={24}
                color="rgba(255, 255, 255, 0.9)"
              />
            </View>

            <View style={styles.bookInfo}>
              <View style={styles.bookTitleRow}>
                <Text style={styles.bookName}>{item.name}</Text>
                {item.isDefault && (
                  <View style={styles.defaultBadge}>
                    <Icon name="star" size={10} color="#FFD700" />
                    <Text style={styles.defaultBadgeText}>默认</Text>
                  </View>
                )}
              </View>
              {item.description && (
                <Text style={styles.bookDescription} numberOfLines={2}>
                  {item.description}
                </Text>
              )}
            </View>
          </View>

          {/* 底部操作区域 */}
          <View style={styles.bookFooter}>
            <View style={styles.bookStats}>
              <Text style={styles.bookStatsText}>点击查看账单</Text>
            </View>

            <View style={styles.bookActions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={(e) => {
                  e.stopPropagation();
                  openModal(item);
                }}
                activeOpacity={0.7}>
                <Icon name="pen" size={14} color="rgba(255, 255, 255, 0.9)" />
              </TouchableOpacity>

              {!item.isDefault && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.deleteButton]}
                  onPress={(e) => {
                    e.stopPropagation();
                    confirmDelete(item);
                  }}
                  activeOpacity={0.7}>
                  <Icon name="trash" size={14} color="rgba(255, 255, 255, 0.9)" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.emptyGradient}>
        <Icon name="book-open" size={64} color="rgba(255, 255, 255, 0.8)" />
        <Text style={styles.emptyText}>还没有账本</Text>
        <Text style={styles.emptySubText}>创建您的第一个账本，开始记账之旅</Text>
        <TouchableOpacity
          style={styles.emptyActionButton}
          onPress={() => openModal()}
          activeOpacity={0.8}>
          <Icon name="plus" size={16} color="#667eea" />
          <Text style={styles.emptyActionText}>创建账本</Text>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );

  return (
    <PageContainer
      headerTitle="我的账本"
      backgroundColor={COLORS.background.light}
      rightComponent={
        <TouchableOpacity
          style={[styles.addButton, {backgroundColor: colors.primary}]}
          onPress={() => openModal()}
          activeOpacity={0.8}>
          <Icon name="plus" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      }>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={accountBooks}
          renderItem={({item, index}) => renderAccountBookItem({item, index})}
          keyExtractor={item => item.id?.toString() || ''}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* 新增/编辑弹窗 */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingBook ? '编辑账本' : '新增账本'}
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <Icon name="xmark" size={18} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>账本名称</Text>
              <TextInput
                style={styles.textInput}
                value={bookName}
                onChangeText={setBookName}
                placeholder="请输入账本名称"
                placeholderTextColor={COLORS.text.placeholder}
                maxLength={20}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>账本描述（可选）</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={bookDescription}
                onChangeText={setBookDescription}
                placeholder="请输入账本描述"
                placeholderTextColor={COLORS.text.placeholder}
                multiline={true}
                numberOfLines={3}
                maxLength={100}
              />
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={closeModal}>
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton, {backgroundColor: colors.primary}]}
                onPress={saveAccountBook}>
                <Text style={styles.saveButtonText}>保存</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="删除账本"
        message={`确定要删除账本"${bookToDelete?.name}"吗？删除后无法恢复。`}
        onCancel={() => {
          setConfirmDialogVisible(false);
          setBookToDelete(null);
        }}
        onConfirm={deleteAccountBook}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  bookItem: {
    marginBottom: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  bookGradient: {
    borderRadius: 16,
    padding: 20,
    minHeight: 140,
    position: 'relative',
  },
  decorativePattern: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100%',
    height: '100%',
  },
  decorativeCircle: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 50,
  },
  circle1: {
    width: 80,
    height: 80,
    top: -20,
    right: -20,
  },
  circle2: {
    width: 40,
    height: 40,
    top: 20,
    right: 60,
  },
  circle3: {
    width: 60,
    height: 60,
    bottom: -10,
    right: 20,
  },
  bookContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  bookHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  bookIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  bookInfo: {
    flex: 1,
  },
  bookTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  bookName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
    marginRight: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  defaultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultBadgeText: {
    fontSize: 10,
    color: '#333',
    fontWeight: '600',
    marginLeft: 4,
  },
  bookDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 1,
  },
  bookFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bookStats: {
    flex: 1,
  },
  bookStatsText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },
  bookActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    paddingHorizontal: 12,
  },
  viewButtonText: {
    fontSize: 12,
    color: '#007AFF',
    marginLeft: 4,
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  deleteButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingTop: 60,
  },
  emptyGradient: {
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    width: '100%',
    maxWidth: 300,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 20,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emptyActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#667eea',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 16,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.border.light,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: COLORS.text.primary,
    backgroundColor: COLORS.background.light,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: COLORS.background.light,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: COLORS.text.gray,
    fontWeight: '500',
  },
  saveButton: {
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default MyAccountBooksScreen;
