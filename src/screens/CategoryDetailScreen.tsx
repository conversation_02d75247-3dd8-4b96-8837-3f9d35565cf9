import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {Transaction} from '../services/DatabaseService';
import {
  VictoryLine,
  VictoryChart,
  VictoryAxis,
  VictoryScatter,
  VictoryLabel,
} from 'victory-native';
import {COLORS} from '../utils/color';
import {useFocusEffect} from '@react-navigation/native';
import {useTheme} from '../context/ThemeContext';
const {width} = Dimensions.get('window');

const CategoryDetailScreen = ({route, navigation}) => {
  const {
    categoryId,
    categoryName,
    categoryIcon,
    categoryColor,
    year,
    month,
    viewType,
    familyId,
  } = route.params;
  const {colors} = useTheme();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [transactionCount, setTransactionCount] = useState(0);
  const [dailyStats, setDailyStats] = useState<
    {date: string; expense: number}[]
  >([]);
  const [yearMonthPickerVisible, setYearMonthPickerVisible] = useState(false);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [selectedMonth, setSelectedMonth] = useState(month);
  const [selectedYear, setSelectedYear] = useState(year);
  const [selectedDayIndex, setSelectedDayIndex] = useState(-1);
  const [selectedPointIndex, setSelectedPointIndex] = useState<number | null>(
    null,
  );
  const [tempYear, setTempYear] = useState(year);
  const [selectedMonthIndex, setSelectedMonthIndex] = useState<number | null>(
    null,
  );
  const [maxExpenseMonth, setMaxExpenseMonth] = useState<{
    month: number;
    expense: number;
  } | null>(null);

  // 月份名称
  const monthNames = [
    '一月',
    '二月',
    '三月',
    '四月',
    '五月',
    '六月',
    '七月',
    '八月',
    '九月',
    '十月',
    '十一月',
    '十二月',
  ];

  // 加载数据
  const loadData = async () => {
    try {
      // 获取所有可用的年份
      const years = await databaseService.getTransactionYears(familyId);
      const currentYear = new Date().getFullYear();
      const recentYears = [];
      for (let i = 0; i < 10; i++) {
        recentYears.push(currentYear - i);
      }
      const allYears = [...new Set([...years, ...recentYears])].sort(
        (a, b) => b - a,
      );
      setAvailableYears(allYears);

      // 获取该类别的月度交易记录
      const categoryTransactions =
        await databaseService.getTransactionsByCategoryAndMonth(
          categoryId,
          selectedYear,
          selectedMonth,
          familyId,
        );

      // 根据 viewType 过滤交易记录
      const filteredTransactions = categoryTransactions.filter(
        t => t.type === viewType,
      );

      setTransactions(filteredTransactions);

      // 计算总金额和交易笔数
      const total = filteredTransactions.reduce((sum, t) => sum + t.amount, 0);
      setTotalAmount(total);
      setTransactionCount(filteredTransactions.length);

      // 计算日消费数据
      const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
      const dailyExpenses = Array.from({length: daysInMonth}, (_, i) => {
        const day = i + 1;
        const date = `${selectedYear}-${selectedMonth
          .toString()
          .padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        return {date, expense: 0};
      });

      // 计算每日支出
      filteredTransactions.forEach(transaction => {
        const dateIndex = parseInt(transaction.date.split('-')[2]) - 1;
        if (dateIndex >= 0 && dateIndex < dailyExpenses.length) {
          dailyExpenses[dateIndex].expense += transaction.amount;
        }
      });

      setDailyStats(dailyExpenses);

      // 找出最高消费月份
      const maxExpense = Math.max(
        ...dailyExpenses.map(item => item.expense),
        0,
      );
      const maxExpenseMonth = dailyExpenses.find(
        item => item.expense === maxExpense,
      );
      setMaxExpenseMonth(maxExpenseMonth);
    } catch (error) {
      console.error('加载类别详情失败', error);
    }
  };

  // 使用 useFocusEffect 替代 useEffect 来加载数据
  useFocusEffect(
    React.useCallback(() => {
      console.log('CategoryDetailScreen 获得焦点，重新加载数据');
      loadData();
      return () => {
        // 可选的清理函数
      };
    }, [selectedYear, selectedMonth, categoryId, viewType]),
  );

  // 处理交易记录点击
  const handleTransactionPress = (transaction: Transaction) => {
    navigation.navigate('addTransaction', {transaction});
  };

  // 渲染交易记录项
  const renderTransactionItem = ({item}: {item: Transaction}) => {
    const isExpense = item.type === 'expense';

    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => handleTransactionPress(item)}
        activeOpacity={0.7}>
        <View style={styles.transactionLeft}>
          <View style={[styles.categoryIcon, {backgroundColor: categoryColor}]}>
            <Icon name={categoryIcon} size={22} color="#FFFFFF" />
          </View>
          <View style={styles.transactionInfo}>
            <Text style={styles.categoryName}>{categoryName}</Text>
            {item.note ? (
              <Text style={styles.transactionNote} numberOfLines={1}>
                {item.note}
              </Text>
            ) : null}
            <Text style={styles.transactionDate}>
              {item.date.split('-')[2]}日
            </Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            {item.familyName ? (
              <View
                style={[
                  styles.familyNameBadge,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.familyName}>{item.familyName}</Text>
              </View>
            ) : null}
            {item.isReimbursable && (
              <View
                style={[
                  styles.reimbursableTag,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.reimbursableText}>报销账单</Text>
              </View>
            )}
          </View>
        </View>
        <Text style={[styles.transactionAmount]}>
          {isExpense ? '-￥' : '+￥'}
          {item.amount.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  };

  // 添加年月选择器渲染函数
  const renderYearMonthSelector = () => (
    <TouchableOpacity
      style={styles.yearMonthSelector}
      onPress={() => setYearMonthPickerVisible(true)}
      activeOpacity={0.7}>
      <Text style={styles.yearMonthSelectorText} numberOfLines={1}>
        {selectedYear}年{selectedMonth}月
      </Text>
      <Icon
        name="chevron-down"
        size={14}
        color="#666666"
        style={styles.yearMonthSelectorIcon}
      />
    </TouchableOpacity>
  );

  // 修改年月选择器弹窗
  const renderYearMonthPicker = () => {
    // 打开选择器时，初始化临时年份
    useEffect(() => {
      if (yearMonthPickerVisible) {
        setTempYear(selectedYear);
      }
    }, [yearMonthPickerVisible]);

    return (
      <Modal
        visible={yearMonthPickerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setYearMonthPickerVisible(false)}>
        <TouchableWithoutFeedback
          onPress={() => setYearMonthPickerVisible(false)}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>选择年月</Text>
                <TouchableOpacity
                  style={styles.closeButtonContainer}
                  onPress={() => setYearMonthPickerVisible(false)}>
                  <Text style={styles.closeButton}>关闭</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.yearMonthPickerContent}>
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerColumnTitle}>年份</Text>
                  <FlatList
                    data={availableYears}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={[
                          styles.pickerOption,
                          tempYear === item && styles.selectedPickerOption,
                        ]}
                        onPress={() => {
                          // 只更新临时年份状态
                          setTempYear(item);
                        }}>
                        <Text
                          style={[
                            styles.pickerOptionText,
                            tempYear === item &&
                              styles.selectedPickerOptionText,
                          ]}>
                          {item}年
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={styles.pickerList}
                    showsVerticalScrollIndicator={false}
                  />
                </View>

                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerColumnTitle}>月份</Text>
                  <FlatList
                    data={Array.from({length: 12}, (_, i) => i + 1)}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={[
                          styles.pickerOption,
                          selectedMonth === item && styles.selectedPickerOption,
                        ]}
                        onPress={() => {
                          // 选择月份时，一次性更新年份和月份，然后加载数据
                          setSelectedYear(tempYear);
                          setSelectedMonth(item);
                          setYearMonthPickerVisible(false);
                          // 使用新的年份和月份加载数据
                          loadData();
                        }}>
                        <Text
                          style={[
                            styles.pickerOptionText,
                            selectedMonth === item &&
                              styles.selectedPickerOptionText,
                          ]}>
                          {item}月
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={styles.pickerList}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  };

  // 渲染类别统计卡片
  const renderCategorySummary = () => (
    <View style={styles.summaryCard}>
      <View style={styles.summaryHeader}>
        <View
          style={[styles.categoryIconLarge, {backgroundColor: categoryColor}]}>
          <Icon name={categoryIcon} size={30} color="#FFFFFF" />
        </View>
        <Text style={styles.summaryTitle}>{categoryName}</Text>
      </View>
      <View style={styles.summaryContent}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>总支出</Text>
          <Text style={styles.summaryValue}>￥{totalAmount.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>交易笔数</Text>
          <Text style={styles.summaryValue}>{transactionCount}笔</Text>
        </View>
      </View>
    </View>
  );

  // 修改 renderTrendChart 函数
  const renderTrendChart = () => {
    if (!dailyStats || dailyStats.length === 0) {
      return (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>月度趋势</Text>
          <View style={styles.emptyChartContainer}>
            <Text style={styles.emptyChartText}>暂无数据</Text>
          </View>
        </View>
      );
    }

    // 准备 Victory 折线图数据
    const chartData = dailyStats.map(item => ({
      x: parseInt(item.date.split('-')[2]),
      y: item.expense,
      month: parseInt(item.date.split('-')[1]),
      value: item.expense,
    }));

    // 找出最大值，用于设置图表的 domain
    const maxValue = Math.max(...dailyStats.map(item => item.expense), 0);
    // 为了让图表更美观，最大值增加一点余量
    const yDomain = [0, maxValue * 1.1 || 10];

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>月度趋势</Text>
        <View style={styles.victoryLineContainer}>
          <VictoryChart
            width={width - 40}
            height={220}
            padding={{top: 30, bottom: 40, left: 50, right: 20}}
            domainPadding={{x: 10, y: 10}}>
            <VictoryAxis
              tickFormat={t => `${t}日`}
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {fontSize: 10, fill: '#333333'},
                grid: {stroke: 'transparent'},
              }}
            />
            <VictoryAxis
              dependentAxis
              domain={yDomain}
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {fontSize: 10, fill: '#333333'},
                grid: {stroke: '#EEEEEE'},
              }}
              tickFormat={t => `￥${t}`}
            />
            <VictoryLine
              data={chartData}
              x="x"
              y="y"
              style={{
                data: {
                  stroke: categoryColor,
                  strokeWidth: 2,
                },
              }}
            />
            <VictoryScatter
              data={chartData}
              x="x"
              y="y"
              size={props => {
                // 检查是否是选中的点或最高点
                const isSelected = props.index === selectedMonthIndex;
                const isMaxPoint = props.datum.month === maxExpenseMonth.month;
                return isSelected || isMaxPoint ? 6 : 4;
              }}
              style={{
                data: {
                  fill: categoryColor,
                  stroke: '#FFFFFF',
                  strokeWidth: 2,
                },
              }}
              labels={props => {
                // 只有当点被选中时才显示标签
                if (props.index === selectedMonthIndex) {
                  return `￥${props.datum.y.toFixed(2)}`;
                }
                return null;
              }}
              labelComponent={
                <VictoryLabel
                  dy={-15}
                  style={{
                    fontSize: 12,
                    fill: '#333333',
                    fontWeight: 'bold',
                  }}
                  backgroundStyle={{
                    fill: 'white',
                    stroke: '#CCCCCC',
                    strokeWidth: 1,
                    rx: 3,
                  }}
                  backgroundPadding={{top: 5, bottom: 3, left: 10, right: 10}}
                />
              }
              events={[
                {
                  target: 'data',
                  eventHandlers: {
                    // 使用多种事件类型来确保在不同设备上都能响应
                    onPress: (_, props) => {
                      setSelectedMonthIndex(props.index);
                      return null;
                    },
                    onPressIn: (_, props) => {
                      setSelectedMonthIndex(props.index);
                      return null;
                    },
                    onTouchStart: (_, props) => {
                      setSelectedMonthIndex(props.index);
                      return null;
                    },
                  },
                },
              ]}
            />
          </VictoryChart>
        </View>
      </View>
    );
  };

  return (
    <PageContainer
      headerTitle={`${categoryName}账单`}
      rightComponent={renderYearMonthSelector()}
      backgroundColor={COLORS.secondary}>
      <ScrollView style={styles.container}>
        {renderCategorySummary()}
        {renderTrendChart()}

        <View style={styles.transactionsContainer}>
          <Text style={styles.sectionTitle}>交易记录</Text>
          {transactions.length > 0 ? (
            <FlatList
              data={transactions}
              renderItem={renderTransactionItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.transactionsList}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>暂无交易记录</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {renderYearMonthPicker()}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  summaryCard: {
    margin: 15,
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    padding: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  categoryIconLarge: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text.primary,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text.primary,
  },
  chartContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    margin: 15,
    padding: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    // marginBottom: 15,
    textAlign: 'center',
  },
  emptyChartContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyChartText: {
    fontSize: 16,
    color: COLORS.text.gray,
  },
  transactionsContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    margin: 15,
    padding: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 15,
  },
  transactionsList: {
    paddingBottom: 10,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  transactionNote: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: COLORS.text.gray,
  },
  familyNameBadge: {
    marginRight: 20,
    backgroundColor: '#FFF9C4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  familyName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  reimbursableTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 10, // 与金额的间距
  },
  reimbursableText: {
    fontSize: 12,
    color: '#FFFFFF', // 待报销标签的文字颜色
    fontWeight: '600',
  },
  transactionAmount: {
    width: 100,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  expenseText: {
    color: '#FF3B30',
  },
  incomeText: {
    color: '#34C759',
  },
  emptyContainer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.text.gray,
  },
  yearMonthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
    maxWidth: 120,
  },
  yearMonthSelectorText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginRight: 5,
    flex: 1,
  },
  yearMonthSelectorIcon: {
    marginLeft: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
  },
  closeButtonContainer: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
  },
  closeButton: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
  },
  yearMonthPickerContent: {
    flexDirection: 'row',
    padding: 15,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerColumnTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    textAlign: 'center',
    marginBottom: 10,
  },
  pickerList: {
    maxHeight: 250,
  },
  pickerOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    alignItems: 'center',
  },
  selectedPickerOption: {
    backgroundColor: '#F0F8FF',
  },
  pickerOptionText: {
    fontSize: 16,
    color: COLORS.text.primary,
    textAlign: 'center',
  },
  selectedPickerOptionText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  victoryLineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    height: 220,
  },
});

export default CategoryDetailScreen;
