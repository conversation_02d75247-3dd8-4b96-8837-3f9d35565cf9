import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import PageContainer from '../components/PageContainer';
import databaseService, {LoanRecord} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import dayjs from 'dayjs';

const LoanRecordsScreen = ({navigation}) => {
  const {colors} = useTheme();
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState<LoanRecord[]>([]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadRecords();
    });
    loadRecords();
    return unsubscribe;
  }, [navigation]);

  const loadRecords = async () => {
    setLoading(true);
    try {
      const data = await databaseService.getAllLoanRecords();
      setRecords(data || []);
    } catch (e) {
      setRecords([]);
    }
    setLoading(false);
  };

  const renderItem = ({item}) => {
    const totalRepaid =
      item.repayments?.reduce((sum, r) => sum + Number(r.amount), 0) || 0;
    const remainingAmount = Number(item.amount) - totalRepaid;
    const isFullyPaid = remainingAmount <= 0;

    // 获取类型配置
    const getTypeConfig = (type: string) => {
      return type === 'lend'
        ? {
            icon: 'hand-holding-dollar',
            color: '#34C759',
            bgColor: '#E8F5E8',
            label: '借出',
            gradientColors: ['#34C759', '#28A745'],
          }
        : {
            icon: 'hand-holding-heart',
            color: '#FF9500',
            bgColor: '#FFF3E0',
            label: '借入',
            gradientColors: ['#FF9500', '#FF8C00'],
          };
    };

    const typeConfig = getTypeConfig(item.type);

    return (
      <TouchableOpacity
        style={styles.item}
        activeOpacity={0.8}
        onPress={() => navigation.navigate('loanRecordDetail', {id: item.id})}>

        {/* 左侧图标和信息 */}
        <View style={styles.itemLeft}>
          <LinearGradient
            colors={typeConfig.gradientColors}
            style={styles.iconCircle}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}>
            <Icon
              name={typeConfig.icon}
              size={18}
              color="#FFFFFF"
            />
          </LinearGradient>

          <View style={styles.itemInfo}>
            <View style={styles.itemTitleRow}>
              <Text style={styles.itemTitle}>
                {typeConfig.label} ￥{item.amount}
              </Text>
              {isFullyPaid && (
                <View style={styles.paidBadge}>
                  <Icon name="check" size={10} color="#FFFFFF" />
                  <Text style={styles.paidText}>已还清</Text>
                </View>
              )}
            </View>
            <Text style={styles.itemNote} numberOfLines={1}>
              {item.note || '无备注'}
            </Text>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min((totalRepaid / Number(item.amount)) * 100, 100)}%`,
                      backgroundColor: isFullyPaid ? '#34C759' : typeConfig.color,
                    },
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {((totalRepaid / Number(item.amount)) * 100).toFixed(0)}%
              </Text>
            </View>
          </View>
        </View>

        {/* 右侧日期和金额信息 */}
        <View style={styles.itemRight}>
          <Text style={styles.itemDate}>
            {dayjs(item.loanDate).format('MM/DD')}
          </Text>
          <Text style={[styles.itemAmount, {color: typeConfig.color}]}>
            ￥{totalRepaid}
          </Text>
          <Text style={styles.itemRemaining}>
            剩余 ￥{remainingAmount.toFixed(2)}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <PageContainer headerTitle="借款记录" backgroundColor={COLORS.background.light}>
      <View style={styles.container}>
        {loading ? (
          <ActivityIndicator
            size="large"
            color={colors.primary}
            style={styles.loader}
          />
        ) : (
          <FlatList
            data={records}
            renderItem={renderItem}
            keyExtractor={item =>
              item.id?.toString() || Math.random().toString()
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <View style={styles.emptyIconContainer}>
                  <Icon name="money-check-dollar" size={64} color="#E5E5EA" />
                </View>
                <Text style={styles.emptyText}>暂无借款记录</Text>
                <Text style={styles.emptySubText}>
                  记录您的借入借出信息{'\n'}让资金往来更清晰
                </Text>
                <TouchableOpacity
                  style={[styles.addButton, {backgroundColor: colors.primary}]}
                  onPress={() => navigation.navigate('loanRecordEdit')}
                  activeOpacity={0.8}>
                  <Icon name="plus" size={16} color="#FFFFFF" style={styles.addButtonIcon} />
                  <Text style={styles.addButtonText}>添加借款记录</Text>
                </TouchableOpacity>
              </View>
            }
            contentContainerStyle={records.length === 0 && styles.emptyList}
          />
        )}
        <TouchableOpacity
          style={[styles.addBtn, {backgroundColor: colors.primary}]}
          activeOpacity={0.85}
          onPress={() =>
            navigation.navigate('loanRecordEdit', {onSaved: loadRecords})
          }>
          <Icon name="plus" size={22} color="#fff" />
        </TouchableOpacity>
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 80,
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 2,
    borderColor: '#F2F2F7',
    borderStyle: 'dashed',
  },
  emptyText: {
    fontSize: 22,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 12,
    letterSpacing: -0.6,
  },
  emptySubText: {
    fontSize: 16,
    color: COLORS.text.gray,
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.4,
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 20,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: {width: 0, height: 4},
    elevation: 6,
  },
  iconCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  itemInfo: {
    flex: 1,
    marginLeft: 16,
  },
  itemTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  itemTitle: {
    fontSize: 17,
    fontWeight: '700',
    color: COLORS.text.primary,
    letterSpacing: -0.4,
    flex: 1,
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#34C759',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginLeft: 8,
  },
  paidText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 3,
    letterSpacing: -0.1,
  },
  itemNote: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 12,
    lineHeight: 20,
    letterSpacing: -0.2,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#F2F2F7',
    borderRadius: 3,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.text.gray,
    letterSpacing: -0.1,
  },
  itemRight: {
    alignItems: 'flex-end',
    marginLeft: 16,
  },
  itemDate: {
    fontSize: 13,
    color: COLORS.text.gray,
    fontWeight: '500',
    marginBottom: 6,
    letterSpacing: -0.1,
  },
  itemAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  itemRemaining: {
    fontSize: 12,
    color: COLORS.text.gray,
    fontWeight: '500',
    letterSpacing: -0.1,
  },
  addBtn: {
    position: 'absolute',
    right: 24,
    bottom: 32,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 4},
  },
});

export default LoanRecordsScreen;
