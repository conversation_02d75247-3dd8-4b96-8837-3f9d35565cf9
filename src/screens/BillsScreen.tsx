import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView,
  Dimensions,
  TextInput,
  TouchableWithoutFeedback,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useFocusEffect} from '@react-navigation/native';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {
  VictoryPie,
  VictoryTheme,
  VictoryLine,
  VictoryChart,
  VictoryAxis,
  VictoryScatter,
  VictoryLabel,
} from 'victory-native';
import {COLORS} from '../utils/color';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import {
  analyzeConsumptionBehavior,
  calculateHealthScore,
  generateSmartSuggestions,
} from '../utils/algorithm';
import {useTheme} from '../context/ThemeContext';
const {width} = Dimensions.get('window');

interface MonthData {
  month: number;
  income: number;
  expense: number;
  balance: number;
}

const BillsScreen = ({navigation, route}) => {
  const {colors} = useTheme();
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [yearPickerVisible, setYearPickerVisible] = useState(false);
  const [monthlyData, setMonthlyData] = useState<MonthData[]>([]);
  const [yearlyTotal, setYearlyTotal] = useState({
    income: 0,
    expense: 0,
    balance: 0,
  });
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [pieData, setPieData] = useState([]);

  // 添加搜索相关状态
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // 添加账单分析相关状态
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const analysisSheetPosition = useSharedValue(Dimensions.get('window').height);

  // 添加一个状态来跟踪是否已经搜索过
  const [hasSearched, setHasSearched] = useState(false);

  // 1. 首先，在组件顶部添加分页相关状态
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreResults, setHasMoreResults] = useState(true);
  const PAGE_SIZE = 30; // 每页30条，可以调整

  // 添加筛选相关状态
  const [filterVisible, setFilterVisible] = useState(false);
  const [isReimbursableFilter, setIsReimbursableFilter] = useState(false);

  // 从路由参数中获取账本信息
  const accountBookId = route.params?.accountBookId;
  const accountBookName = route.params?.accountBookName;
  // 保持对家庭账单的兼容性
  const showFamilyBillsOnly = route.params?.showFamilyBillsOnly || false;
  const familyId = route.params?.familyId;
  const familyName = route.params?.familyName;

  useEffect(() => {
    if (isSearching) {
      getSearchResults();
    }
  }, [isSearching]);

  // 加载数据
  const loadData = async () => {
    try {
      // 获取所有有交易记录的年份，支持账本筛选
      const filterParam = accountBookId || familyId;
      const transactionYears = await databaseService.getTransactionYears(
        filterParam,
      );

      // 获取当前年份
      const currentYear = new Date().getFullYear();

      // 创建一个包含最近5年的数组（包括当前年份）
      const recentYears = [];
      for (let i = 0; i < 5; i++) {
        recentYears.push(currentYear - i);
      }

      // 合并交易年份和最近年份，并去重排序
      const allYears = [...new Set([...transactionYears, ...recentYears])].sort(
        (a, b) => b - a,
      );

      setAvailableYears(allYears);

      // 如果选择的年份不在可用年份中，设置为当前年份
      if (!allYears.includes(selectedYear)) {
        setSelectedYear(currentYear);
      }

      // 获取选定年份的月度数据
      const months: MonthData[] = [];
      let totalIncome = 0;
      let totalExpense = 0;

      // 获取每个月的数据
      for (let month = 1; month <= 12; month++) {
        const {income, expense} = await databaseService.getMonthlyStatistics(
          selectedYear,
          month,
          filterParam,
        );

        const balance = income - expense;
        months.push({month, income, expense, balance});

        totalIncome += income;
        totalExpense += expense;
      }

      setMonthlyData(months);
      setYearlyTotal({
        income: totalIncome,
        expense: totalExpense,
        balance: totalIncome - totalExpense,
      });

      // 设置饼图数据，触发动画
      if (totalIncome > 0 || totalExpense > 0) {
        const newPieData = [
          {
            name: '收入',
            population: totalIncome || 0.01,
            color: '#4CAF50',
            legendFontColor: '#333333',
            legendFontSize: 14,
          },
          {
            name: '支出',
            population: totalExpense || 0.01,
            color: '#FF3B30',
            legendFontColor: '#333333',
            legendFontSize: 14,
          },
        ];

        // 清空数据后重新设置，触发动画效果
        setPieData([]);
        setTimeout(() => setPieData(newPieData), 300);
      }
    } catch (error) {
      console.error('加载账单数据失败', error);
    }
  };

  // 在页面获得焦点时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadData();
    }, [selectedYear]),
  );

  // 处理月份点击
  const handleMonthPress = (month: number) => {
    navigation.navigate('monthDetail', {
      year: selectedYear,
      month,
      familyId,
      accountBookId,
    });
  };

  // 处理年份选择
  const handleYearSelect = (year: number) => {
    setSelectedYear(year);
    setYearPickerVisible(false);
  };

  // 渲染月份项
  const renderMonthItem = ({item}: {item: MonthData}) => {
    const monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];

    return (
      <TouchableOpacity
        style={styles.tableRow}
        onPress={() => handleMonthPress(item.month)}
        activeOpacity={0.7}>
        <View style={styles.tableRowContent}>
          <View style={styles.monthColumn}>
            <Text style={styles.monthText}>{monthNames[item.month - 1]}</Text>
            <Text style={styles.monthTransactionCount}>共计账单</Text>
          </View>

          <View style={styles.amountColumns}>
            <View style={styles.amountColumn}>
              <Text style={styles.amountLabel}>收入</Text>
              <Text style={styles.incomeText}>￥{item.income.toFixed(2)}</Text>
            </View>

            <View style={styles.amountColumn}>
              <Text style={styles.amountLabel}>支出</Text>
              <Text style={styles.expenseText}>
                ￥{item.expense.toFixed(2)}
              </Text>
            </View>

            <View style={styles.amountColumn}>
              <Text style={styles.amountLabel}>结余</Text>
              <Text
                style={[
                  item.balance >= 0 ? styles.incomeText : styles.expenseText,
                ]}>
                ￥{item.balance.toFixed(2)}
              </Text>
            </View>
          </View>

          <Icon name="chevron-right" size={16} color="#C7C7CC" />
        </View>
      </TouchableOpacity>
    );
  };

  // 渲染年份选择器
  const renderYearPicker = () => (
    <Modal
      visible={yearPickerVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setYearPickerVisible(false)}>
      <TouchableWithoutFeedback onPress={() => setYearPickerVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.yearPickerContainer}>
            <View style={styles.yearPickerHeader}>
              <Text style={styles.yearPickerTitle}>选择年份</Text>
              <TouchableOpacity
                style={styles.closeButtonContainer}
                onPress={() => setYearPickerVisible(false)}>
                <Text style={styles.closeButton}>关闭</Text>
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.yearList}>
              {availableYears.map(year => (
                <TouchableOpacity
                  key={year}
                  style={[
                    styles.yearOption,
                    selectedYear === year && styles.selectedYearOption,
                  ]}
                  onPress={() => handleYearSelect(year)}>
                  <Text
                    style={[
                      styles.yearOptionText,
                      selectedYear === year && styles.selectedYearOptionText,
                    ]}>
                    {year}年
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 渲染右上角年份选择按钮
  const renderYearSelector = () => (
    <TouchableOpacity
      style={styles.yearSelector}
      onPress={() => setYearPickerVisible(true)}
      activeOpacity={0.7}>
      <Text style={styles.yearSelectorText}>{selectedYear}年</Text>
      <Icon
        name="chevron-down"
        size={14}
        color="#666666"
        style={styles.yearSelectorIcon}
      />
    </TouchableOpacity>
  );

  // 渲染年度收支饼图
  const renderYearlyPieChart = () => {
    if (pieData.length === 0) {
      return (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>年度收支比例</Text>
          <View style={styles.emptyChartContainer}>
            <Text style={styles.emptyChartText}>暂无数据</Text>
          </View>
        </View>
      );
    }

    // 计算收入和支出的百分比
    const totalAmount = yearlyTotal.income + yearlyTotal.expense;
    const incomePercentage =
      totalAmount > 0
        ? ((yearlyTotal.income / totalAmount) * 100).toFixed(1)
        : '0.0';
    const expensePercentage =
      totalAmount > 0
        ? ((yearlyTotal.expense / totalAmount) * 100).toFixed(1)
        : '0.0';

    // 收入和支出的颜色
    const incomeColor = '#4CAF50';
    const expenseColor = '#FF3B30';

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>年度收支比例</Text>

        <View style={styles.pieChartWrapper}>
          <VictoryPie
            width={width - 40}
            height={200}
            data={pieData}
            theme={VictoryTheme.clean}
            x="name"
            y="population"
            innerRadius={80}
            padAngle={0}
            labelPlacement="perpendicular"
            labels={({datum}) => {
              // 计算占比
              const total = pieData.reduce(
                (sum, item) => sum + item.population,
                0,
              );
              const percentage = (datum.population / total) * 100;

              // 如果占比小于 10%，则不显示标签
              if (percentage < 10) {
                return null;
              }

              // 如果名称超过2个字，则用省略号
              const name = datum.name;
              return name.length > 2 ? name.substring(0, 2) + '...' : name;
            }}
            style={{
              data: {
                fill: ({datum}) => {
                  return datum.name === '收入' ? incomeColor : expenseColor;
                },
                stroke: '#FFFFFF',
                strokeWidth: 0,
              },
              labels: {
                fill: '#FFFFFF',
                fontSize: 12,
                fontWeight: 'bold',
              },
            }}
          />
        </View>

        {/* 收支比例信息 - 移到饼图下方 */}
        <View style={styles.pieChartRatioInfo}>
          <Text style={styles.pieChartRatioTitle}>
            {yearlyTotal.income > yearlyTotal.expense
              ? '收入大于支出'
              : '支出大于收入'}
          </Text>
        </View>

        {/* 图例和比例说明 - 水平布局 */}
        <View style={styles.pieChartLegendContainer}>
          <View style={styles.pieChartLegendRow}>
            <View style={styles.pieChartLegendItem}>
              <View
                style={[
                  styles.pieChartLegendColor,
                  {backgroundColor: incomeColor},
                ]}
              />
              <Text style={styles.pieChartLegendText}>
                收入{incomePercentage}%
              </Text>
            </View>
            <View style={styles.pieChartLegendItem}>
              <View
                style={[
                  styles.pieChartLegendColor,
                  {backgroundColor: expenseColor},
                ]}
              />
              <Text style={styles.pieChartLegendText}>
                支出{expensePercentage}%
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // 渲染月度收支趋势图
  const renderYearlyTrendChart = () => {
    if (monthlyData.length === 0) {
      return (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>月度收支趋势</Text>
          <View style={styles.emptyChartContainer}>
            <Text style={styles.emptyChartText}>暂无数据</Text>
          </View>
        </View>
      );
    }

    // 准备 Victory 折线图数据
    const incomeData = monthlyData.map((item, index) => ({
      x: item.month,
      y: item.income,
      month: item.month,
      value: item.income,
      type: '收入',
    }));

    const expenseData = monthlyData.map((item, index) => ({
      x: item.month,
      y: item.expense,
      month: item.month,
      value: item.expense,
      type: '支出',
    }));

    // 找出最大值，用于设置图表的 domain
    const maxIncome = Math.max(...monthlyData.map(item => item.income), 0);
    const maxExpense = Math.max(...monthlyData.map(item => item.expense), 0);
    const maxValue = Math.max(maxIncome, maxExpense, 0);

    // 确定是否需要换算单位（超过10000换算为"万"）
    const needUnitConversion = maxValue > 100000;

    // 为了让图表更美观，最大值增加一点余量
    const yMax = Math.ceil(maxValue * 1.1 || 10);
    const yDomain = [0, needUnitConversion ? yMax / 10000 : yMax];

    // 格式化 Y 轴标签
    const formatYAxis = t => {
      if (needUnitConversion) {
        return `${t}万`;
      }
      return `￥${t}`;
    };

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>月度收支趋势</Text>

        {/* 添加最高额度显示 */}
        <View style={styles.maxValueContainer}>
          <Text style={styles.maxValueLabel}>最高收入:</Text>
          <Text style={[styles.maxValueText, {color: '#4CAF50'}]}>
            {`￥${maxIncome.toFixed(0)}`}
          </Text>
          <Text style={styles.maxValueLabel}>最高支出:</Text>
          <Text style={[styles.maxValueText, {color: '#FF3B30'}]}>
            {`￥${maxExpense.toFixed(0)}`}
          </Text>
        </View>

        <View style={styles.victoryLineContainer}>
          <VictoryChart
            width={width - 40}
            height={220}
            domainPadding={{x: 10, y: 0}}
            padding={{top: 20, bottom: 40, left: 50, right: 20}}>
            <VictoryAxis
              tickFormat={t => `${t}月`}
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {
                  fontSize: 10,
                  fill: '#333333',
                },
              }}
            />
            <VictoryAxis
              dependentAxis
              domain={yDomain}
              style={{
                axis: {stroke: '#CCCCCC'},
                ticks: {stroke: 'transparent'},
                tickLabels: {fontSize: 10, fill: '#333333'},
                grid: {stroke: '#EEEEEE'},
              }}
              tickFormat={formatYAxis}
            />

            {/* 收入线 */}
            <VictoryLine
              data={
                needUnitConversion
                  ? incomeData.map(item => ({...item, y: item.y / 10000}))
                  : incomeData
              }
              x="x"
              y="y"
              style={{
                data: {
                  stroke: '#4CAF50', // 绿色，表示收入
                  strokeWidth: 2,
                },
              }}
            />

            {/* 支出线 */}
            <VictoryLine
              data={
                needUnitConversion
                  ? expenseData.map(item => ({...item, y: item.y / 10000}))
                  : expenseData
              }
              x="x"
              y="y"
              style={{
                data: {
                  stroke: '#FF3B30', // 红色，表示支出
                  strokeWidth: 2,
                },
              }}
            />

            {/* 收入点 */}
            <VictoryScatter
              data={
                needUnitConversion
                  ? incomeData.map(item => ({...item, y: item.y / 10000}))
                  : incomeData
              }
              x="x"
              y="y"
              size={4}
              style={{
                data: {
                  fill: '#4CAF50',
                  stroke: '#FFFFFF',
                  strokeWidth: 2,
                },
              }}
            />

            {/* 支出点 */}
            <VictoryScatter
              data={
                needUnitConversion
                  ? expenseData.map(item => ({...item, y: item.y / 10000}))
                  : expenseData
              }
              x="x"
              y="y"
              size={4}
              style={{
                data: {
                  fill: '#FF3B30',
                  stroke: '#FFFFFF',
                  strokeWidth: 2,
                },
              }}
            />
          </VictoryChart>
        </View>

        {/* 图例 */}
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, {backgroundColor: '#4CAF50'}]} />
            <Text style={styles.legendText}>收入</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, {backgroundColor: '#FF3B30'}]} />
            <Text style={styles.legendText}>支出</Text>
          </View>
        </View>
      </View>
    );
  };

  // 搜索交易记录的函数
  const getSearchResults = async (loadMore = false) => {
    if (!searchQuery.trim() && !isReimbursableFilter) {
      return;
    }

    try {
      const newPage = loadMore ? page + 1 : 1;
      if (loadMore) {
        setIsLoadingMore(true);
      } else {
        setIsSearching(true);
        // 重置分页
        setPage(1);
        setHasMoreResults(true);
      }

      // 调用数据库服务搜索交易，添加分页参数，添加筛选条件
      const searchFilterParam = accountBookId || familyId;
      const results = await databaseService.searchTransactions(
        searchQuery,
        searchFilterParam,
        (newPage - 1) * PAGE_SIZE,
        PAGE_SIZE,
        isReimbursableFilter ? true : undefined,
      );
      // 获取所有分类以便添加分类信息
      const allCategories = await databaseService.getAllCategories();

      // 添加分类信息到搜索结果
      const resultsWithCategory = results.map(transaction => {
        const category = allCategories.find(
          c => c.id === transaction.categoryId,
        );
        return {
          ...transaction,
          categoryName: category ? category.name : '未知分类',
          categoryIcon: category ? category.icon : 'question',
          categoryColor: getRandomColor(category ? category.name : '未知分类'),
        };
      });

      // 更新搜索结果 - 合并或替换
      if (loadMore) {
        setSearchResults(prev => [...prev, ...resultsWithCategory]);
        // 如果返回的结果少于一页，说明已经没有更多了
        setHasMoreResults(resultsWithCategory.length === PAGE_SIZE);
      } else {
        setSearchResults(resultsWithCategory);
        // 初始搜索时，如果结果为空或少于一页，设置没有更多
        setHasMoreResults(resultsWithCategory.length === PAGE_SIZE);
      }

      // 更新页码
      setPage(newPage);
    } catch (error) {
      console.error('搜索交易失败', error);
      if (!loadMore) {
        setSearchResults([]);
        setHasMoreResults(false);
      }
    } finally {
      setIsLoadingMore(false);
      // 如果是初始搜索，保持isSearching为true
    }
  };

  // 添加加载更多的处理函数
  const handleLoadMore = () => {
    if (!isLoadingMore && hasMoreResults) {
      getSearchResults(true);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setHasSearched(true);
    getSearchResults();
  };

  // 当页面获得焦点时，如果之前搜索过，则重新执行搜索
  useFocusEffect(
    useCallback(() => {
      if (hasSearched) {
        getSearchResults();
      }
    }, [hasSearched]),
  );

  // 清除搜索
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setIsSearching(false);
    setHasSearched(false);
    setIsReimbursableFilter(false); // 重置筛选状态
  };

  // 添加颜色生成函数
  const getRandomColor = (seed: string) => {
    const colors = [
      '#FF3B30', // 红色
      '#34C759', // 绿色
      '#007AFF', // 蓝色
      '#FF9500', // 橙色
      '#AF52DE', // 紫色
      '#5856D6', // 靛蓝
      '#FFCC00', // 黄色
      '#00BCD4', // 青色
      '#FF2D55', // 粉红
    ];

    const index = seed.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // 渲染搜索结果项
  const renderSearchResultItem = ({item}) => {
    const isExpense = item.type === 'expense';
    const date = new Date(item.date);
    const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

    return (
      <TouchableOpacity
        style={styles.searchResultItem}
        onPress={() =>
          navigation.navigate('addTransaction', {transaction: item})
        }
        activeOpacity={0.7}>
        <View style={styles.searchResultLeft}>
          <View
            style={[
              styles.categoryIcon,
              {backgroundColor: item.categoryColor},
            ]}>
            <Icon name={item.categoryIcon} size={20} color="#FFFFFF" />
          </View>
          <View style={styles.searchResultInfo}>
            <Text style={styles.searchResultCategory}>{item.categoryName}</Text>
            {item.note ? (
              <Text style={styles.searchResultNote} numberOfLines={1}>
                {item.note}
              </Text>
            ) : null}
            <Text style={styles.searchResultDate}>{formattedDate}</Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            {item.familyName ? (
              <View
                style={[
                  styles.familyNameBadge,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.familyName}>{item.familyName}</Text>
              </View>
            ) : null}
            {/* 待报销标识 - 如果isReimbursable为true则显示 */}
            {item.isReimbursable && (
              <View
                style={[
                  styles.reimbursableTag,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.reimbursableText}>报销账单</Text>
              </View>
            )}
          </View>
        </View>
        <Text style={[styles.searchResultAmount]}>
          {isExpense ? '-￥' : '+￥'}
          {item.amount.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  };

  // 打开分析报告
  const openAnalysisReport = async () => {
    try {
      // 获取当年所有交易数据
      const allTransactions = await databaseService.getAllTransactions(
        familyId,
      );
      const transactions = allTransactions.filter(t => {
        const transactionYear = new Date(t.date).getFullYear();
        return transactionYear === selectedYear;
      });

      // 获取所有分类
      const categories = await databaseService.getAllCategories();

      // 分析消费行为
      const behaviorAnalysis = analyzeConsumptionBehavior(
        transactions,
        categories,
      );
      // 计算消费健康度评分
      const healthScore = calculateHealthScore(transactions, yearlyTotal);

      // 生成智能建议
      const suggestions = generateSmartSuggestions(
        transactions,
        categories,
        behaviorAnalysis,
        healthScore,
        yearlyTotal,
      );

      // 设置分析数据
      setAnalysisData({
        behaviorAnalysis,
        healthScore,
        suggestions,
        transactions,
        categories,
        yearlyTotal,
        monthlyData,
      });

      // 显示分析报告
      setShowAnalysis(true);

      // 动画显示分析报告
      analysisSheetPosition.value = withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.cubic),
      });
    } catch (error) {
      console.error('生成分析报告失败', error);
    }
  };

  // 关闭分析报告
  const closeAnalysisReport = () => {
    setShowAnalysis(false);
  };

  // 渲染年度账单分析按钮
  const renderAnalysisButton = () => (
    <TouchableOpacity
      style={[styles.analysisButton, {backgroundColor: colors.primary}]}
      onPress={openAnalysisReport}
      activeOpacity={0.7}>
      <Icon
        name="chart-pie"
        size={16}
        color="#FFFFFF"
        style={styles.analysisButtonIcon}
      />
      <Text style={styles.analysisButtonText}>年度账单分析</Text>
    </TouchableOpacity>
  );

  // 渲染分析报告
  const renderAnalysisReport = () => {
    if (!analysisData) {
      return null;
    }

    const {
      behaviorAnalysis,
      healthScore,
      suggestions,
      yearlyTotal,
      monthlyData,
    } = analysisData;

    // 找出消费最高的三个月
    const topExpenseMonths = [...monthlyData]
      .sort((a, b) => b.expense - a.expense)
      .slice(0, 3);

    // 月份名称
    const monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];

    return (
      <Modal
        visible={showAnalysis}
        animationType="slide"
        transparent={true}
        onRequestClose={closeAnalysisReport}>
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* 标题栏 */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>年度账单分析</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={closeAnalysisReport}>
                <Icon name="xmark" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            {/* 分析内容 - 滚动视图 */}
            <ScrollView style={styles.analysisScrollView}>
              {/* 账单封面 */}
              <View style={styles.analysisCover}>
                <Icon name="file-invoice-dollar" size={50} color="#007AFF" />
                <Text style={styles.analysisTitle}>
                  {selectedYear}年度账单分析
                </Text>

                <View style={styles.healthScoreContainer}>
                  <Text style={styles.healthScoreText}>
                    {healthScore?.totalScore || 0}
                  </Text>
                  <Text style={styles.healthScoreMax}>/100</Text>
                </View>

                <View style={styles.healthScoreDesc}>
                  <Text style={styles.healthScoreDescText}>
                    {healthScore?.scoreDescription || '暂无评分'}
                  </Text>
                </View>
              </View>

              {/* 年度汇总 */}
              <View style={styles.analysisSummary}>
                <View style={styles.analysisSummaryRow}>
                  <View style={styles.analysisSummaryItem}>
                    <Text style={styles.analysisSummaryValue}>
                      ￥{yearlyTotal?.expense?.toFixed(0) || 0}
                    </Text>
                    <Text style={styles.analysisSummaryLabel}>年度总支出</Text>
                  </View>

                  <View style={styles.analysisSummaryItem}>
                    <Text style={styles.analysisSummaryValue}>
                      ￥
                      {yearlyTotal?.expense
                        ? (yearlyTotal.expense / 12).toFixed(0)
                        : 0}
                    </Text>
                    <Text style={styles.analysisSummaryLabel}>月均支出</Text>
                  </View>
                </View>

                {/* 消费最高的三个月 */}
                <Text style={styles.analysisSubtitle}>支出最高的月份</Text>
                <View style={styles.topMonthsContainer}>
                  {topExpenseMonths.map((month, index) => (
                    <View
                      key={`top-month-${index}`}
                      style={styles.topMonthItem}>
                      <Text style={styles.topMonthRank}>{index + 1}</Text>
                      <View style={styles.topMonthInfo}>
                        <Text style={styles.topMonthName}>
                          {monthNames[month.month - 1]}
                        </Text>
                        <Text style={styles.topMonthExpense}>
                          ￥{month.expense.toFixed(0)}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              </View>

              {/* 消费行为特征 */}
              <View style={styles.analysisSection}>
                <Text style={styles.analysisSectionTitle}>消费行为特征</Text>

                {/* 消费类型分布 */}
                <Text style={styles.analysisSubtitle}>消费类型分布</Text>
                <View style={styles.behaviorTraitContainer}>
                  {behaviorAnalysis.topCategories.map((category, index) => (
                    <View key={`category-${index}`} style={styles.categoryItem}>
                      <View style={styles.categoryProgress}>
                        <View
                          style={[
                            styles.categoryProgressFill,
                            {
                              width: `${category.percentage}%`,
                              backgroundColor: category.color || '#007AFF',
                            },
                          ]}
                        />
                      </View>
                      <View style={styles.categoryInfo}>
                        <Text style={styles.categoryName}>{category.name}</Text>
                        <Text style={styles.categoryPercentage}>
                          {category.percentage.toFixed(1)}%
                        </Text>
                      </View>
                      <Text style={styles.categoryAmount}>
                        ￥{category.amount.toFixed(0)}
                      </Text>
                    </View>
                  ))}
                </View>

                {/* 消费习惯特征 */}
                <Text style={styles.analysisSubtitle}>消费习惯特征</Text>
                <View style={styles.traitsContainer}>
                  {behaviorAnalysis.traits.map((trait, index) => (
                    <View key={`trait-${index}`} style={styles.traitItem}>
                      <Icon
                        name={trait.icon}
                        size={20}
                        color="#007AFF"
                        style={styles.traitIcon}
                      />
                      <View style={styles.traitContent}>
                        <Text style={styles.traitTitle}>{trait.title}</Text>
                        <Text style={styles.traitDescription}>
                          {trait.description}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              </View>

              {/* 健康度评分详情 */}
              <View style={styles.analysisSection}>
                <Text style={styles.analysisSectionTitle}>财务健康度评分</Text>

                {healthScore.factors.map((factor, index) => (
                  <View key={`factor-${index}`} style={styles.scoreFactorItem}>
                    <View style={styles.scoreFactorHeader}>
                      <Text style={styles.scoreFactorTitle}>{factor.name}</Text>
                      <Text style={styles.scoreFactorValue}>
                        {factor.score}/100
                      </Text>
                    </View>
                    <View style={styles.scoreFactorProgress}>
                      <View
                        style={[
                          styles.scoreFactorProgressFill,
                          {
                            width: `${factor.score}%`,
                            backgroundColor: factor.color,
                          },
                        ]}
                      />
                    </View>
                    <Text style={styles.scoreFactorDescription}>
                      {factor.description}
                    </Text>
                  </View>
                ))}
              </View>

              {/* 智能建议 */}
              <View style={styles.analysisSection}>
                <Text style={styles.analysisSectionTitle}>智能财务建议</Text>

                {suggestions.map((suggestion, index) => (
                  <View
                    key={`suggestion-${index}`}
                    style={styles.suggestionItem}>
                    <View
                      style={[
                        styles.suggestionIcon,
                        {backgroundColor: suggestion.color},
                      ]}>
                      <Icon name={suggestion.icon} size={20} color="#FFFFFF" />
                    </View>
                    <View style={styles.suggestionContent}>
                      <Text style={styles.suggestionTitle}>
                        {suggestion.title}
                      </Text>
                      <Text style={styles.suggestionDescription}>
                        {suggestion.description}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>

              {/* 未来预测 */}
              <View style={styles.analysisSection}>
                <Text style={styles.analysisSectionTitle}>未来消费预测</Text>
                <Text style={styles.predictionText}>
                  根据你的消费习惯，预计下一年度总支出约为
                  <Text style={styles.predictionHighlight}>
                    ￥
                    {(
                      yearlyTotal.expense * behaviorAnalysis.growthRate
                    ).toFixed(0)}
                  </Text>
                  {behaviorAnalysis.growthRate > 1
                    ? `，增长约 ${(
                        (behaviorAnalysis.growthRate - 1) *
                        100
                      ).toFixed(1)}%`
                    : `，减少约 ${(
                        (1 - behaviorAnalysis.growthRate) *
                        100
                      ).toFixed(1)}%`}
                </Text>

                <Text style={styles.predictionAdvice}>
                  {behaviorAnalysis.growthRate > 1.1
                    ? '支出增长较快，建议控制消费节奏，增加储蓄比例。'
                    : behaviorAnalysis.growthRate < 0.9
                    ? '支出减少明显，可以适当增加必要消费，提高生活品质。'
                    : '支出增长平稳，继续保持良好的消费习惯。'}
                </Text>
              </View>

              {/* 结语 */}
              <View style={styles.analysisFooter}>
                <Text style={styles.analysisFooterText}>
                  希望这份分析报告能帮助你更好地了解自己的财务状况，做出更明智的消费决策。
                </Text>
                <Text style={styles.analysisFooterSignature}>
                  — 你的智能财务助手
                </Text>
              </View>
            </ScrollView>
          </View>
        </SafeAreaView>
      </Modal>
    );
  };

  // 在界面顶部添加一个指示器，表明当前是在查看家庭账单
  const renderFamilyModeIndicator = () => {
    if (showFamilyBillsOnly) {
      return null;
    }

    return (
      <View
        style={[styles.familyModeIndicator, {backgroundColor: colors.primary}]}>
        <Icon name="users" size={14} color="#FFFFFF" style={{marginRight: 6}} />
        <Text style={styles.familyModeText}>家庭账单模式</Text>
      </View>
    );
  };

  // 渲染筛选模态框
  const renderFilterModal = () => (
    <Modal
      visible={filterVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setFilterVisible(false)}>
      <TouchableWithoutFeedback onPress={() => setFilterVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.filterContainer}>
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>筛选条件</Text>
              <TouchableOpacity
                style={styles.closeButtonContainer}
                onPress={() => setFilterVisible(false)}>
                <Text style={styles.closeButton}>关闭</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.filterOption}>
              <Text style={styles.filterOptionLabel}>待报销账单</Text>
              <TouchableOpacity
                style={[
                  styles.filterCheckbox,
                  isReimbursableFilter && {backgroundColor: colors.primary},
                ]}
                onPress={() => setIsReimbursableFilter(!isReimbursableFilter)}>
                {isReimbursableFilter && (
                  <Icon name="check" size={14} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.filterActions}>
              <TouchableOpacity
                style={styles.resetFilterButton}
                onPress={() => {
                  setIsReimbursableFilter(false);
                }}>
                <Text style={styles.resetFilterText}>重置</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.applyFilterButton,
                  {backgroundColor: colors.primary},
                ]}
                onPress={() => {
                  setFilterVisible(false);
                  setHasSearched(true);
                  getSearchResults();
                }}>
                <Text style={styles.applyFilterText}>应用筛选</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  const resultFoot = () => {
    return isLoadingMore ? (
      <View style={styles.loadingMore}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingMoreText}>加载更多...</Text>
      </View>
    ) : !hasMoreResults && searchResults.length > 0 ? (
      <Text style={styles.noMoreText}>
        已显示全部 {searchResults.length} 条结果
      </Text>
    ) : null;
  };

  // 确定页面标题
  const getPageTitle = () => {
    if (accountBookName) {
      return `${accountBookName}的账单`;
    } else if (familyName) {
      return `${familyName}的家庭账单`;
    } else {
      return '账单';
    }
  };

  return (
    <PageContainer
      headerTitle={getPageTitle()}
      backgroundColor={COLORS.secondary}
      rightComponent={renderYearSelector()}>
      {/* 搜索框 */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon
            name="magnifying-glass-dollar"
            size={18}
            color="#999999"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="类型、备注、金额、家庭名字"
            placeholderTextColor="#999999"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
              <Icon name="times-circle" size={16} color="#999999" />
            </TouchableOpacity>
          )}
        </View>

        {/* 筛选按钮 */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            isReimbursableFilter && {backgroundColor: colors.primary},
          ]}
          onPress={() => setFilterVisible(true)}
          activeOpacity={0.7}>
          <Icon
            name="filter"
            size={16}
            color={isReimbursableFilter ? '#FFFFFF' : '#666666'}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.searchButton, {backgroundColor: colors.primary}]}
          onPress={handleSearch}
          activeOpacity={0.7}>
          <Text style={styles.searchButtonText}>搜索</Text>
        </TouchableOpacity>
      </View>

      {/* 筛选框 */}
      {renderFilterModal()}

      {/* 搜索结果或原始内容 */}
      {isSearching ? (
        <View style={styles.searchResultsContainer}>
          <View style={styles.searchResultsHeader}>
            <Text style={styles.searchResultsTitle}>
              搜索结果 ({searchResults.length}
              {!hasMoreResults ? '' : '+'})
              {isReimbursableFilter && (
                <Text style={{color: colors.primary}}> (待报销)</Text>
              )}
            </Text>
            <TouchableOpacity onPress={clearSearch} style={styles.backButton}>
              <Text style={styles.backButtonText}>返回</Text>
            </TouchableOpacity>
          </View>

          {searchResults.length > 0 ? (
            <FlatList
              data={searchResults}
              renderItem={renderSearchResultItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.searchResultsList}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              ListFooterComponent={resultFoot()}
            />
          ) : (
            <View style={styles.emptySearchResults}>
              <Icon name="magnifying-glass-dollar" size={50} color="#DDDDDD" />
              <Text style={styles.emptySearchText}>未找到匹配的交易记录</Text>
            </View>
          )}
        </View>
      ) : (
        // 原始内容
        <ScrollView style={styles.chartScrollView}>
          {/* 年度统计卡片 */}
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>{selectedYear}年度统计</Text>

            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>年度收入</Text>
                <Text style={[styles.summaryValue, styles.incomeText]}>
                  ￥{yearlyTotal.income.toFixed(2)}
                </Text>
              </View>

              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>年度支出</Text>
                <Text style={[styles.summaryValue, styles.expenseText]}>
                  ￥{yearlyTotal.expense.toFixed(2)}
                </Text>
              </View>
            </View>

            <View style={styles.balanceContainer}>
              <Text style={styles.balanceLabel}>年度结余</Text>
              <Text
                style={[
                  styles.balanceValue,
                  yearlyTotal.balance >= 0
                    ? styles.incomeText
                    : styles.expenseText,
                ]}>
                ￥{yearlyTotal.balance.toFixed(2)}
              </Text>
            </View>

            {/* 添加分析按钮 */}
            <View style={styles.analysisButtonContainer}>
              {renderAnalysisButton()}
            </View>
          </View>

          {/* 图表部分 */}
          <View style={styles.chartScrollView}>
            {/* 年度收支饼图 */}
            {renderYearlyPieChart()}

            {/* 月度收支趋势图 */}
            {renderYearlyTrendChart()}

            {/* 月度列表 */}
            <View style={styles.monthlyContainer}>
              <Text style={styles.sectionTitle}>月度账单</Text>
              <FlatList
                data={monthlyData}
                renderItem={renderMonthItem}
                keyExtractor={item => `month-${item.month}`}
                contentContainerStyle={styles.monthlyList}
                scrollEnabled={false} // 禁用内部滚动，使用外部 ScrollView
              />
            </View>
          </View>
        </ScrollView>
      )}

      {/* 年份选择器 Modal */}
      {renderYearPicker()}

      {/* 渲染分析报告 */}
      {renderAnalysisReport()}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  yearSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
    // backgroundColor: '#F0F0F0',
  },
  yearSelectorText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginRight: 5,
  },
  yearSelectorIcon: {
    marginTop: 2,
  },
  summaryCard: {
    margin: 15,
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 15,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryItem: {
    flex: 1,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 5,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
  },
  incomeText: {
    color: '#34C759',
  },
  expenseText: {
    color: '#FF3B30',
  },
  balanceContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 5,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 5,
    textAlign: 'center',
  },
  balanceValue: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
  },
  monthlyContainer: {
    flex: 1,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 15,
    marginLeft: 5,
  },
  monthlyList: {
    paddingBottom: 20,
  },
  tableRow: {
    backgroundColor: COLORS.background.white,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: 'rgba(0, 0, 0, 0.08)',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  tableRowContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  monthColumn: {
    width: 70,
    alignItems: 'center',
  },
  monthText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  monthTransactionCount: {
    fontSize: 10,
    color: '#999999',
  },
  amountColumns: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  amountColumn: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearPickerContainer: {
    width: width * 0.8,
    backgroundColor: COLORS.background.white,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 10},
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  yearPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#F9F9F9',
  },
  yearPickerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
  },
  closeButtonContainer: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
  },
  closeButton: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
  },
  yearList: {
    maxHeight: 350,
  },
  yearOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    alignItems: 'center',
  },
  selectedYearOption: {
    backgroundColor: COLORS.secondary,
  },
  yearOptionText: {
    fontSize: 18,
    color: COLORS.primary,
    textAlign: 'center',
  },
  selectedYearOptionText: {
    color: COLORS.text.primary,
    fontWeight: '700',
  },
  chartScrollView: {
    flex: 1,
  },
  chartContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 15,
    margin: 15,
    padding: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    // marginBottom: 15,
    textAlign: 'center',
  },
  emptyChartContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyChartText: {
    fontSize: 16,
    color: '#999999',
  },
  pieChartWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    height: 200,
  },
  pieChartRatioInfo: {
    alignItems: 'center',
    marginBottom: 6,
  },
  pieChartRatioTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  pieChartRatioValue: {
    fontSize: 14,
    color: '#666666',
  },
  pieChartLegendContainer: {
    width: '100%',
    marginTop: 5,
    paddingHorizontal: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  pieChartLegendRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  pieChartLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pieChartLegendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  pieChartLegendText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  pieChartAmountRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 5,
  },
  pieChartAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: COLORS.background.white,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: COLORS.background.light,
    borderRadius: 20,
    paddingHorizontal: 12,
    alignItems: 'center',
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text.primary,
    padding: 0,
    height: 40,
  },
  clearButton: {
    padding: 5,
  },
  searchButton: {
    marginLeft: 10,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  searchResultsContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  searchResultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: '#EEEEEE',
  },
  searchResultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  resultTip: {
    fontSize: 12,
    color: '#999999',
  },
  backButton: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    backgroundColor: COLORS.background.light,
  },
  backButtonText: {
    fontSize: 14,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  searchResultsList: {
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 20,
  },
  searchResultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: '#F0F0F0',
  },
  searchResultLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  searchResultInfo: {
    flex: 1,
  },
  searchResultCategory: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 2,
  },
  searchResultNote: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  searchResultDate: {
    fontSize: 12,
    color: '#999999',
  },
  familyNameBadge: {
    marginRight: 20,
    backgroundColor: '#FFF9C4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  familyName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  reimbursableTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 10, // 与金额的间距
  },
  reimbursableText: {
    fontSize: 12,
    color: '#FFFFFF', // 待报销标签的文字颜色
    fontWeight: '600',
  },
  searchResultAmount: {
    width: 100,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  emptySearchResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptySearchText: {
    fontSize: 16,
    color: '#999999',
    marginTop: 15,
  },
  victoryLineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    height: 220,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 5,
  },
  legendText: {
    fontSize: 14,
    color: '#333333',
  },
  analysisButtonContainer: {
    marginTop: 15,
    alignItems: 'center',
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 3,
  },
  analysisButtonIcon: {
    marginRight: 8,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  analysisModalCustomStyle: {
    padding: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Dimensions.get('window').height * 0.8,
  },
  analysisSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  analysisSheetHeaderLeft: {
    flex: 1,
  },
  analysisSheetTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
  },
  analysisSheetSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  analysisCover: {
    alignItems: 'center',
    marginBottom: 30,
    paddingVertical: 30,
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 3,
  },
  analysisTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 15,
  },
  healthScoreContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  healthScoreText: {
    fontSize: 60,
    fontWeight: '700',
    color: '#007AFF',
  },
  healthScoreMax: {
    fontSize: 24,
    color: '#999999',
    marginBottom: 10,
  },
  healthScoreDesc: {
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: '#F0F8FF',
    borderRadius: 15,
  },
  healthScoreDescText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
  },
  analysisSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 3,
  },
  analysisSectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 15,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
    paddingLeft: 10,
  },
  analysisSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#555555',
    marginTop: 15,
    marginBottom: 10,
  },
  analysisSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 10,
  },
  analysisSummaryItem: {
    alignItems: 'center',
  },
  analysisSummaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
  },
  analysisSummaryLabel: {
    fontSize: 14,
    color: '#666666',
    marginTop: 5,
  },
  topMonthsContainer: {
    marginTop: 10,
  },
  topMonthItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 10,
  },
  topMonthRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#007AFF',
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: '700',
    marginRight: 10,
  },
  topMonthInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topMonthName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  topMonthExpense: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FF3B30',
  },
  behaviorTraitContainer: {
    marginTop: 10,
  },
  categoryItem: {
    marginBottom: 15,
  },
  categoryProgress: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    marginBottom: 5,
    overflow: 'hidden',
  },
  categoryProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  categoryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  categoryPercentage: {
    fontSize: 14,
    color: '#666666',
  },
  categoryAmount: {
    fontSize: 12,
    color: '#999999',
  },
  traitsContainer: {
    marginTop: 10,
  },
  traitItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 12,
  },
  traitIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  traitContent: {
    flex: 1,
  },
  traitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 5,
  },
  traitDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  scoreFactorItem: {
    marginBottom: 15,
  },
  scoreFactorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  scoreFactorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  scoreFactorValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#007AFF',
  },
  scoreFactorProgress: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  scoreFactorProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  scoreFactorDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  suggestionItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 15,
  },
  suggestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 5,
  },
  suggestionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  predictionText: {
    fontSize: 15,
    color: '#333333',
    lineHeight: 22,
  },
  predictionHighlight: {
    fontWeight: '700',
    color: '#007AFF',
  },
  predictionAdvice: {
    fontSize: 14,
    color: '#666666',
    marginTop: 10,
    fontStyle: 'italic',
  },
  analysisFooter: {
    marginTop: 10,
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#F8F8F8',
    borderRadius: 15,
  },
  analysisFooterText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  analysisFooterSignature: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'right',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: 50, // 距离顶部的距离
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  modalCloseButton: {
    padding: 5,
  },
  analysisScrollView: {
    flex: 1,
  },
  analysisSummary: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  monthlyAnalysisSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    marginTop: 20,
    marginBottom: 10,
  },
  maxValueContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    flexWrap: 'wrap',
  },
  maxValueLabel: {
    fontSize: 12,
    color: COLORS.text.gray,
    marginRight: 4,
    marginLeft: 12,
  },
  maxValueText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 12,
  },
  familyModeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    alignSelf: 'center',
    marginVertical: 8,
  },
  familyModeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  loadingMoreText: {
    marginLeft: 5,
    color: '#666666',
    fontSize: 14,
  },
  noMoreText: {
    textAlign: 'center',
    color: '#999999',
    fontSize: 14,
    paddingVertical: 10,
  },
  filterButton: {
    marginLeft: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: COLORS.background.light,
  },
  filterContainer: {
    width: width * 0.8,
    backgroundColor: COLORS.background.white,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 10},
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#F9F9F9',
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  filterOptionLabel: {
    fontSize: 16,
    color: '#333333',
  },
  filterCheckbox: {
    width: 22,
    height: 22,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  resetFilterButton: {
    flex: 1,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    marginRight: 10,
  },
  resetFilterText: {
    fontSize: 16,
    color: '#666666',
  },
  applyFilterButton: {
    flex: 1,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    marginLeft: 10,
  },
  applyFilterText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default BillsScreen;
