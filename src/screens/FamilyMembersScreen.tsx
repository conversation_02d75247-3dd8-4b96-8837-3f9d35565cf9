import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  TextInput,
  TouchableWithoutFeedback,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import {COLORS} from '../utils/color';
import databaseService from '../services/DatabaseService';
import {useTheme} from '../context/ThemeContext';
import {useToast} from '../context/ToastContext';
import ConfirmDialog from '../components/ConfirmDialog';
import {useFocusEffect} from '@react-navigation/native';
import syncService from '../services/SyncService';

// 家庭成员类型定义
interface FamilyMember {
  id: number;
  name: string;
  deviceId: string;
  avatar?: string;
  role: 'admin' | 'member';
  lastSyncTime?: string;
  isOnline?: boolean;
}

// 家庭接口定义
interface Family {
  id: number;
  name: string;
  code: string;
  createdAt: string;
}

const FamilyMembersScreen = ({navigation}) => {
  const {colors} = useTheme();
  // 使用 useToast 钩子获取 showToast 函数
  const {showToast} = useToast();
  const [members, setMembers] = useState<FamilyMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasFamily, setHasFamily] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [familyCode, setFamilyCode] = useState('');
  const [joinModalVisible, setJoinModalVisible] = useState(false);
  const [inputFamilyCode, setInputFamilyCode] = useState('');
  const [family, setFamily] = useState<Family | null>(null);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [currentDeviceId, setCurrentDeviceId] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  // 添加确认对话框状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );
  // 添加新的状态变量
  const [createFamilyModalVisible, setCreateFamilyModalVisible] =
    useState(false);
  const [newFamilyName, setNewFamilyName] = useState('');
  const [newMemberName, setNewMemberName] = useState('');
  const [joinFamilyModalVisible, setJoinFamilyModalVisible] = useState(false);
  const [joinMemberName, setJoinMemberName] = useState('');
  const [syncStatus, setSyncStatus] = useState('idle');
  const [syncMessage, setSyncMessage] = useState('');
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [discoveredDevices, setDiscoveredDevices] = useState([]);

  useEffect(() => {
    loadFamilyMembers();
    // 添加同步状态监听器
    const handleSyncStatusChanged = data => {
      const {status, message} = data;
      console.log('同步状态变化:', status, message);
      setSyncStatus(status);
      setSyncMessage(message || getSyncStatusMessage(status));

      // 当开始扫描时显示同步模态框
      if (status === 'scanning') {
        setSyncModalVisible(true);
        setDiscoveredDevices([]); // 清空之前发现的设备
      }

      // 同步完成或失败时3秒后自动关闭模态框
      if (status === 'completed' || status === 'failed') {
        setTimeout(() => {
          setSyncModalVisible(false);
          loadFamilyMembers(); // 刷新家庭成员列表
        }, 3000);
      }
    };

    // 添加设备发现监听器
    const handleDeviceDiscovered = device => {
      console.log('发现设备:', device);
      setDiscoveredDevices(prevDevices => {
        // 检查设备是否已经在列表中
        const exists = prevDevices.some(d => d.id === device.id);
        if (!exists) {
          return [...prevDevices, device];
        }
        return prevDevices;
      });
    };

    // 注册监听器
    syncService.addSyncStatusListener(handleSyncStatusChanged);
    syncService.addDeviceDiscoveredListener(handleDeviceDiscovered);

    // 组件卸载时清理监听器
    return () => {
      syncService.removeSyncStatusListener(handleSyncStatusChanged);
      syncService.removeDeviceDiscoveredListener(handleDeviceDiscovered);
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // 当屏幕重新获得焦点时重新加载家庭成员数据
      loadFamilyMembers();
      return () => {
        // 可选的清理函数
      };
    }, []),
  );

  const loadFamilyMembers = async () => {
    try {
      setIsLoading(true);

      // 获取当前设备ID
      const deviceId = await databaseService.getDeviceId();
      setCurrentDeviceId(deviceId);

      // 检查用户是否已有家庭
      const familyInfo = await databaseService.getFamilyInfo();

      if (familyInfo) {
        setHasFamily(true);
        setFamily(familyInfo);
        setFamilyCode(familyInfo.code);

        // 获取家庭成员列表
        const membersList = await databaseService.getFamilyMembers();
        setMembers(membersList);

        // 检查当前用户是否为管理员
        const currentMember = membersList.find(
          member => member.deviceId === deviceId,
        );
        setIsAdmin(currentMember?.role === 'admin');
      } else {
        setHasFamily(false);
        setMembers([]);
      }
    } catch (error) {
      console.error('加载家庭成员失败', error);
      showToast('加载家庭成员信息失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const createFamily = async () => {
    try {
      if (!newFamilyName.trim()) {
        showToast('请输入家庭名称', 'warning');
        return;
      }

      if (!newMemberName.trim()) {
        showToast('请输入您的名字', 'warning'); // 显示提示信息，不退出当前页面
        return;
      }

      // 生成随机邀请码
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      setFamilyCode(code);

      // 创建家庭并添加当前用户为管理员
      await databaseService.createFamily(
        code,
        newFamilyName.trim(),
        newMemberName.trim(),
      );
      setHasFamily(true);
      await loadFamilyMembers();

      // 关闭创建家庭弹窗，显示邀请码
      setCreateFamilyModalVisible(false);
      setInviteModalVisible(true);
    } catch (error) {
      console.error('创建家庭失败', error);
      showToast('创建家庭失败', 'error');
    }
  };

  const joinFamily = async () => {
    try {
      if (!inputFamilyCode.trim()) {
        showToast('请输入邀请码', 'warning');
        return;
      }

      if (!joinMemberName.trim()) {
        showToast('请输入您的名字', 'warning');
        return;
      }

      await databaseService.joinFamily(
        inputFamilyCode.trim(),
        joinMemberName.trim(),
      );
      setJoinModalVisible(false);
      setHasFamily(true);
      await loadFamilyMembers();
      showToast('成功加入家庭', 'success');
    } catch (error) {
      console.error('加入家庭失败', error);
      showToast('加入家庭失败，请检查邀请码是否正确', 'error');
    }
  };

  const startSync = async () => {
    try {
      if (!family) {
        showToast('请先创建或加入家庭', 'warning');
        return;
      }

      // 调用 syncService 的 startSync 方法
      const started = await syncService.startSync();

      if (!started) {
        showToast('同步已在进行中，请稍候', 'warning');
      } else {
        showToast('开始寻找附近设备并同步...', 'info');
      }
    } catch (error) {
      console.error('开始同步失败', error);
      showToast('同步失败', 'error');
    }
  };

  // 显示邀请码
  const showInviteCode = () => {
    if (familyCode) {
      setInviteModalVisible(true);
    } else {
      // 如果没有家庭码（理论上不应该发生），重新获取
      loadFamilyMembers().then(() => {
        if (familyCode) {
          setInviteModalVisible(true);
        } else {
          showToast('无法获取邀请码，请重试', 'error');
        }
      });
    }
  };

  // 退出家庭
  const leaveFamily = async () => {
    setShowSettingsModal(false);
    setConfirmDialogMessage(
      '确定要退出当前家庭吗？退出后将无法同步家庭账单数据。',
    );
    setConfirmDialogAction(() => async () => {
      await databaseService.leaveFamily();
      setShowSettingsModal(false);
      setHasFamily(false);
      setFamily(null);
      setMembers([]);
      showToast('已退出家庭', 'success');
    });
    setConfirmDialogVisible(true);
  };

  // 添加踢出成员功能
  const kickMember = async (memberId, memberName) => {
    if (!isAdmin) {
      showToast('只有管理员可以踢出成员', 'warning');
      return;
    }

    setConfirmDialogMessage(`确定要将"${memberName}"踢出家庭吗？`);
    setConfirmDialogAction(() => async () => {
      try {
        await databaseService.removeFamilyMember(memberId);
        showToast('已成功踢出该成员', 'success');
        loadFamilyMembers(); // 重新加载成员列表
      } catch (error) {
        console.error('踢出成员失败', error);
        showToast('踢出成员失败', 'error');
      }
    });
    setConfirmDialogVisible(true);
  };

  // 添加虚拟家庭成员的函数
  const addTestMember = async () => {
    try {
      if (!family) {
        showToast('请先创建或加入家庭', 'warning');
        return;
      }

      // 生成随机名称和设备ID
      const randomName = `测试用户${Math.floor(Math.random() * 1000)}`;
      const randomDeviceId = `test_device_${Math.random()
        .toString(36)
        .substring(2, 10)}`;
      const now = new Date().toISOString();

      await databaseService.database!.executeSql(
        `INSERT INTO family_members (family_id, name, device_id, role, last_sync_time) 
         VALUES (?, ?, ?, ?, ?)`,
        [family.id, randomName, randomDeviceId, 'member', now],
      );

      // 重新加载家庭成员
      loadFamilyMembers();
      showToast(`已添加虚拟成员: ${randomName}`, 'success');
    } catch (error) {
      console.error('添加虚拟成员失败', error);
      showToast('添加虚拟成员失败', 'error');
    }
  };

  // 添加查看家庭账单的函数
  const viewFamilyBills = () => {
    if (!family) {
      showToast('请先创建或加入家庭', 'warning');
      return;
    }

    // 导航到账单页面并传递参数，指示只显示家庭账单
    navigation.navigate('bills', {
      showFamilyBillsOnly: true,
      familyId: family.id,
      familyName: family.name,
    });
  };

  const renderMemberItem = ({item}: {item: FamilyMember}) => {
    const isCurrentUser = item.deviceId === currentDeviceId;

    return (
      <View style={styles.memberItem}>
        <View style={styles.memberInfo}>
          <View
            style={[
              styles.memberAvatar,
              {backgroundColor: item.avatar || colors.primary},
            ]}>
            <Text style={styles.avatarText}>{item.name.charAt(0)}</Text>
          </View>
          <View style={styles.memberDetails}>
            <View style={styles.namebox}>
              <Text style={styles.memberName}>{item.name}</Text>
              {isCurrentUser && (
                <View style={styles.selfBadge}>
                  <Text style={styles.selfText}>我</Text>
                </View>
              )}
              {item.role === 'admin' && (
                <View style={styles.adminBadge}>
                  <Text style={styles.adminText}>管理员</Text>
                </View>
              )}
            </View>
            <View style={styles.memberStatusRow}>
              <View
                style={[
                  styles.statusIndicator,
                  {backgroundColor: item.isOnline ? '#4CAF50' : '#BBBBBB'},
                ]}
              />
              <Text style={styles.memberStatus}>
                {item.isOnline ? '在线' : '离线'}
              </Text>
              {item.lastSyncTime && (
                <Text style={styles.syncTime}>
                  上次同步: {item.lastSyncTime}
                </Text>
              )}
            </View>
          </View>
        </View>

        {/* 踢人按钮，只有管理员可见且不能踢自己 */}
        {isAdmin && !isCurrentUser && (
          <TouchableOpacity
            style={styles.kickButton}
            onPress={() => kickMember(item.id, item.name)}>
            <Icon name="user-minus" size={12} color="#FF3B30" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderNoFamilyView = () => (
    <View style={styles.emptyContainer}>
      <Icon name="users" size={60} color="#CCCCCC" />
      <Text style={styles.emptyTitle}>还没有加入家庭</Text>
      <Text style={styles.emptyText}>
        创建一个家庭或加入已有家庭，与家人共享账单
      </Text>

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.button, {backgroundColor: colors.primary}]}
          onPress={() => setCreateFamilyModalVisible(true)}>
          <Text style={styles.buttonText}>创建家庭</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, {backgroundColor: '#7E57C2'}]}
          onPress={() => setJoinModalVisible(true)}>
          <Text style={styles.buttonText}>加入家庭</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTitleRow}>
        <Text style={styles.headerTitle}>家庭成员</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.inviteButton}
            onPress={showInviteCode}>
            <Icon
              name="user-plus"
              size={16}
              color={colors.primary}
              style={{marginRight: 6}}
            />
            <Text style={[styles.inviteButtonText, {color: colors.primary}]}>
              邀请成员
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => setShowSettingsModal(true)}>
            <Icon name="gear" size={16} color="#666666" />
          </TouchableOpacity>
        </View>
      </View>
      <Text style={styles.familyName}>家庭名称：{family?.name}</Text>
      <Text style={styles.headerText}>
        同一WiFi或蓝牙环境下可以自动同步账单
      </Text>

      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity
          style={[styles.syncButton, {backgroundColor: colors.primary}]}
          onPress={startSync}>
          <Icon
            name="rotate"
            size={16}
            color="#FFFFFF"
            style={{marginRight: 8}}
          />
          <Text style={styles.syncButtonText}>立即同步</Text>
        </TouchableOpacity>

        {/* 新增：查看家庭账单按钮 */}
        <TouchableOpacity
          style={[styles.viewFamilyBillButton, {backgroundColor: '#FF9500'}]}
          onPress={viewFamilyBills}>
          <Icon
            name="file-invoice-dollar"
            size={16}
            color="#FFFFFF"
            style={{marginRight: 8}}
          />
          <Text style={styles.syncButtonText}>查看家庭账单</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderInviteModal = () => (
    <Modal
      visible={inviteModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setInviteModalVisible(false)}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>邀请家人加入</Text>

          <View style={styles.codeContainer}>
            <Text style={styles.codeLabel}>家庭邀请码</Text>
            <Text style={styles.codeText}>{familyCode}</Text>
            <Text style={styles.codeInstruction}>
              将此邀请码分享给家人，让他们在"家庭账单"页面中选择"加入家庭"并输入此邀请码
            </Text>
          </View>
          <View style={styles.modalButtonRow}>
            <TouchableOpacity
              style={[styles.modalButton, {backgroundColor: colors.primary}]}
              onPress={() => setInviteModalVisible(false)}>
              <Text style={styles.modalButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderJoinModal = () => (
    <Modal
      visible={joinModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setJoinModalVisible(false)}>
      <TouchableWithoutFeedback onPress={() => setJoinModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>加入家庭</Text>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>家庭邀请码</Text>
                <TextInput
                  style={styles.input}
                  value={inputFamilyCode}
                  onChangeText={text => setInputFamilyCode(text.toUpperCase())}
                  placeholder="输入邀请码"
                  autoCapitalize="characters"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>您的名字</Text>
                <TextInput
                  style={styles.input}
                  value={joinMemberName}
                  onChangeText={setJoinMemberName}
                  placeholder="请输入您的名字"
                  maxLength={10}
                />
                <Text style={styles.inputTip}>*加入后无法修改</Text>
              </View>

              <View style={styles.modalButtonRow}>
                <TouchableOpacity
                  style={[styles.modalButton, {backgroundColor: '#EEEEEE'}]}
                  onPress={() => setJoinModalVisible(false)}>
                  <Text style={[styles.modalButtonText, {color: '#333333'}]}>
                    取消
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.modalButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={joinFamily}>
                  <Text style={styles.modalButtonText}>加入</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 添加家庭设置模态框
  const renderSettingsModal = () => (
    <Modal
      visible={showSettingsModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowSettingsModal(false)}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>家庭设置</Text>
          <View style={styles.settingsOptions}>
            <TouchableOpacity
              style={[styles.settingsOption, styles.dangerOption]}
              onPress={leaveFamily}>
              <Icon
                name="right-from-bracket"
                size={20}
                color="#FF3B30"
                style={styles.settingsIcon}
              />
              <View style={styles.settingsTextContainer}>
                <Text style={[styles.settingsText, styles.dangerText]}>
                  退出家庭
                </Text>
                <Text style={styles.settingsDescription}>
                  不再接收此家庭的账单数据
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          <View style={styles.modalButtonRow}>
            <TouchableOpacity
              style={[styles.modalButton, {backgroundColor: '#EEEEEE'}]}
              onPress={() => setShowSettingsModal(false)}>
              <Text style={[styles.modalButtonText, {color: '#000000'}]}>
                关闭
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // 添加创建家庭弹窗
  const renderCreateFamilyModal = () => (
    <Modal
      visible={createFamilyModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setCreateFamilyModalVisible(false)}>
      <TouchableWithoutFeedback
        onPress={() => setCreateFamilyModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>创建家庭</Text>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>家庭名称</Text>
                <TextInput
                  style={styles.input}
                  value={newFamilyName}
                  onChangeText={setNewFamilyName}
                  placeholder="请输入家庭名称"
                  maxLength={20}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>您的名字</Text>
                <TextInput
                  style={styles.input}
                  value={newMemberName}
                  onChangeText={setNewMemberName}
                  placeholder="请输入您的名字"
                  maxLength={10}
                />
                <Text style={styles.inputTip}>*创建后无法修改</Text>
              </View>

              <View style={styles.modalButtonRow}>
                <TouchableOpacity
                  style={[styles.modalButton, {backgroundColor: '#EEEEEE'}]}
                  onPress={() => setCreateFamilyModalVisible(false)}>
                  <Text style={[styles.modalButtonText, {color: '#333333'}]}>
                    取消
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.modalButton,
                    {backgroundColor: colors.primary},
                  ]}
                  onPress={createFamily}>
                  <Text style={styles.modalButtonText}>创建</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  // 添加测试按钮渲染函数
  const renderTestButton = () => {
    // 仅在开发环境下显示
    if (!__DEV__) {
      return null;
    }

    return (
      <View style={styles.testButtonContainer}>
        <TouchableOpacity style={styles.testButton} onPress={addTestMember}>
          <Text style={styles.testButtonText}>添加虚拟成员(仅测试用)</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 添加获取同步状态消息的辅助函数
  const getSyncStatusMessage = status => {
    switch (status) {
      case 'idle':
        return '准备同步';
      case 'scanning':
        return '正在扫描设备...';
      case 'connecting':
        return '正在连接设备...';
      case 'syncing':
        return '正在同步数据...';
      case 'completed':
        return '同步完成';
      case 'failed':
        return '同步失败';
      default:
        return '';
    }
  };

  // 添加停止同步功能
  const stopSync = () => {
    syncService.stopSync();
    setSyncModalVisible(false);
  };

  // 添加同步状态模态框渲染函数
  const renderSyncModal = () => (
    <Modal
      visible={syncModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => {
        if (
          syncStatus !== 'scanning' &&
          syncStatus !== 'connecting' &&
          syncStatus !== 'syncing'
        ) {
          setSyncModalVisible(false);
        }
      }}>
      <View style={styles.modalOverlay}>
        <View style={styles.syncModalContent}>
          <Text style={styles.syncModalTitle}>
            {syncStatus === 'completed'
              ? '同步完成'
              : syncStatus === 'failed'
              ? '同步失败'
              : '正在同步数据'}
          </Text>

          {(syncStatus === 'scanning' ||
            syncStatus === 'connecting' ||
            syncStatus === 'syncing') && (
            <View style={styles.syncSpinner}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}

          {syncStatus === 'completed' && (
            <Icon
              name="circle-check"
              size={50}
              color="#4CD964"
              style={styles.syncStatusIcon}
            />
          )}

          {syncStatus === 'failed' && (
            <Icon
              name="circle-xmark"
              size={50}
              color="#FF3B30"
              style={styles.syncStatusIcon}
            />
          )}

          <Text style={styles.syncStatusMessage}>{syncMessage}</Text>

          {discoveredDevices.length > 0 && (
            <View style={styles.discoveredDevicesContainer}>
              <Text style={styles.discoveredDevicesTitle}>发现的设备</Text>
              {discoveredDevices.map(device => (
                <View key={device.id} style={styles.discoveredDeviceItem}>
                  <Icon
                    name={device.rssi ? 'bluetooth' : 'wifi'}
                    size={16}
                    color={colors.primary}
                    style={{marginRight: 8}}
                  />
                  <Text style={styles.discoveredDeviceName}>
                    {device.name ||
                      device.syncData?.deviceName ||
                      device.id ||
                      '未知设备'}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {(syncStatus === 'scanning' ||
            syncStatus === 'connecting' ||
            syncStatus === 'syncing') && (
            <TouchableOpacity
              style={styles.cancelSyncButton}
              onPress={stopSync}>
              <Text style={styles.cancelSyncButtonText}>取消</Text>
            </TouchableOpacity>
          )}

          {(syncStatus === 'completed' || syncStatus === 'failed') && (
            <TouchableOpacity
              style={[styles.closeSyncButton, {backgroundColor: colors.primary}]}
              onPress={() => setSyncModalVisible(false)}>
              <Text style={styles.closeSyncButtonText}>关闭</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <PageContainer headerTitle="家庭账单" backgroundColor={COLORS.secondary}>
      <View style={styles.container}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text>加载中...</Text>
          </View>
        ) : !hasFamily ? (
          renderNoFamilyView()
        ) : (
          <FlatList
            data={members}
            renderItem={renderMemberItem}
            keyExtractor={item => item.id.toString()}
            ListHeaderComponent={renderHeader}
            ListFooterComponent={renderTestButton}
            contentContainerStyle={styles.membersList}
          />
        )}
      </View>

      {renderInviteModal()}
      {renderJoinModal()}
      {renderSettingsModal()}
      {renderCreateFamilyModal()}
      {renderSyncModal()}

      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        onCancel={() => setConfirmDialogVisible(false)}
        confirmText="确定"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginTop: 20,
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 14,
    color: COLORS.text.gray,
    textAlign: 'center',
    marginBottom: 30,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 20,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 2,
  },
  headerTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  familyName: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginBottom: 10,
    fontWeight: '600',
  },
  headerText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  syncButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  membersList: {
    padding: 16,
  },
  memberItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 1,
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '700',
  },
  memberDetails: {
    flex: 1,
  },
  namebox: {
    flexDirection: 'row',
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  memberStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  memberStatus: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginRight: 10,
  },
  syncTime: {
    fontSize: 12,
    color: COLORS.text.gray,
  },
  adminBadge: {
    marginLeft: 10,
    backgroundColor: '#FFF9C4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  adminText: {
    color: '#FF9500',
    fontSize: 10,
    fontWeight: '500',
  },
  selfBadge: {
    marginLeft: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E3F2FD',
  },
  selfText: {
    color: '#2196F3',
    fontSize: 10,
    fontWeight: '600',
  },
  kickButton: {
    marginLeft: -20,
    marginTop: -40,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputTip: {
    fontSize: 12,
    color: '#FF3B30',
    marginTop: 4,
  },
  testButtonContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#FFCC00',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  testButtonText: {
    color: '#333333',
    fontWeight: '600',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewFamilyBillButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  codeContainer: {
    backgroundColor: '#F9F9F9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  codeLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  codeText: {
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  codeInstruction: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  modalButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    width: '100%',
  },
  inviteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 16,
    marginRight: 8,
  },
  inviteButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingsButton: {
    padding: 8,
  },
  settingsOptions: {
    width: '100%',
    marginVertical: 10,
  },
  settingsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  dangerOption: {
    borderBottomWidth: 0,
  },
  settingsIcon: {
    marginRight: 12,
  },
  settingsTextContainer: {
    flex: 1,
  },
  settingsText: {
    fontSize: 16,
  },
  dangerText: {
    color: '#FF3B30',
  },
  settingsDescription: {
    fontSize: 13,
    color: '#666666',
    marginTop: 2,
  },
  modalText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  inviteCode: {
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsOptionText: {
    fontSize: 16,
  },
  // 同步模态框样式
  syncModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
  },
  syncModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  syncSpinner: {
    marginBottom: 20,
  },
  syncStatusIcon: {
    marginBottom: 20,
  },
  syncStatusMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  discoveredDevicesContainer: {
    width: '100%',
    marginTop: 10,
    marginBottom: 20,
  },
  discoveredDevicesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  discoveredDeviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 10,
    borderRadius: 8,
    marginBottom: 6,
  },
  discoveredDeviceName: {
    fontSize: 14,
  },
  cancelSyncButton: {
    backgroundColor: '#EEEEEE',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  cancelSyncButtonText: {
    color: '#666666',
    fontSize: 14,
    fontWeight: '600',
  },
  closeSyncButton: {
    paddingVertical: 10,
    paddingHorizontal: 30,
  },
  closeSyncButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default FamilyMembersScreen;
