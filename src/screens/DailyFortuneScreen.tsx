import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import CustomModal from '../components/CustomModal';
import ConfirmDialog from '../components/ConfirmDialog';
import {COLORS} from '../utils/color';
import {useToast} from '../context/ToastContext';
import {
  calculateDailyFortune,
  generateCareerAnalysis,
  generateWealthAnalysis,
  generateLoveAnalysis,
  generateHealthAnalysis,
  generateStudyAnalysis,
  generateOverallAnalysis
} from '../utils/algorithm';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';

// 辅助函数
const getFortuneColor = (level) => {
  switch (level) {
    case '大吉': return '#34C759';
    case '中吉': return '#30D158';
    case '小吉': return '#FFCC00';
    case '平': return '#FF9500';
    case '小凶': return '#FF6B35';
    case '凶': return '#FF3B30';
    default: return '#8E8E93';
  }
};

// 所有分析函数已从algorithm.js导入，删除本地重复定义

// 继续删除重复的本地函数定义

// 添加缺失的辅助函数
const getScoreColor = (score) => {
  if (score >= 80) return '#34C759'; // 绿色
  if (score >= 60) return '#FFD60A'; // 黄色
  if (score >= 40) return '#FF9500'; // 橙色
  return '#FF3B30'; // 红色
};

const DailyFortuneScreen = ({navigation}) => {
  const {showToast} = useToast();

  const [userInfo, setUserInfo] = useState<any>(null);
  const [fortuneData, setFortuneData] = useState<any>(null);
  const [showUserInfoModal, setShowUserInfoModal] = useState(false);
  const [birthYear, setBirthYear] = useState('');
  const [birthMonth, setBirthMonth] = useState('');
  const [birthDay, setBirthDay] = useState('');
  const [loading, setLoading] = useState(true);

  // ConfirmDialog 状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(() => {});

  const loadUserInfo = async () => {
    try {
      const savedUserInfo = await AsyncStorage.getItem('fortuneUserInfo');
      if (savedUserInfo) {
        const userInfoData = JSON.parse(savedUserInfo);
        setUserInfo(userInfoData);
        calculateFortune(userInfoData);
      } else {
        setShowUserInfoModal(true);
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserInfo();
  }, []);

  const calculateFortune = (userInfoData: any) => {
    const fortune = calculateDailyFortune(userInfoData);
    setFortuneData(fortune);
  };

  const saveUserInfo = async () => {
    if (!birthYear || birthYear.length !== 4) {
      showToast('请输入正确的出生年份', 'warning');
      return;
    }

    const year = parseInt(birthYear, 10);
    const month = birthMonth ? parseInt(birthMonth, 10) : null;
    const day = birthDay ? parseInt(birthDay, 10) : null;

    if (year < 1900 || year > new Date().getFullYear()) {
      showToast('请输入有效的出生年份', 'warning');
      return;
    }

    if (month && (month < 1 || month > 12)) {
      showToast('请输入有效的月份(1-12)', 'warning');
      return;
    }

    if (day && (day < 1 || day > 31)) {
      showToast('请输入有效的日期(1-31)', 'warning');
      return;
    }

    const userInfoData = {
      year,
      month,
      day,
      createdAt: new Date().toISOString(),
    };

    try {
      await AsyncStorage.setItem('fortuneUserInfo', JSON.stringify(userInfoData));
      setUserInfo(userInfoData);
      setShowUserInfoModal(false);
      calculateFortune(userInfoData);
      showToast('信息保存成功', 'success');
    } catch (error) {
      console.error('保存用户信息失败:', error);
      showToast('保存失败，请重试', 'error');
    }
  };

  const resetUserInfo = () => {
    setConfirmDialogMessage('确定要重新设置出生信息吗？');
    setConfirmDialogAction(() => async () => {
      try {
        await AsyncStorage.removeItem('fortuneUserInfo');
        setUserInfo(null);
        setFortuneData(null);
        setBirthYear('');
        setBirthMonth('');
        setBirthDay('');
        setShowUserInfoModal(true);
      } catch (error) {
        console.error('重置失败:', error);
      }
    });
    setConfirmDialogVisible(true);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) {return '#34C759';}
    if (score >= 60) {return '#FFCC00';}
    if (score >= 40) {return '#FF9500';}
    return '#FF3B30';
  };

  const getScoreText = (score: number) => {
    if (score >= 80) {return '极佳';}
    if (score >= 60) {return '良好';}
    if (score >= 40) {return '一般';}
    return '欠佳';
  };

  const renderStars = (score: number) => {
    const stars = Math.round(score / 20);
    return Array.from({length: 5}, (_, index) => (
      <Icon
        key={index}
        name="star"
        size={16}
        color={index < stars ? '#FFD700' : '#E0E0E0'}
        solid={index < stars}
      />
    ));
  };

  if (loading) {
    return (
      <PageContainer headerTitle="每日运势" backgroundColor={COLORS.background.light}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, {color: COLORS.text.primary}]}>加载中...</Text>
        </View>
      </PageContainer>
    );
  }

  return (
    <PageContainer headerTitle="每日运势" backgroundColor={COLORS.background.light}>
      <ScrollView style={styles.container}>
        {fortuneData ? (
          <>
            {/* 今日日期 */}
            <View style={[styles.dateCard, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.dateText, {color: COLORS.text.primary}]}>
                {dayjs().format('YYYY年MM月DD日')}
              </Text>
              <Text style={[styles.dayText, {color: COLORS.text.gray}]}>
                {dayjs().format('dddd')}
              </Text>
            </View>

            {/* 综合运势 */}
            <View style={[styles.overallCard, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>综合运势</Text>
              <View style={styles.overallContent}>
                <View style={styles.scoreContainer}>
                  <Text style={[styles.scoreNumber, {color: getScoreColor(fortuneData.overall)}]}>
                    {fortuneData.overall}
                  </Text>
                  <Text style={[styles.scoreLabel, {color: COLORS.text.gray}]}>
                    {getScoreText(fortuneData.overall)}
                  </Text>
                </View>
                <View style={styles.starsContainer}>
                  {renderStars(fortuneData.overall)}
                </View>
              </View>
            </View>

            {/* 生肖运势 */}
            <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>生肖运势</Text>
              <View style={styles.zodiacContent}>
                <View style={styles.zodiacInfo}>
                  <Text style={[styles.zodiacName, {color: COLORS.text.primary}]}>
                    {fortuneData.zodiac.name}
                  </Text>
                  <Text style={[styles.zodiacElement, {color: COLORS.text.gray}]}>
                    五行属{fortuneData.zodiac.element}
                  </Text>
                </View>
                <View style={styles.zodiacScore}>
                  <Text style={[styles.scoreNumber, {color: getScoreColor(fortuneData.zodiac.score)}]}>
                    {fortuneData.zodiac.score}
                  </Text>
                </View>
              </View>
            </View>

            {/* 梅花易数卦象分析 */}
            {fortuneData.meihua && (
              <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
                <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>梅花易数</Text>
                <View style={styles.guaContainer}>
                  <View style={styles.guaItem}>
                    <Text style={[styles.guaLabel, {color: COLORS.text.gray}]}>本卦</Text>
                    <Text style={[styles.guaName, {color: COLORS.primary}]}>
                      {fortuneData.meihua.originalGua.upper.name}{fortuneData.meihua.originalGua.lower.name}
                    </Text>
                    <Text style={[styles.guaElement, {color: COLORS.text.gray}]}>
                      {fortuneData.meihua.originalGua.upper.meaning}·{fortuneData.meihua.originalGua.lower.meaning}
                    </Text>
                  </View>
                  <View style={styles.guaArrow}>
                    <Icon name="arrow-right" size={16} color={COLORS.text.gray} />
                  </View>
                  <View style={styles.guaItem}>
                    <Text style={[styles.guaLabel, {color: COLORS.text.gray}]}>变卦</Text>
                    <Text style={[styles.guaName, {color: COLORS.primary}]}>
                      {fortuneData.meihua.changedGua.upper.name}{fortuneData.meihua.changedGua.lower.name}
                    </Text>
                    <Text style={[styles.guaElement, {color: COLORS.text.gray}]}>
                      第{fortuneData.meihua.originalGua.changingLine}爻动
                    </Text>
                  </View>
                </View>
                <Text style={[styles.interpretationText, {color: COLORS.text.primary}]}>
                  {fortuneData.meihua.interpretation.mainInterpretation}
                </Text>
                <Text style={[styles.changingLineText, {color: COLORS.text.gray}]}>
                  {fortuneData.meihua.interpretation.changingLineAdvice}
                </Text>
              </View>
            )}

            {/* 大衍之数分析 */}
            {fortuneData.dayan && (
              <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
                <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>大衍之数</Text>
                <View style={styles.dayanContainer}>
                  <View style={styles.fortuneLevelContainer}>
                    <Text style={[styles.fortuneLevel, {color: getFortuneColor(fortuneData.dayan.fortuneLevel.level)}]}>
                      {fortuneData.dayan.fortuneLevel.level}
                    </Text>
                    <Text style={[styles.fortuneDescription, {color: COLORS.text.primary}]}>
                      {fortuneData.dayan.fortuneLevel.description}
                    </Text>
                  </View>
                  <View style={styles.personalGuaContainer}>
                    <Text style={[styles.guaLabel, {color: COLORS.text.gray}]}>本命卦象</Text>
                    <Text style={[styles.guaName, {color: COLORS.primary}]}>
                      {fortuneData.dayan.personalGua.name}卦
                    </Text>
                    <Text style={[styles.guaTraits, {color: COLORS.text.gray}]}>
                      {fortuneData.dayan.personalGua.traits.join('·')}
                    </Text>
                  </View>
                </View>
              </View>
            )}

            {/* 六大运势分析 */}
            <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>六大运势分析</Text>

              {/* 事业运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="briefcase" size={18} color="#5856D6" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>事业运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.constellation?.scores.career || fortuneData.overall)}]}>
                    {fortuneData.constellation?.scores.career || fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateCareerAnalysis(fortuneData)}
                </Text>
              </View>

              {/* 财运运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="coins" size={18} color="#FFD60A" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>财运运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.constellation?.scores.wealth || fortuneData.overall)}]}>
                    {fortuneData.constellation?.scores.wealth || fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateWealthAnalysis(fortuneData)}
                </Text>
              </View>

              {/* 感情运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="heart" size={18} color="#FF2D55" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>感情运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.constellation?.scores.love || fortuneData.overall)}]}>
                    {fortuneData.constellation?.scores.love || fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateLoveAnalysis(fortuneData)}
                </Text>
              </View>

              {/* 健康运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="heart-pulse" size={18} color="#34C759" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>健康运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.constellation?.scores.health || fortuneData.overall)}]}>
                    {fortuneData.constellation?.scores.health || fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateHealthAnalysis(fortuneData)}
                </Text>
              </View>

              {/* 学业运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="graduation-cap" size={18} color="#007AFF" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>学业运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.overall)}]}>
                    {fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateStudyAnalysis(fortuneData)}
                </Text>
              </View>

              {/* 综合运势 */}
              <View style={styles.fortuneCategory}>
                <View style={styles.categoryHeader}>
                  <Icon name="chart-line" size={18} color="#AF52DE" />
                  <Text style={[styles.categoryTitle, {color: COLORS.text.primary}]}>综合运势</Text>
                  <Text style={[styles.categoryScore, {color: getScoreColor(fortuneData.overall)}]}>
                    {fortuneData.overall}分
                  </Text>
                </View>
                <Text style={[styles.categoryAnalysis, {color: COLORS.text.primary}]}>
                  {generateOverallAnalysis(fortuneData)}
                </Text>
              </View>
            </View>

            {/* 基础信息 */}
            <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>基础信息</Text>
              <View style={styles.basicInfoGrid}>
                <View style={styles.basicInfoItem}>
                  <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>生肖</Text>
                  <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                    {fortuneData.zodiac.name}
                  </Text>
                </View>
                {fortuneData.constellation && (
                  <View style={styles.basicInfoItem}>
                    <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>星座</Text>
                    <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                      {fortuneData.constellation.name}
                    </Text>
                  </View>
                )}
                <View style={styles.basicInfoItem}>
                  <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>五行</Text>
                  <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                    {fortuneData.wuxing.userElement}
                  </Text>
                </View>
                <View style={styles.basicInfoItem}>
                  <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>纳音</Text>
                  <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                    {fortuneData.wuxing.nayinWuxing}
                  </Text>
                </View>
                <View style={styles.basicInfoItem}>
                  <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>幸运数字</Text>
                  <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                    {fortuneData.luckyNumbers.join(', ')}
                  </Text>
                </View>
                <View style={styles.basicInfoItem}>
                  <Text style={[styles.basicInfoLabel, {color: COLORS.text.gray}]}>幸运颜色</Text>
                  <Text style={[styles.basicInfoValue, {color: COLORS.text.primary}]}>
                    {fortuneData.luckyColors.join(', ')}
                  </Text>
                </View>
              </View>
            </View>

            {/* 专业建议 */}
            <View style={[styles.card, {backgroundColor: COLORS.background.white}]}>
              <Text style={[styles.cardTitle, {color: COLORS.text.primary}]}>专业建议</Text>
              {fortuneData.suggestions.map((suggestion, index) => (
                <View key={index} style={styles.suggestionItem}>
                  <Text style={[styles.suggestionType, {color: COLORS.primary}]}>
                    {suggestion.type}
                  </Text>
                  <Text style={[styles.suggestionText, {color: COLORS.text.primary}]}>
                    {suggestion.text}
                  </Text>
                </View>
              ))}
            </View>

            {/* 重置按钮 */}
            <TouchableOpacity style={styles.resetButton} onPress={resetUserInfo}>
              <Icon name="rotate-right" size={16} color={COLORS.text.gray} />
              <Text style={[styles.resetText, {color: COLORS.text.gray}]}>
                重新设置出生信息
              </Text>
            </TouchableOpacity>
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="star" size={60} color={COLORS.text.gray} />
            <Text style={[styles.emptyText, {color: COLORS.text.primary}]}>
              请先设置您的出生信息
            </Text>
            <TouchableOpacity
              style={[styles.setupButton, {backgroundColor: COLORS.primary}]}
              onPress={() => setShowUserInfoModal(true)}>
              <Text style={styles.setupButtonText}>设置信息</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* 用户信息输入模态框 */}
      <CustomModal
        visible={showUserInfoModal}
        onClose={() => setShowUserInfoModal(false)}
        animationType="slide"
        position="center"
        closeOnBackdropPress={true}
        customStyle={styles.modalContent}>
        {/* 关闭按钮 */}
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => setShowUserInfoModal(false)}>
          <Icon name="xmark" size={20} color={COLORS.text.gray} />
        </TouchableOpacity>

        <Text style={[styles.modalTitle, {color: COLORS.text.primary}]}>设置出生信息</Text>
        <Text style={[styles.modalSubtitle, {color: COLORS.text.gray}]}>
          用于计算您的专属运势
        </Text>

        {/* 日期类型说明 */}
        <View style={styles.dateTypeNotice}>
          {/* <Icon name="info-circle" size={16} color={COLORS.primary} /> */}
          <Text style={[styles.dateTypeText, {color: COLORS.text.gray}]}>
            请填写阳历（公历）出生日期，系统会自动进行运势计算
          </Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, {color: COLORS.text.primary}]}>出生年份 *</Text>
          <TextInput
            style={[styles.input, {borderColor: COLORS.border.light, color: COLORS.text.primary}]}
            value={birthYear}
            onChangeText={setBirthYear}
            placeholder="例如：1990"
            placeholderTextColor={COLORS.text.gray}
            keyboardType="numeric"
            maxLength={4}
          />
        </View>

        <View style={styles.inputRow}>
          <View style={[styles.inputContainer, styles.inputHalf]}>
            <Text style={[styles.inputLabel, {color: COLORS.text.primary}]}>月份（可选）</Text>
            <TextInput
              style={[styles.input, {borderColor: COLORS.border.light, color: COLORS.text.primary}]}
              value={birthMonth}
              onChangeText={setBirthMonth}
              placeholder="月"
              placeholderTextColor={COLORS.text.gray}
              keyboardType="numeric"
              maxLength={2}
            />
          </View>
          <View style={[styles.inputContainer, styles.inputHalf]}>
            <Text style={[styles.inputLabel, {color: COLORS.text.primary}]}>日期（可选）</Text>
            <TextInput
              style={[styles.input, {borderColor: COLORS.border.light, color: COLORS.text.primary}]}
              value={birthDay}
              onChangeText={setBirthDay}
              placeholder="日"
              placeholderTextColor={COLORS.text.gray}
              keyboardType="numeric"
              maxLength={2}
            />
          </View>
        </View>

        <Text style={[styles.noteText, {color: COLORS.text.gray}]}>
          * 年份必填，月日可选。填写阳历（公历）月日可获得更准确的星座运势分析。
        </Text>

        <View style={styles.modalButtons}>
          {userInfo && (
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowUserInfoModal(false)}>
              <Text style={[styles.buttonText, {color: COLORS.text.primary}]}>取消</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[styles.modalButton, styles.confirmButton, {backgroundColor: COLORS.primary}]}
            onPress={saveUserInfo}>
            <Text style={styles.confirmButtonText}>确定</Text>
          </TouchableOpacity>
        </View>
      </CustomModal>

      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="重置信息"
        message={confirmDialogMessage}
        onCancel={() => setConfirmDialogVisible(false)}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        cancelText="取消"
        confirmText="确定"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  dateCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
  },
  dateText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  dayText: {
    fontSize: 14,
    marginTop: 4,
  },
  overallCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  overallContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreNumber: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  starsContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  zodiacContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  zodiacInfo: {
    flex: 1,
  },
  zodiacName: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  zodiacElement: {
    fontSize: 14,
    marginTop: 4,
  },
  zodiacScore: {
    alignItems: 'center',
  },
  constellationHeader: {
    marginBottom: 16,
  },
  constellationName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  constellationElement: {
    fontSize: 14,
    marginTop: 4,
  },
  dimensionsContainer: {
    gap: 12,
  },
  dimensionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dimensionLabel: {
    fontSize: 16,
    flex: 1,
  },
  dimensionScore: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dimensionNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    minWidth: 30,
    textAlign: 'center',
  },
  dimensionStars: {
    flexDirection: 'row',
    gap: 2,
  },
  wuxingContent: {
    gap: 12,
  },
  wuxingInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wuxingText: {
    fontSize: 16,
  },
  wuxingRelation: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  wuxingScore: {
    alignItems: 'center',
  },
  luckyContainer: {
    gap: 16,
  },
  luckySection: {
    gap: 8,
  },
  luckyLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  luckyNumbers: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  numberBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  numberText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  luckyColors: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
  },
  colorText: {
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 12,
  },
  suggestionItem: {
    marginBottom: 12,
  },
  suggestionType: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  suggestionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 16,
    marginBottom: 20,
  },
  resetText: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  setupButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  setupButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 24,
    position: 'relative',
    backgroundColor: COLORS.background.white,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  dateTypeNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    gap: 8,
  },
  dateTypeText: {
    fontSize: 12,
    flex: 1,
    lineHeight: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  inputHalf: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  noteText: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  confirmButton: {
    // backgroundColor set via style prop
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // 新增样式
  guaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background.light,
    borderRadius: 8,
  },
  guaItem: {
    alignItems: 'center',
    flex: 1,
  },
  guaLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  guaName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  guaElement: {
    fontSize: 12,
  },
  guaArrow: {
    marginHorizontal: 16,
  },
  interpretationText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  changingLineText: {
    fontSize: 12,
    lineHeight: 18,
    fontStyle: 'italic',
  },
  dayanContainer: {
    marginBottom: 12,
  },
  fortuneLevelContainer: {
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background.light,
    borderRadius: 8,
  },
  fortuneLevel: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  fortuneDescription: {
    fontSize: 14,
    textAlign: 'center',
  },
  personalGuaContainer: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  guaTraits: {
    fontSize: 12,
    marginTop: 2,
  },
  fortuneCategory: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border.light,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  categoryScore: {
    fontSize: 16,
    fontWeight: '700',
  },
  categoryAnalysis: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 26,
  },
  basicInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  basicInfoItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 12,
  },
  basicInfoLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  basicInfoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default DailyFortuneScreen;
