import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { COLORS } from '../utils/color';
import { useTheme } from '../context/ThemeContext';
const { width, height } = Dimensions.get('window');


const WelcomeScreen = ({ navigation }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const {colors} = useTheme();
  // 欢迎页内容
  const welcomePages = [
    {
      title: '欢迎使用雪球记账',
      description: '简单易用的本地记账工具，帮助您轻松管理个人财务',
      icon: '💰',
    },
    {
      title: '本地数据存储',
      description: '所有数据均存储在您的设备上，不会上传到云端，保护您的隐私',
      icon: '🔒',
    },
    {
      title: '数据备份提醒',
      description: '卸载应用或清除缓存可能导致数据丢失，请定期使用导出功能备份您的数据',
      icon: '⚠️',
    },
    {
      title: '开始使用',
      description: '点击"开始使用"按钮，开启您的记账之旅',
      icon: '🚀',
    },
  ];

  // 处理开始使用按钮点击
  const handleGetStarted = async () => {
    try {
      // 标记用户已经看过欢迎页
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      // 导航到主页
      navigation.reset({
        index: 0,
        routes: [{ name: 'home' }],
      });
    } catch (error) {
      console.error('保存欢迎页状态失败', error);
    }
  };

  // 处理页面切换
  const handlePageChange = (index) => {
    setCurrentPage(index);
    // 滚动到对应页面
    scrollViewRef.current?.scrollTo({
      x: index * width,
      animated: true,
    });
  };

  // 渲染指示器
  const renderIndicators = () => {
    return (
      <View style={styles.indicatorContainer}>
        {welcomePages.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.indicator,
              currentPage === index && styles.activeIndicator,
              {backgroundColor: colors.primary},
            ]}
            onPress={() => handlePageChange(index)}
          />
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={Platform.OS === 'android'}
      />

      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const newPage = Math.round(event.nativeEvent.contentOffset.x / width);
          setCurrentPage(newPage);
        }}
      >
        {welcomePages.map((page, index) => (
          <View key={index} style={styles.page}>
            <Text style={styles.icon}>{page.icon}</Text>
            <Text style={styles.title}>{page.title}</Text>
            <Text style={styles.description}>{page.description}</Text>
          </View>
        ))}
      </ScrollView>

      {renderIndicators()}

      <View style={styles.buttonContainer}>
        {currentPage < welcomePages.length - 1 ? (
          <TouchableOpacity
            style={styles.nextButton}
            onPress={() => handlePageChange(currentPage + 1)}
          >
            <Text style={styles.nextButtonText}>下一步</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.getStartedButton, {backgroundColor: colors.primary}]}
            onPress={handleGetStarted}
          >
            <Text style={styles.getStartedButtonText}>开始使用</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.white,
  },
  page: {
    width,
    height: height * 0.8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  icon: {
    fontSize: 80,
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: COLORS.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  indicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.text.secondary,
    marginHorizontal: 5,
  },
  activeIndicator: {
    width: 20,
  },
  buttonContainer: {
    paddingHorizontal: 30,
    paddingBottom: 50,
  },
  nextButton: {
    backgroundColor: COLORS.background.light,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  nextButtonText: {
    color: COLORS.primary,
    fontSize: 18,
    fontWeight: '600',
  },
  getStartedButton: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  getStartedButtonText: {
    color: COLORS.background.white,
    fontSize: 18,
    fontWeight: '600',
  },
});

export default WelcomeScreen;
