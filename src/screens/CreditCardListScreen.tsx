import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {CreditCard} from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import {useToast} from '../context/ToastContext';
import {useFocusEffect} from '@react-navigation/native';
import notificationService from '../services/NotificationService';
import { useTheme } from '../context/ThemeContext';
const CreditCardListScreen = ({navigation}) => {
  const {showToast} = useToast();
  const {colors} = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [creditCards, setCreditCards] = useState<CreditCard[]>([]);

  // 当页面获得焦点时加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadCreditCards();
    }, []),
  );

  useEffect(() => {
    loadCreditCards();
  }, []);

  const loadCreditCards = async () => {
    setIsLoading(true);
    try {
      const cards = await databaseService.getAllCreditCards();
      setCreditCards(cards);
    } catch (error) {
      console.error('加载信用卡失败', error);
      showToast('加载信用卡数据失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 计算下次账单日和还款日
  const calculateNextDates = (billingDay: number, paymentDueDay: number) => {
    let now = dayjs();
    let nextBillingDate, nextPaymentDate;

    // 计算下次账单日
    if (now.date() > billingDay) {
      // 如果当前日期已经过了本月账单日，则下次账单日在下个月
      nextBillingDate = now.add(1, 'month').set('date', billingDay);
    } else {
      // 否则下次账单日就在本月
      nextBillingDate = now.set('date', billingDay);
    }

    // 计算下次还款日
    if (now.date() > paymentDueDay) {
      // 如果当前日期已经过了本月还款日，则下次还款日在下个月
      nextPaymentDate = now.add(1, 'month').set('date', paymentDueDay);
    } else {
      // 否则下次还款日就在本月
      nextPaymentDate = now.set('date', paymentDueDay);
    }

    return {
      nextBillingDate: nextBillingDate.format('MM/DD'),
      nextPaymentDate: nextPaymentDate.format('MM/DD'),
      daysUntilPayment: nextPaymentDate.diff(now, 'day'),
    };
  };

  const renderCreditCardItem = ({item}: {item: CreditCard}) => {
    const {nextBillingDate, nextPaymentDate, daysUntilPayment} = calculateNextDates(
      item.billingDay,
      item.paymentDueDay,
    );

    // 获取紧急程度配置
    const getUrgencyConfig = (days: number) => {
      if (days <= 3) {
        return {
          color: '#FF3B30',
          bgColor: '#FFE5E5',
          icon: 'triangle-exclamation',
          label: '紧急',
        };
      } else if (days <= 7) {
        return {
          color: '#FF9500',
          bgColor: '#FFF3E0',
          icon: 'clock',
          label: '提醒',
        };
      } else {
        return {
          color: '#34C759',
          bgColor: '#E8F5E8',
          icon: 'check-circle',
          label: '正常',
        };
      }
    };

    const urgencyConfig = getUrgencyConfig(daysUntilPayment);

    return (
      <TouchableOpacity
        style={styles.cardItem}
        onPress={() => navigation.navigate('creditCardDetail', {cardId: item.id})}
        activeOpacity={0.8}>
        <View style={[styles.cardTop, {backgroundColor: item.color || COLORS.primary}]}>
          <View style={styles.cardTopHeader}>
            <View style={styles.cardTopLeft}>
              <Text style={styles.bankName}>{item.bankName}</Text>
              <Text style={styles.cardNumber}>**** **** **** {item.lastThreeDigits}</Text>
            </View>
            <View style={styles.cardTopRight}>
              <Icon name="credit-card" size={20} color="rgba(255,255,255,0.8)" />
            </View>
          </View>
          <View style={styles.cardTopDates}>
            <View style={styles.cardTopDateItem}>
              <Icon name="calendar" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.cardTopDateLabel}>账单日 {item.billingDay}号</Text>
            </View>
            <View style={styles.cardTopDateItem}>
              <Icon name="clock" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.cardTopDateLabel}>还款日 {item.paymentDueDay}号</Text>
            </View>
          </View>
        </View>
        <View style={styles.cardBottom}>
          <View style={styles.dateInfoSection}>
            <View style={styles.dateItem}>
              <Text style={styles.dateLabel}>下次账单日</Text>
              <Text style={styles.dateValue}>{nextBillingDate}</Text>
            </View>
            <View style={styles.dateItem}>
              <Text style={styles.dateLabel}>下次还款日</Text>
              <Text style={styles.dateValue}>{nextPaymentDate}</Text>
            </View>
          </View>
          <View style={styles.urgencySection}>
            <View style={[styles.urgencyBadge, {backgroundColor: urgencyConfig.bgColor}]}>
              <Icon name={urgencyConfig.icon} size={14} color={urgencyConfig.color} />
              <Text style={[styles.urgencyLabel, {color: urgencyConfig.color}]}>
                {urgencyConfig.label}
              </Text>
            </View>
            <Text style={[styles.daysValue, {color: urgencyConfig.color}]}>
              {daysUntilPayment}天
            </Text>
            {daysUntilPayment <= 3 && (
              <Text style={styles.urgentText}>请尽快还款!</Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Icon name="credit-card" size={64} color="#E5E5EA" />
      </View>
      <Text style={styles.emptyText}>暂无信用卡</Text>
      <Text style={styles.emptySubText}>
        添加您的信用卡信息，我们将为您提供{'\n'}账单日和还款日的贴心提醒
      </Text>
      <TouchableOpacity
        style={[styles.addButton, {backgroundColor: colors.primary}]}
        onPress={() => navigation.navigate('creditCardAdd')}
        activeOpacity={0.8}>
        <Icon name="plus" size={16} color="#FFFFFF" style={styles.addButtonIcon} />
        <Text style={styles.addButtonText}>添加信用卡</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary} />
    </View>
  );

  return (
    <PageContainer headerTitle="信用卡还款提醒" backgroundColor={COLORS.background.light}>
      <View style={styles.container}>
        {isLoading ? (
          renderLoading()
        ) : (
          <>
            {creditCards.length > 0 ? (
              <FlatList
                data={creditCards}
                renderItem={renderCreditCardItem}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.listContainer}
              />
            ) : (
              renderEmptyState()
            )}
            {creditCards.length > 0 && (
              <TouchableOpacity
                style={[styles.floatingAddButton, {backgroundColor: colors.primary}]}
                onPress={() => navigation.navigate('creditCardAdd')}>
                <Icon name="plus" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            )}
            {__DEV__ && (
              <TouchableOpacity
                style={styles.testButton}
                onPress={() => {
                  notificationService.testNotification();
                  showToast('测试通知已发送', 'info');
                }}>
                <Text style={styles.testButtonText}>测试通知</Text>
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background.light,
  },
  listContainer: {
    padding: 16,
  },
  cardItem: {
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    marginHorizontal: 4,
    overflow: 'hidden',
  },
  cardTop: {
    padding: 24,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    position: 'relative',
  },
  cardTopHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardTopLeft: {
    flex: 1,
  },
  cardTopRight: {
    marginLeft: 16,
  },
  bankName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.6,
  },
  cardNumber: {
    fontSize: 17,
    color: '#FFFFFF',
    opacity: 0.9,
    letterSpacing: 1.2,
    fontFamily: 'Menlo',
  },
  cardTopDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cardTopDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTopDateLabel: {
    fontSize: 13,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
    marginLeft: 6,
    letterSpacing: -0.2,
  },
  cardBottom: {
    backgroundColor: '#FFFFFF',
    padding: 24,
    paddingTop: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  dateInfoSection: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  dateItem: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 13,
    color: COLORS.text.gray,
    marginBottom: 6,
    fontWeight: '500',
    letterSpacing: -0.2,
  },
  dateValue: {
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text.primary,
    letterSpacing: -0.5,
  },
  urgencySection: {
    alignItems: 'center',
  },
  urgencyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
  },
  urgencyLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    letterSpacing: -0.2,
  },
  daysValue: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  urgentText: {
    fontSize: 11,
    color: '#FF3B30',
    fontWeight: '600',
    letterSpacing: -0.1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 2,
    borderColor: '#F2F2F7',
    borderStyle: 'dashed',
  },
  emptyText: {
    fontSize: 22,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 12,
    letterSpacing: -0.6,
  },
  emptySubText: {
    fontSize: 16,
    color: COLORS.text.gray,
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.4,
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  testButton: {
    position: 'absolute',
    bottom: 94,
    right: 24,
    backgroundColor: '#FF9500', // iOS橙色
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: -0.3,
  },
});

export default CreditCardListScreen;
