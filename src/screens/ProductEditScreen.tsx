import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService from '../services/DatabaseService';
import {COLORS} from '../utils/color';
import dayjs from 'dayjs';
import CustomModal from '../components/CustomModal';
import {useToast} from '../context/ToastContext';
import ConfirmDialog from '../components/ConfirmDialog';
import { useTheme } from '../context/ThemeContext';
import { Picker, Modal } from '@ant-design/react-native';

const ProductEditScreen = ({navigation, route}) => {
  const {colors} = useTheme();
  const {productId} = route.params || {};
  const isEditing = Boolean(productId);
  const {showToast} = useToast();

  const [product, setProduct] = useState<any>({
    id: null,
    name: '',
    notes: '',
    createdAt: '',
    updatedAt: '',
    purchased: 0,
  });

  const [originalPrices, setOriginalPrices] = useState<any[]>([]);
  const [prices, setPrices] = useState<any[]>([]);
  const [pricesToAdd, setPricesToAdd] = useState<any[]>([]);
  const [pricesToUpdate, setPricesToUpdate] = useState<any[]>([]);
  const [pricesToDelete, setPricesToDelete] = useState<number[]>([]);
  const [newPrice, setNewPrice] = useState('');
  const [loading, setLoading] = useState(isEditing);
  const [editingPrice, setEditingPrice] = useState<any>(null);
  const [editingPriceValue, setEditingPriceValue] = useState('');
  const [priceModalVisible, setPriceModalVisible] = useState(false);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [newPlatform, setNewPlatform] = useState('');
  const [customPlatform, setCustomPlatform] = useState('');
  const [showCustomPlatform, setShowCustomPlatform] = useState(false);
  const [editingPlatform, setEditingPlatform] = useState('');
  const [editingCustomPlatform, setEditingCustomPlatform] = useState('');
  const [showEditingCustomPlatform, setShowEditingCustomPlatform] = useState(false);
  const [platformPickerVisible, setPlatformPickerVisible] = useState(false);
  const [editingPlatformPickerVisible, setEditingPlatformPickerVisible] = useState(false);

  const platformOptions = [
    { value: '', label: '选择平台' },
    { value: 'jd', label: '京东' },
    { value: 'taobao', label: '淘宝' },
    { value: 'tmall', label: '天猫' },
    { value: 'pdd', label: '拼多多' },
    { value: 'suning', label: '苏宁易购' },
    { value: 'vip', label: '唯品会' },
    { value: 'amazon', label: '亚马逊' },
    { value: 'offline', label: '线下实体店' },
    { value: 'custom', label: '其他(自定义)' },
  ];

  const getPlatformLabel = (value) => {
    // 确保返回值始终是字符串
    if (!value || typeof value !== 'string') {
      return '未知';
    }

    const option = platformOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  useEffect(() => {
    if (isEditing) {
      loadProductData();
    }
  }, [productId, isEditing]);

  useEffect(() => {
    if (!BackHandler.removeEventListener) {
      BackHandler.removeEventListener = () => {};
    }

    return () => {
      // 组件卸载时清理
    };
  }, []);

  const loadProductData = useCallback(async () => {
    try {
      setLoading(true);
      const productData = await databaseService.getProductById(productId);
      if (productData) {
        setProduct(productData);

        const priceData = await databaseService.getProductPrices(productId);
        setOriginalPrices(priceData);
        setPrices(priceData);
      }
    } catch (error) {
      console.error('加载商品数据失败:', error);
      showToast('加载商品数据失败', 'error');
    } finally {
      setLoading(false);
    }
  }, [productId, showToast]);

  const openImagePicker = () => {
    setPriceModalVisible(false);
    setConfirmDialogVisible(true);
    setConfirmDialogMessage('选择图片来源');
    setConfirmDialogAction(() => () => {});
  };

  // 删除图片相关函数

  const addPrice = () => {
    if (!newPrice.trim()) {
      showToast('请输入价格', 'error');
      return;
    }

    const parsedPrice = parseFloat(newPrice);
    if (isNaN(parsedPrice) || parsedPrice <= 0) {
      showToast('请输入有效的价格', 'error');
      return;
    }

    let platform = newPlatform;
    if (platform === 'custom') {
      if (!customPlatform.trim()) {
        showToast('请输入自定义平台名称', 'error');
        return;
      }
      platform = customPlatform.trim();
    } else if (!platform) {
      showToast('请选择购买平台', 'error');
      return;
    } else {
      platform = getPlatformLabel(platform);
    }

    if (!product.name.trim()) {
      showToast('请先输入商品名称', 'error');
      return;
    }

    const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const tempId = -Date.now();
    const newPriceItem = {
      id: tempId,
      productId: product.id || tempId,
      price: parsedPrice,
      platform: platform,
      createdAt: now,
      updatedAt: now,
    };

    setPrices(prev => [newPriceItem, ...prev]);
    setPricesToAdd(prev => [...prev, newPriceItem]);

    setNewPrice('');
    setNewPlatform('');
    setCustomPlatform('');
    setShowCustomPlatform(false);
    setHasUnsavedChanges(true);
    showToast('价格已添加', 'success');
  };

  const deletePrice = priceId => {
    setPrices(prev => prev.filter(price => price.id !== priceId));

    if (priceId > 0) {
      setPricesToDelete(prev => [...prev, priceId]);

      setPricesToUpdate(prev => prev.filter(price => price.id !== priceId));
    } else {
      setPricesToAdd(prev => prev.filter(price => price.id !== priceId));
    }

    setHasUnsavedChanges(true);
    showToast('价格已删除', 'success');
  };

  const openEditPriceModal = price => {
    setEditingPrice(price);
    setEditingPriceValue(price.price.toString());

    const platformOption = platformOptions.find(opt => opt.label === price.platform);
    if (platformOption) {
      setEditingPlatform(platformOption.value);
      setShowEditingCustomPlatform(false);
    } else if (price.platform) {
      setEditingPlatform('custom');
      setEditingCustomPlatform(price.platform);
      setShowEditingCustomPlatform(true);
    } else {
      setEditingPlatform('');
      setEditingCustomPlatform('');
      setShowEditingCustomPlatform(false);
    }

    setPriceModalVisible(true);
  };

  const saveEditedPrice = () => {
    if (!editingPriceValue.trim()) {
      showToast('请输入价格', 'error');
      return;
    }

    const parsedPrice = parseFloat(editingPriceValue);
    if (isNaN(parsedPrice) || parsedPrice <= 0) {
      showToast('请输入有效的价格', 'error');
      return;
    }

    let platform = editingPlatform;
    if (platform === 'custom') {
      if (!editingCustomPlatform.trim()) {
        showToast('请输入自定义平台名称', 'error');
        return;
      }
      platform = editingCustomPlatform.trim();
    } else if (!platform) {
      showToast('请选择购买平台', 'error');
      return;
    } else {
      platform = getPlatformLabel(platform);
    }

    const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const updatedPrice = {
      ...editingPrice,
      price: parsedPrice,
      platform: platform,
      updatedAt: now,
    };

    setPrices(prev =>
      prev.map(price => (price.id === editingPrice.id ? updatedPrice : price)),
    );

    if (editingPrice.id > 0) {
      setPricesToUpdate(prev => {
        const existingIndex = prev.findIndex(p => p.id === editingPrice.id);
        if (existingIndex >= 0) {
          const newList = [...prev];
          newList[existingIndex] = updatedPrice;
          return newList;
        } else {
          return [...prev, updatedPrice];
        }
      });
    } else {
      setPricesToAdd(prev =>
        prev.map(price =>
          price.id === editingPrice.id ? updatedPrice : price,
        ),
      );
    }

    setPriceModalVisible(false);
    setHasUnsavedChanges(true);
    showToast('价格已修改', 'success');
  };

  const saveProduct = async () => {
    if (!product.name.trim()) {
      showToast('请输入商品名称', 'error');
      return;
    }

    if (prices.length === 0) {
      showToast('请至少添加一个价格', 'error');
      return;
    }

    try {
      setLoading(true);

      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const productToSave = {
        ...product,
        updatedAt: now,
      };

      if (!product.id) {
        productToSave.createdAt = now;
      }
      console.log('productToSave', productToSave);
      const savedProductId = await databaseService.saveProduct(productToSave);

      if (!product.id) {
        setProduct(prev => ({
          ...prev,
          id: savedProductId,
        }));
      }

      for (const priceId of pricesToDelete) {
        await databaseService.deleteProductPrice(priceId);
      }

      for (const priceItem of pricesToAdd) {
        const priceToAdd = {
          ...priceItem,
          productId: savedProductId || product.id,
        };
        await databaseService.addProductPrice(priceToAdd);
      }

      for (const priceItem of pricesToUpdate) {
        await databaseService.updateProductPrice(priceItem);
      }

      setPricesToAdd([]);
      setPricesToUpdate([]);
      setPricesToDelete([]);
      setHasUnsavedChanges(false);

      showToast('商品保存成功', 'success');

      navigation.goBack();
    } catch (error) {
      console.error('保存商品失败:', error);
      showToast('保存商品失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showDeletePriceConfirm = priceId => {
    setConfirmDialogMessage('确定要删除这个价格记录吗？');
    setConfirmDialogAction(() => () => deletePrice(priceId));
    setConfirmDialogVisible(true);
  };

  const showDiscardChangesConfirm = useCallback(() => {
    if (hasUnsavedChanges) {
      setConfirmDialogMessage('您有未保存的更改，确定要放弃吗？');
      setConfirmDialogAction(() => () => navigation.goBack());
      setConfirmDialogVisible(true);
    } else {
      navigation.goBack();
    }
  }, [hasUnsavedChanges, navigation]);

  const renderPriceItem = price => {
    const createdTime = dayjs(price.createdAt).format('YYYY-MM-DD HH:mm:ss');
    const updatedTime = dayjs(price.updatedAt).format('YYYY-MM-DD HH:mm:ss');
    const isUpdated = price.createdAt !== price.updatedAt;
    const isNewPrice = price.id < 0;
    const isPending = isNewPrice || pricesToUpdate.some(p => p.id === price.id);

    return (
      <View key={price.id} style={styles.priceItem}>
        <View style={styles.priceInfo}>
          <Text style={styles.priceValue}>¥{price.price.toFixed(2)}</Text>
          {price.platform && (
            <View style={styles.platformTag}>
              <Icon name="cart-shopping" size={12} color="#666666" style={styles.platformIcon} />
              <Text style={styles.platformText}>{price.platform}</Text>
            </View>
          )}
          <Text style={styles.priceTime}>添加于: {createdTime}</Text>
          {isUpdated && (
            <Text style={styles.priceTime}>修改于: {updatedTime}</Text>
          )}
          {isPending && (
            <Text style={styles.pendingTag}>
              {isNewPrice ? '新增' : '已修改'}
            </Text>
          )}
        </View>

        <View style={styles.priceActions}>
          {!isUpdated && (
            <TouchableOpacity
              style={[styles.priceAction, styles.editAction]}
              onPress={() => openEditPriceModal(price)}>
              <Icon name="edit" size={16} color={colors.primary} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.priceAction, styles.deleteAction]}
            onPress={() => showDeletePriceConfirm(price.id)}>
            <Icon name="trash" size={16} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <PageContainer headerTitle={isEditing ? '编辑商品' : '添加商品'} backgroundColor={COLORS.secondary}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      headerTitle={isEditing ? '编辑商品' : '添加商品'}
      onLeftPress={hasUnsavedChanges ? showDiscardChangesConfirm : undefined}
      backgroundColor="#F5F7FA">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 移除图片卡片，直接显示表单 */}

        <View style={styles.formCard}>
          <Text style={styles.cardTitle}>基本信息</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>商品名称 <Text style={styles.requiredMark}>*</Text></Text>
            <TextInput
              style={styles.input}
              value={product.name}
              onChangeText={(text) => {
                setProduct(prev => ({ ...prev, name: text }));
                setHasUnsavedChanges(true);
              }}
              placeholder="输入商品名称"
              maxLength={100}
              placeholderTextColor="#BBBBBB"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>备注信息</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={product.notes}
              onChangeText={(text) => {
                setProduct(prev => ({ ...prev, notes: text }));
                setHasUnsavedChanges(true);
              }}
              placeholder="输入备注信息（可选）"
              multiline={true}
              textAlignVertical="top"
              maxLength={500}
              placeholderTextColor="#BBBBBB"
            />
          </View>
        </View>

        <View style={styles.formCard}>
          <Text style={styles.cardTitle}>价格信息 <Text style={styles.requiredMark}>*</Text></Text>

          {/* 优化后的价格添加区域 */}
          <View style={styles.quickPriceAdd}>
            <View style={styles.priceInputRow}>
              <View style={styles.priceInputWrapper}>
                <Text style={styles.inputLabel}>价格</Text>
                <TextInput
                  style={styles.compactPriceInput}
                  value={newPrice}
                  onChangeText={setNewPrice}
                  placeholder="0.00"
                  keyboardType="numeric"
                  placeholderTextColor="#BBBBBB"
                />
              </View>

              <View style={styles.platformInputWrapper}>
                <Text style={styles.inputLabel}>平台</Text>
                <TouchableOpacity
                  style={[styles.compactPlatformButton, {borderColor: colors.primary}]}
                  onPress={() => setPlatformPickerVisible(true)}>
                  <Text style={[styles.compactPlatformText,
                    newPlatform ? {color: '#333333'} : {color: '#BBBBBB'}]}>
                    {newPlatform ? getPlatformLabel(newPlatform) : '选择平台'}
                  </Text>
                  <Icon name="chevron-down" size={12} color="#999999" />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={[styles.compactAddButton, {backgroundColor: colors.primary}]}
                onPress={addPrice}>
                <Icon name="plus" size={16} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            {showCustomPlatform && (
              <View style={styles.customPlatformRow}>
                <TextInput
                  style={styles.customPlatformInput}
                  value={customPlatform}
                  onChangeText={setCustomPlatform}
                  placeholder="请输入平台名称"
                  placeholderTextColor="#BBBBBB"
                />
              </View>
            )}
          </View>

          {/* 价格列表 */}
          {prices.length > 0 ? (
            <View style={styles.priceList}>
              <Text style={styles.priceListTitle}>价格记录 ({prices.length})</Text>
              {prices.map(renderPriceItem)}
            </View>
          ) : (
            <View style={styles.noPricesContainer}>
              <Icon name="tag" size={24} color="#DDDDDD" />
              <Text style={styles.noPricesText}>请添加至少一个价格</Text>
              <Text style={styles.noPricesHint}>记录不同平台的价格，方便比价</Text>
            </View>
          )}
        </View>

        {product.id && (
          <View style={styles.formCard}>
            <Text style={styles.cardTitle}>商品状态</Text>

            <View style={styles.statusCard}>
              <Icon
                name={product.purchased ? 'check-circle' : 'circle'}
                size={28}
                color={product.purchased ? '#4CD964' : '#007AFF'}
                style={styles.statusIcon}
              />
              <View style={styles.statusTextContainer}>
                <Text style={styles.statusText}>
                  {product.purchased ? '该商品已购买' : '该商品未购买'}
                </Text>
                <Text style={styles.statusDescription}>
                  {product.purchased
                    ? '商品已添加到账单记录中'
                    : '点击详情页价格旁的购买按钮可将商品添加到账单'}
                </Text>
              </View>
            </View>
          </View>
        )}

        {hasUnsavedChanges && (
          <View style={styles.unsavedChangesContainer}>
            <Icon name="lightbulb" size={16} color={COLORS.warning} />
            <Text style={styles.unsavedChangesText}>
              您有未保存的更改
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.saveButton,
            {backgroundColor: colors.primary},
            hasUnsavedChanges && styles.saveButtonHighlight,
          ]}
          onPress={saveProduct}>
          <Icon name="save" size={18} color="#FFFFFF" style={styles.saveButtonIcon} />
          <Text style={styles.saveButtonText}>保存商品</Text>
        </TouchableOpacity>
      </ScrollView>

      <CustomModal
        visible={priceModalVisible}
        onClose={() => setPriceModalVisible(false)}
        position="center"
        animationType="fade">
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>修改价格</Text>

          <View style={styles.modalInputSection}>
            <Text style={styles.modalInputLabel}>价格</Text>
            <TextInput
              style={styles.modalInput}
              value={editingPriceValue}
              onChangeText={setEditingPriceValue}
              keyboardType="numeric"
              autoFocus={true}
            />
          </View>

          <View style={styles.modalInputSection}>
            <Text style={styles.modalInputLabel}>购买平台</Text>
            <TouchableOpacity
              style={styles.modalPlatformSelector}
              onPress={() => setEditingPlatformPickerVisible(true)}>
              <Text style={[styles.modalPlatformText,
                editingPlatform ? {color: '#333333'} : {color: '#BBBBBB'}]}>
                {editingPlatform ? getPlatformLabel(editingPlatform) : '选择购买平台'}
              </Text>
              <Icon name="chevron-down" size={14} color="#999999" />
            </TouchableOpacity>

            {showEditingCustomPlatform && (
              <TextInput
                style={styles.customPlatformInput}
                value={editingCustomPlatform}
                onChangeText={setEditingCustomPlatform}
                placeholder="请输入平台名称"
                placeholderTextColor="#BBBBBB"
              />
            )}
          </View>

          <Text style={styles.modalNote}>注意: 每个价格只能修改一次</Text>

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton]}
              onPress={() => setPriceModalVisible(false)}>
              <Text style={styles.modalCancelButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modalButton, styles.modalSaveButton, {backgroundColor: colors.primary}]}
              onPress={saveEditedPrice}>
              <Text style={styles.modalSaveButtonText}>保存</Text>
            </TouchableOpacity>
          </View>
        </View>
      </CustomModal>

      {/* 删除图片选择对话框 */}
      {confirmDialogVisible && (
        <ConfirmDialog
          visible={confirmDialogVisible}
          title="确认"
          message={confirmDialogMessage}
          onCancel={() => setConfirmDialogVisible(false)}
          onConfirm={() => {
            setConfirmDialogVisible(false);
            confirmDialogAction();
          }}
          cancelText="取消"
          confirmText="确定"
        />
      )}

      <Picker
        data={platformOptions}
        cols={1}
        onChange={(value) => {
          if (value && value[0]) {
            setNewPlatform(value[0] as string);
            setShowCustomPlatform(value[0] === 'custom');
          }
        }}
        onVisibleChange={(v) => setPlatformPickerVisible(v)}
        visible={platformPickerVisible}
        value={[newPlatform]}
      />

      <Picker
        data={platformOptions}
        cols={1}
        onChange={(value) => {
          if (value && value[0]) {
            setEditingPlatform(value[0] as string);
            setShowEditingCustomPlatform(value[0] === 'custom');
          }
        }}
        onVisibleChange={(v) => setEditingPlatformPickerVisible(v)}
        visible={editingPlatformPickerVisible}
        value={[editingPlatform]}
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#999999',
  },
  imageCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 16,
  },
  // 删除图片相关样式
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333333',
  },
  requiredMark: {
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  input: {
    backgroundColor: '#F8F8F8',
    borderWidth: 0,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priceInputContainer: {
    marginBottom: 20,
  },
  priceInput: {
    backgroundColor: '#F8F8F8',
    borderWidth: 0,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    marginBottom: 12,
  },
  // 新增的紧凑价格添加样式
  quickPriceAdd: {
    marginBottom: 20,
  },
  priceInputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  priceInputWrapper: {
    flex: 1,
  },
  platformInputWrapper: {
    flex: 1.2,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 6,
  },
  compactPriceInput: {
    backgroundColor: '#F8F8F8',
    borderWidth: 0,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333333',
    textAlign: 'right',
  },
  compactPlatformButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  compactPlatformText: {
    fontSize: 14,
    flex: 1,
  },
  compactAddButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customPlatformRow: {
    marginTop: 12,
  },
  platformSelectorButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  platformSelectorText: {
    fontSize: 16,
  },
  customPlatformInput: {
    backgroundColor: '#F8F8F8',
    borderWidth: 0,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    marginBottom: 12,
  },
  addPriceButton: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  priceList: {
    marginTop: 10,
  },
  priceListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333333',
  },
  priceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    padding: 16,
    borderRadius: 12,
    marginBottom: 10,
  },
  priceInfo: {
    flex: 1,
  },
  priceValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  priceTime: {
    fontSize: 12,
    color: '#999999',
  },
  priceActions: {
    flexDirection: 'row',
  },
  priceAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  editAction: {
    backgroundColor: '#E8F9E8',
  },
  deleteAction: {
    backgroundColor: '#FFE8E8',
  },
  noPricesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
  },
  noPricesText: {
    marginTop: 10,
    color: '#999999',
    fontSize: 16,
    fontWeight: '600',
  },
  noPricesHint: {
    marginTop: 4,
    color: '#BBBBBB',
    fontSize: 14,
    textAlign: 'center',
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
  },
  statusIcon: {
    marginRight: 12,
  },
  statusTextContainer: {
    flex: 1,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: '#666666',
  },
  unsavedChangesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF8E1',
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
  },
  unsavedChangesText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  saveButton: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  saveButtonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonHighlight: {
    backgroundColor: '#4CD964',
  },
  pendingTag: {
    color: '#FFC107',
    fontWeight: 'bold',
    fontSize: 12,
    marginTop: 4,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    padding: 24,
    borderRadius: 16,
    width: '85%',
    alignSelf: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333333',
  },
  modalInputSection: {
    marginBottom: 16,
  },
  modalInputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  modalInput: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  modalList: {
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalNote: {
    fontSize: 14,
    color: '#FF3B30',
    marginBottom: 24,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#F2F2F2',
    marginRight: 8,
  },
  modalCancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  modalSaveButton: {
    marginLeft: 8,
  },
  modalSaveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  imagePickerModal: {
    width: '100%',
    padding: 20,
  },
  imagePickerOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  imagePickerOption: {
    alignItems: 'center',
    padding: 10,
  },
  imagePickerIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  imagePickerOptionText: {
    fontSize: 14,
    color: '#333333',
    marginTop: 4,
  },
  cancelButton: {
    backgroundColor: '#F2F2F2',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginTop: 10,
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  platformTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginVertical: 6,
  },
  platformIcon: {
    marginRight: 4,
  },
  platformText: {
    fontSize: 12,
    color: '#666666',
  },
  modalPlatformSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  modalPlatformText: {
    fontSize: 16,
  },
});

export default ProductEditScreen;
