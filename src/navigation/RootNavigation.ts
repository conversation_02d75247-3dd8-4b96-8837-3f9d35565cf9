// 创建一个导航引用，可以在组件外部使用
import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

// 导航到指定的路由
export function navigate(name: string, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name as never, params as never);
  } else {
    // 如果导航容器还没准备好，可以将导航操作放入队列
    // 这里简单处理，只是记录一个错误
    console.warn('Navigation attempted before navigator was ready');
  }
}

// 返回上一个页面
export function goBack() {
  if (navigationRef.isReady()) {
    navigationRef.goBack();
  }
}

// 重置导航状态
export function reset(state: any) {
  if (navigationRef.isReady()) {
    navigationRef.reset(state);
  }
}
