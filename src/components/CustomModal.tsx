import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
} from 'react-native';
import { COLORS } from '../utils/color';
const { height, width } = Dimensions.get('window');

interface CustomModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  animationType?: 'slide' | 'fade';
  position?: 'center' | 'bottom';
  avoidKeyboard?: boolean;
  closeOnBackdropPress?: boolean;
  backgroundColor?: string;
  customStyle?: {};
}

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  onClose,
  children,
  animationType = 'fade',
  position = 'center',
  avoidKeyboard = false,
  closeOnBackdropPress = true,
  customStyle = {},
}) => {
  const backdropAnimation = useRef(new Animated.Value(0)).current;
  const modalAnimation = useRef(new Animated.Value(0)).current;
  const [modalVisible, setModalVisible] = useState(visible);

  useEffect(() => {
    if (visible) {
      setModalVisible(true);

      // 显示模态框
      Animated.parallel([
        Animated.timing(backdropAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(modalAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // 处理安卓返回按钮
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          handleClose();
          return true;
        },
      );

      return () => backHandler.remove();
    } else {
      handleClose();
    }
  }, [visible]);

  // 处理关闭动画
  const handleClose = () => {
    if (!modalVisible) {return;}

    if (position === 'bottom' && animationType === 'slide') {
      // 先执行模态框滑出动画
      Animated.timing(modalAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // 模态框滑出后再淡出背景
        Animated.timing(backdropAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start(() => {
          setModalVisible(false);
        });
      });
    } else {
        setModalVisible(false);

      // 对于其他类型的模态框，同时执行淡出动画
      // Animated.parallel([
      //   Animated.timing(backdropAnimation, {
      //     toValue: 0,
      //     duration: 300,
      //     useNativeDriver: true,
      //   }),
      //   Animated.timing(modalAnimation, {
      //     toValue: 0,
      //     duration: 300,
      //     useNativeDriver: true,
      //   }),
      // ]).start(() => {
      // });
    }
  };

  // 用户点击关闭按钮
  const onClosePress = () => {
    handleClose();
    // 延迟调用外部的 onClose，确保动画完成
    setTimeout(() => {
      onClose();
    }, 300);
  };

  // 如果不可见，不渲染任何内容
  if (!modalVisible) {return null;}

  // 背景透明度动画
  const backdropStyle = {
    opacity: backdropAnimation,
  };

  // 模态框动画
  const getModalAnimationStyle = () => {
    if (animationType === 'slide') {
      if (position === 'bottom') {
        // 底部弹出的模态框，从底部滑入/滑出
        return {
          transform: [
            {
              translateY: modalAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [height, 0], // 从屏幕底部滑入
              }),
            },
          ],
        };
      } else {
        // 中间位置的模态框，从下方滑入
        return {
          transform: [
            {
              translateY: modalAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [300, 0],
              }),
            },
          ],
          opacity: modalAnimation,
        };
      }
    } else {
      // 淡入淡出动画
      return {
        opacity: modalAnimation,
        transform: [
          {
            scale: modalAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1],
            }),
          },
        ],
      };
    }
  };

  // 模态框位置样式
  const getPositionStyle = () => {
    if (position === 'bottom') {
      return styles.bottomPosition;
    }
    return styles.centerPosition;
  };

  // 渲染内容
  const renderContent = () => {
    const content = (
      <Animated.View
        style={[
          styles.modalContainer,
          customStyle,
          getPositionStyle(),
          getModalAnimationStyle(),
        ]}>
        {children}
      </Animated.View>
    );

    if (avoidKeyboard) {
      return (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          {content}
        </KeyboardAvoidingView>
      );
    }

    return content;
  };

  return (
    <View style={styles.container} pointerEvents="box-none">
      <TouchableWithoutFeedback
        onPress={closeOnBackdropPress ? onClosePress : undefined}>
        <Animated.View style={[styles.backdrop, backdropStyle]} />
      </TouchableWithoutFeedback>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  modalContainer: {
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxHeight: height * 0.8,
  },
  centerPosition: {
    width: width * 0.85,
    maxWidth: 400,
    alignSelf: 'center',
  },
  bottomPosition: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: width,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  keyboardAvoidingView: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
});

export default CustomModal;
