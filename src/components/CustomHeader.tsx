import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { COLORS } from '../utils/color';
import { useTheme } from '../context/ThemeContext';

// 获取状态栏高度
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

interface CustomHeaderProps {
  title?: string;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  backgroundColor?: string;
  titleColor?: string;
  showBackButton?: boolean;
  iconColor?: string;
}

const CustomHeader: React.FC<CustomHeaderProps> = ({
  title = '',
  leftComponent,
  rightComponent,
  onLeftPress,
  onRightPress,
  backgroundColor = '#FFFFFF',
  titleColor = '#000000',
  showBackButton = true,
  iconColor = '#007AFF',
}) => {
  const navigation = useNavigation();
  const {colors} = useTheme();
  // 默认的返回按钮处理函数
  const handleBackPress = () => {
    if (onLeftPress) {
      onLeftPress();
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  // 默认的左侧组件（返回按钮）
  const defaultLeftComponent = showBackButton ? (
    <TouchableOpacity style={styles.leftButton} onPress={handleBackPress}>
      <Icon name="angle-left" size={24} color={colors.primary} />
    </TouchableOpacity>
  ) : null;

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
      <View style={[styles.container, { backgroundColor }]}>
        <View style={styles.leftContainer}>
          {leftComponent || defaultLeftComponent}
        </View>

        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: titleColor }]} numberOfLines={1}>
            {title}
          </Text>
        </View>

        <View style={styles.rightContainer}>
          {rightComponent || (
            onRightPress ? (
              <TouchableOpacity style={styles.rightButton} onPress={onRightPress}>
                <View style={styles.rightButtonPlaceholder} />
              </TouchableOpacity>
            ) : <View style={styles.rightButtonPlaceholder} />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // safeArea: {
  //   paddingTop: Platform.OS === 'android' ? STATUSBAR_HEIGHT : 0,
  // },
  container: {
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderBottomWidth: 1,
    // borderBottomColor: '#EEEEEE',
  },
  leftContainer: {
    flex: 1,
    alignItems: 'flex-start',
    paddingLeft: 16,
  },
  titleContainer: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightContainer: {
    flex: 1,
    alignItems: 'flex-end',
    paddingRight: 16,
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
  },
  leftButton: {
    paddingVertical: 8,
    paddingRight: 8,
  },
  rightButton: {
    paddingVertical: 8,
    paddingLeft: 8,
  },
  rightButtonPlaceholder: {
    width: 40,
  },
});

export default CustomHeader;
