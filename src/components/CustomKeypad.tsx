import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import dayjs from 'dayjs';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import Icon from 'react-native-vector-icons/FontAwesome6';

const {width, height} = Dimensions.get('window');
const KEYPAD_HEIGHT = height * 0.45; // 降低键盘高度为屏幕高度的35%
const BUTTON_SIZE = (width * 0.75 - 50) / 3; // 左侧宽度的三分之一减去间距
const BUTTON_HEIGHT = BUTTON_SIZE * 0.7; // 按钮高度为宽度的70%

interface CustomKeypadProps {
  onNumberPress: (num: string) => void;
  onDeletePress: () => void;
  onDotPress: () => void;
  onDonePress: () => void;
  onDatePress: () => void;
  onNotePress: () => void;
  onPlusPress: () => void;
  onMinusPress: () => void;
  onWalletPress: () => void;
  amount: string;
  note: string;
  date: string;
  // 新增属性
  isReimbursable: boolean;
  onReimbursableToggle: () => void;
}

const CustomKeypad: React.FC<CustomKeypadProps> = ({
  onNumberPress,
  onDeletePress,
  onDotPress,
  onDonePress,
  onDatePress,
  onNotePress,
  onPlusPress,
  onMinusPress,
  onWalletPress,
  amount,
  note,
  date,
  // 新增属性
  isReimbursable,
  onReimbursableToggle,
}) => {
  const {colors} = useTheme();
  // 格式化日期为年月格式
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('MM-DD');
  };

  const renderNumberButton = (value: string, onPress: () => void) => (
    <TouchableOpacity
      style={styles.numberButton}
      onPress={onPress}
      activeOpacity={0.7}>
      <Text style={styles.numberButtonText}>{value}</Text>
    </TouchableOpacity>
  );

  const renderActionButton = (
    label: string,
    onPress: () => void,
    style?: object,
    textStyle?: object,
  ) => (
    <TouchableOpacity
      style={[styles.actionButton, style]}
      onPress={onPress}
      activeOpacity={0.7}>
      <Text style={[styles.actionButtonText, textStyle]}>{label}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* 显示区域 */}
      <View style={styles.displayContainer}>
        <View style={styles.leftSection}>
          <TouchableOpacity style={styles.noteContainer} onPress={onNotePress} activeOpacity={0.7}>
            <View style={styles.noteInputWrapper}>
              <Icon
                name="pen"
                size={14}
                color={note ? colors.primary : COLORS.text.gray}
                style={styles.noteIcon}
              />
              <Text style={[
                styles.noteText,
                note ? styles.noteTextActive : styles.noteTextPlaceholder,
              ]} numberOfLines={1}>
                {note || '点击输入备注'}
              </Text>
            </View>
            {note && (
              <View style={styles.noteIndicator}>
                <View style={[styles.noteIndicatorDot, {backgroundColor: colors.primary}]} />
              </View>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.rightSection}>
          <TouchableOpacity style={styles.walletContainer} onPress={onWalletPress}>
            <Icon name="wallet" size={20} color={colors.primary} />
          </TouchableOpacity>
          <Text style={styles.amountText}>{amount || '0'}</Text>
        </View>
      </View>

      {/* 待报销按钮 - 独立一行 */}
      <View style={styles.reimbursableContainer}>
        <TouchableOpacity
          style={[
            styles.reimbursableButton,
            isReimbursable && {backgroundColor: colors.primary},
          ]}
          onPress={onReimbursableToggle}
        >
          <Text style={[
            styles.reimbursableText,
            isReimbursable && styles.reimbursableTextActive,
          ]}>报销账单</Text>
        </TouchableOpacity>
      </View>

      {/* 键盘区域 */}
      <View style={styles.keypadContainer}>
        {/* 左侧数字键盘 */}
        <View style={styles.numbersContainer}>
          <View style={styles.row}>
            {renderNumberButton('7', () => onNumberPress('7'))}
            {renderNumberButton('8', () => onNumberPress('8'))}
            {renderNumberButton('9', () => onNumberPress('9'))}
          </View>
          <View style={styles.row}>
            {renderNumberButton('4', () => onNumberPress('4'))}
            {renderNumberButton('5', () => onNumberPress('5'))}
            {renderNumberButton('6', () => onNumberPress('6'))}
          </View>
          <View style={styles.row}>
            {renderNumberButton('1', () => onNumberPress('1'))}
            {renderNumberButton('2', () => onNumberPress('2'))}
            {renderNumberButton('3', () => onNumberPress('3'))}
          </View>
          <View style={styles.row}>
            {renderNumberButton('.', onDotPress)}
            {renderNumberButton('0', () => onNumberPress('0'))}
            {renderNumberButton('⌫', onDeletePress)}
          </View>
        </View>

        {/* 右侧功能按钮 */}
        <View style={styles.actionsContainer}>
          {/* 日期选择按钮 - 只显示年月 */}
          {renderActionButton(
            formatDate(date),
            onDatePress,
            styles.dateButton,
            styles.dateButtonText,
          )}

          {/* 加减按钮 - 垂直排列 */}
          {renderActionButton(
            '+',
            onPlusPress,
            styles.calcButton,
            styles.calcButtonText,
          )}
          {renderActionButton(
            '-',
            onMinusPress,
            styles.calcButton,
            styles.calcButtonText,
          )}

          {/* 完成按钮 */}
          {renderActionButton(
            '完成',
            onDonePress,
            {...styles.doneButton, backgroundColor: colors.primary},
            styles.doneButtonText,
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: KEYPAD_HEIGHT,
    backgroundColor: COLORS.secondary,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  displayContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.background.white,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  // 新增待报销容器样式
  reimbursableContainer: {
    backgroundColor: COLORS.background.white,
    padding: 10,
    // paddingTop: 0,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    alignItems: 'flex-start',
  },
  leftSection: {
    flex: 1,
    flexDirection: 'column',
    marginRight: 10,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteContainer: {
    marginBottom: 0, // 移除底部间距，让备注紧凑显示
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: 'transparent',
    position: 'relative',
  },
  noteInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteIcon: {
    marginRight: 8,
  },
  walletContainer: {
    width: 34,
    height: 34,
    marginRight: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    backgroundColor: COLORS.background.light,
  },
  noteText: {
    fontSize: 16,
    color: COLORS.text.gray,
    flex: 1,
  },
  noteTextActive: {
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  noteTextPlaceholder: {
    color: COLORS.text.gray,
    // fontStyle: 'italic',
  },
  noteIndicator: {
    position: 'absolute',
    top: 6,
    right: 6,
  },
  noteIndicatorDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  amountText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text.primary,
  },
  keypadContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  numbersContainer: {
    width: '75%',
    padding: 10,
  },
  actionsContainer: {
    width: '25%',
    padding: 10,
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  numberButton: {
    width: BUTTON_SIZE,
    height: BUTTON_HEIGHT,
    backgroundColor: COLORS.background.white,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  numberButtonText: {
    fontSize: 20,
    color: COLORS.text.primary,
  },
  actionButton: {
    width: BUTTON_SIZE,
    height: BUTTON_HEIGHT,
    backgroundColor: COLORS.background.white,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  calcButton: {
    backgroundColor: '#F0F0F0',
  },
  calcButtonText: {
    fontSize: 20,
    color: COLORS.text.primary,
    fontWeight: 'bold',
  },
  dateButton: {
    backgroundColor: COLORS.background.light,
  },
  dateButtonText: {
    color: COLORS.primary,
  },
  doneButton: {
    marginTop: 'auto', // 将完成按钮推到底部
  },
  doneButtonText: {
    color: COLORS.background.white,
    fontWeight: 'bold',
  },
  // 修改后的待报销按钮样式
  reimbursableButton: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 16,
    backgroundColor: COLORS.background.light,
    alignSelf: 'flex-start',
  },
  reimbursableText: {
    fontSize: 14,
    color: COLORS.text.gray,
    fontWeight: '500',
  },
  reimbursableTextActive: {
    color: '#FFFFFF',
  },
});

export default CustomKeypad;
