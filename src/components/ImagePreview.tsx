import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Modal,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Animated,
  PanResponder,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ImagePreviewProps {
  visible: boolean;
  imageUri: string | null;
  onClose: () => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ visible, imageUri, onClose }) => {
  const [scale] = useState(new Animated.Value(1));
  const [translateX] = useState(new Animated.Value(0));
  const [translateY] = useState(new Animated.Value(0));

  // 使用 useRef 替代 useState 来跟踪最后的值
  const lastScale = useRef(1);
  const lastTranslateX = useRef(0);
  const lastTranslateY = useRef(0);

  // 用于双击检测
  const lastTap = useRef(0);

  // 处理捏合缩放和拖动手势
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,

    // 双指捏合缩放
    onPanResponderMove: (evt, gestureState) => {
      // 检测双指手势
      if (evt.nativeEvent.changedTouches.length === 2) {
        const touch1 = evt.nativeEvent.changedTouches[0];
        const touch2 = evt.nativeEvent.changedTouches[1];

        // 计算双指之间的距离
        const dist = Math.sqrt(
          Math.pow(touch2.pageX - touch1.pageX, 2) +
          Math.pow(touch2.pageY - touch1.pageY, 2)
        );

        // 计算缩放因子，按触摸点距离调整
        const newScale = Math.max(1, Math.min(3, lastScale.current * (dist / 150)));
        scale.setValue(newScale);
      } else {
        // 单指拖动
        translateX.setValue(lastTranslateX.current + gestureState.dx);
        translateY.setValue(lastTranslateY.current + gestureState.dy);
      }
    },

    // 手势结束时记录最终状态
    onPanResponderRelease: (_evt, _gestureState) => {
      // 使用 getValue() 方法获取当前值
      const currentScale = scale.__getValue ? scale.__getValue() : 1;
      const currentTranslateX = translateX.__getValue ? translateX.__getValue() : 0;
      const currentTranslateY = translateY.__getValue ? translateY.__getValue() : 0;

      lastScale.current = currentScale;
      lastTranslateX.current = currentTranslateX;
      lastTranslateY.current = currentTranslateY;

      // 如果缩放比例小于1.1，重置为初始状态
      if (lastScale.current < 1.1) {
        Animated.parallel([
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
            friction: 5,
          }),
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
            friction: 5,
          }),
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            friction: 5,
          }),
        ]).start();
        lastScale.current = 1;
        lastTranslateX.current = 0;
        lastTranslateY.current = 0;
      }
    },

    onPanResponderTerminate: () => {
      // 重置状态
      const currentScale = scale.__getValue ? scale.__getValue() : 1;
      const currentTranslateX = translateX.__getValue ? translateX.__getValue() : 0;
      const currentTranslateY = translateY.__getValue ? translateY.__getValue() : 0;

      lastScale.current = currentScale;
      lastTranslateX.current = currentTranslateX;
      lastTranslateY.current = currentTranslateY;
    },
  });

  // 重置缩放和位移
  const resetImagePosition = () => {
    Animated.parallel([
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        friction: 5,
      }),
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
        friction: 5,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        friction: 5,
      }),
    ]).start();
    lastScale.current = 1;
    lastTranslateX.current = 0;
    lastTranslateY.current = 0;
  };

  // 处理双击
  const handleImagePress = () => {
    const now = Date.now();
    const DOUBLE_PRESS_DELAY = 300;

    if (now - lastTap.current < DOUBLE_PRESS_DELAY) {
      // 双击操作
      const currentScale = scale.__getValue ? scale.__getValue() : 1;

      if (currentScale > 1.5) {
        // 如果已经放大，则重置
        resetImagePosition();
      } else {
        // 放大到2倍
        Animated.spring(scale, {
          toValue: 2,
          useNativeDriver: true,
          friction: 5,
        }).start();
        lastScale.current = 2;
      }
    } else {
      // 单击操作 - 可以不做任何事，或者触发其他行为
    }

    lastTap.current = now;
  };

  // 处理关闭操作
  const handleClose = () => {
    resetImagePosition();
    onClose();
  };

  // 当组件卸载时重置状态
  useEffect(() => {
    return () => {
      resetImagePosition();
    };
  }, []);

  if (!visible) {return null;}

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}>
      <StatusBar backgroundColor="#000000" barStyle="light-content" />
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
          activeOpacity={0.7}>
          <Icon name="xmark" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.imageContainer}>
          <TouchableOpacity
            style={styles.touchableImage}
            activeOpacity={1}
            onPress={handleImagePress}
            onLongPress={resetImagePosition}
            delayLongPress={200}>
            {imageUri ? (
              <Animated.Image
                source={{ uri: imageUri }}
                style={[
                  styles.image,
                  {
                    transform: [
                      { scale: scale },
                      { translateX: translateX },
                      { translateY: translateY },
                    ],
                  },
                ]}
                resizeMode="contain"
                {...panResponder.panHandlers}
              />
            ) : null}
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.backgroundClose}
          activeOpacity={1}
          onPress={handleClose} />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundClose: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  imageContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  touchableImage: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.8,
    backgroundColor: 'transparent',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ImagePreview;
