import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import CustomModal from './CustomModal';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';

const {width} = Dimensions.get('window');

interface ConfirmDialogProps {
  visible: boolean;
  title: string;
  message: string;
  onCancel?: () => void;
  onConfirm: () => void;
  cancelText?: string;
  confirmText?: string;
  type?: 'default' | 'danger';
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  visible,
  title,
  message,
  onCancel,
  onConfirm,
  cancelText = '取消',
  confirmText = '确定',
  type = 'default',
}) => {
  const {colors} = useTheme();
  return (
    <CustomModal
      visible={visible}
      onClose={onCancel || (() => {})}
      animationType="fade"
      position="center"
      closeOnBackdropPress={!!onCancel}
      customStyle={styles.confirmModal}>
      <View style={styles.confirmModalContent}>
        <Text style={styles.confirmModalTitle}>{title}</Text>
        <Text style={styles.confirmModalMessage}>{message}</Text>
        <View style={styles.confirmModalButtons}>
          <TouchableOpacity
            style={[styles.confirmModalButton, styles.confirmModalCancelButton]}
            onPress={onCancel}>
            <Text style={styles.confirmModalCancelText}>{cancelText}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.confirmModalButton,
              styles.confirmModalConfirmButton,
              {backgroundColor: colors.primary},
            ]}
            onPress={() => {
              onConfirm();
            }}>
            <Text style={styles.confirmModalConfirmText}>{confirmText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CustomModal>
  );
};

const styles = StyleSheet.create({
  confirmModal: {
    padding: 0,
    borderRadius: 20,
    width: '90%',
    maxWidth: 360,
    overflow: 'hidden',
  },
  confirmModalContent: {
    padding: 24,
  },
  confirmModalTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  confirmModalMessage: {
    fontSize: 15,
    color: COLORS.text.gray,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmModalButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  confirmModalCancelButton: {
    backgroundColor: '#F2F2F7',
  },
  confirmModalConfirmButton: {},
  confirmModalCancelText: {
    color: COLORS.text.primary,
    fontSize: 17,
    fontWeight: '500',
  },
  confirmModalConfirmText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
  },
});

export default ConfirmDialog;
