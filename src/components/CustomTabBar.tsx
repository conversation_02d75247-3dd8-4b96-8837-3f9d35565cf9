import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { COLORS } from '../utils/color';
import { useTheme } from '../context/ThemeContext';
interface TabItem {
  key: string;
  title: string;
  icon: string; // FontAwesome6 图标名称
}

interface CustomTabBarProps {
  tabs: TabItem[];
  activeTab: string;
  onTabPress: (tabKey: string) => void;
}

const CustomTabBar: React.FC<CustomTabBarProps> = ({
  tabs,
  activeTab,
  onTabPress,
}) => {
  const {colors} = useTheme();

  // 渲染中间的添加按钮
  const renderAddButton = () => {
    return (
      <TouchableOpacity
        style={[styles.addButton, {backgroundColor: colors.primary}]}
        onPress={() => onTabPress('add')}
        activeOpacity={0.8}>
        <View style={styles.addButtonInner}>
          <Icon name="plus" size={30} color="#fff" />
        </View>
      </TouchableOpacity>
    );
  };

  // 渲染普通的 Tab 按钮
  const renderTabButton = (tab: TabItem) => {
    const isActive = activeTab === tab.key;

    return (
      <TouchableOpacity
        key={tab.key}
        style={[
          styles.tabButton,
          // isActive && styles.activeTabButton,
        ]}
        onPress={() => onTabPress(tab.key)}
        activeOpacity={0.7}>
        <Icon
          name={tab.icon}
          size={22}
          // color={getIconColor(tab.key)}
          color={colors.primary}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabText,
            // { color: getTextColor(tab.key) },
            {color: colors.primary},
          ]}>
          {tab.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabBar}>
        {/* 左侧 Tab */}
        <View style={styles.tabSide}>{renderTabButton(tabs[0])}</View>

        {/* 中间的添加按钮 */}
        {renderAddButton()}

        {/* 右侧 Tab */}
        <View style={styles.tabSide}>{renderTabButton(tabs[2])}</View>
      </View>

      {/* 底部安全区域填充 */}
      {Platform.OS === 'ios' && <View style={styles.safeAreaFill} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: COLORS.secondary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 10,
    paddingTop: 10,
  },
  tabBar: {
    flexDirection: 'row',
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -10,
  },
  tabSide: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  activeTabButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  tabIcon: {
    marginBottom: 4,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
  },
  addButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    transform: [{translateY: -20}],
  },
  addButtonInner: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  safeAreaFill: {
    height: Platform.OS === 'ios' ? 20 : 0,
  },
});

export default CustomTabBar;
