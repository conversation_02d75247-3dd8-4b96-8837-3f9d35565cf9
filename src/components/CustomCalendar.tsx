import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  GestureResponderEvent,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { COLORS } from '../utils/color';
import dayjs from 'dayjs';
import {useTheme} from '../context/ThemeContext';

interface DayProps {
  date: string;
  day: number;
  month: number;
  year: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasTransactions: boolean;
}

interface CustomCalendarProps {
  onSelectDate: (date: string) => void;
  markedDates?: { [key: string]: any };
  transactionDates?: string[];
  initialDate?: Date;
  onYearMonthPress?: () => void; // 新增：可选，首页传递
}

const CustomCalendar: React.FC<CustomCalendarProps & {ref?: any}> = forwardRef(({
  onSelectDate,
  markedDates = {},
  transactionDates = [],
  initialDate = new Date(),
  onYearMonthPress,
}, ref) => {
  const {colors} = useTheme();
  // 使用 dayjs 处理日期
  const initialDayjs = dayjs(initialDate);
  const [currentMonth, setCurrentMonth] = useState(initialDayjs.month());
  const [currentYear, setCurrentYear] = useState(initialDayjs.year());
  const [selectedDate, setSelectedDate] = useState(initialDayjs.format('YYYY-MM-DD'));

  // 添加触摸状态管理
  const [touchStartX, setTouchStartX] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // 获取当前日期
  const today = dayjs().format('YYYY-MM-DD');

  // 月份名称
  const monthNames = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月',
  ];

  // 星期名称
  const weekDayNames = ['日', '一', '二', '三', '四', '五', '六'];

  // 生成日历数据
  const generateCalendarDays = () => {
    const days: DayProps[] = [];

    // 当月第一天
    const firstDayOfMonth = dayjs(new Date(currentYear, currentMonth, 1));
    // 当月最后一天
    const lastDayOfMonth = firstDayOfMonth.endOf('month');

    // 上个月的最后几天（用于填充当月第一周）
    const daysFromPrevMonth = firstDayOfMonth.day();
    const prevMonth = firstDayOfMonth.subtract(1, 'month');

    // 添加上个月的日期
    for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
      const prevDate = prevMonth.endOf('month').subtract(i, 'day');
      const date = prevDate.format('YYYY-MM-DD');

      days.push({
        date,
        day: prevDate.date(),
        month: prevDate.month(),
        year: prevDate.year(),
        isCurrentMonth: false,
        isToday: date === today,
        isSelected: date === selectedDate,
        hasTransactions: transactionDates.includes(date),
      });
    }

    // 添加当月的日期
    for (let i = 1; i <= lastDayOfMonth.date(); i++) {
      const currentDate = dayjs(new Date(currentYear, currentMonth, i));
      const date = currentDate.format('YYYY-MM-DD');

      days.push({
        date,
        day: i,
        month: currentMonth,
        year: currentYear,
        isCurrentMonth: true,
        isToday: date === today,
        isSelected: date === selectedDate,
        hasTransactions: transactionDates.includes(date),
      });
    }

    // 添加下个月的日期（用于填充当月最后一周）
    const daysFromNextMonth = 42 - days.length; // 6行7列 = 42个日期
    for (let i = 1; i <= daysFromNextMonth; i++) {
      const nextDate = dayjs(new Date(currentYear, currentMonth + 1, i));
      const date = nextDate.format('YYYY-MM-DD');

      days.push({
        date,
        day: nextDate.date(),
        month: nextDate.month(),
        year: nextDate.year(),
        isCurrentMonth: false,
        isToday: date === today,
        isSelected: date === selectedDate,
        hasTransactions: transactionDates.includes(date),
      });
    }

    return days;
  };

  // 处理日期选择
  const handleSelectDate = (date: string, month: number, year: number) => {
    setSelectedDate(date);

    // 如果选择的不是当前显示的月份，则切换到对应月份
    if (month !== currentMonth || year !== currentYear) {
      setCurrentMonth(month);
      setCurrentYear(year);
    }

    onSelectDate(date);
  };

  // 切换到上个月时更新选中日期
  const goToPreviousMonth = () => {
    if (isAnimating) {return;}

    setIsAnimating(true);
    const prevMonth = dayjs(new Date(currentYear, currentMonth, 1)).subtract(1, 'month');

    // 获取当前选中日期的天数
    const currentSelectedDay = dayjs(selectedDate).date();

    // 计算上个月的最后一天
    const lastDayOfPrevMonth = prevMonth.endOf('month').date();

    // 确保新选择的日期不超过上个月的天数
    const newDay = Math.min(currentSelectedDay, lastDayOfPrevMonth);

    // 更新选中的日期
    const newSelectedDate = prevMonth.date(newDay).format('YYYY-MM-DD');
    setSelectedDate(newSelectedDate);

    // 通知父组件日期变化
    onSelectDate(newSelectedDate);

    // 更新月份和年份
    setCurrentMonth(prevMonth.month());
    setCurrentYear(prevMonth.year());

    // 动画完成后重置状态
    setTimeout(() => setIsAnimating(false), 300);
  };

  // 切换到下个月时更新选中日期
  const goToNextMonth = () => {
    if (isAnimating) {return;}

    setIsAnimating(true);
    const nextMonth = dayjs(new Date(currentYear, currentMonth, 1)).add(1, 'month');

    // 获取当前选中日期的天数
    const currentSelectedDay = dayjs(selectedDate).date();

    // 计算下个月的最后一天
    const lastDayOfNextMonth = nextMonth.endOf('month').date();

    // 确保新选择的日期不超过下个月的天数
    const newDay = Math.min(currentSelectedDay, lastDayOfNextMonth);

    // 更新选中的日期
    const newSelectedDate = nextMonth.date(newDay).format('YYYY-MM-DD');
    setSelectedDate(newSelectedDate);

    // 通知父组件日期变化
    onSelectDate(newSelectedDate);

    // 更新月份和年份
    setCurrentMonth(nextMonth.month());
    setCurrentYear(nextMonth.year());

    // 动画完成后重置状态
    setTimeout(() => setIsAnimating(false), 300);
  };

  // 处理触摸开始
  const handleTouchStart = (event: GestureResponderEvent) => {
    setTouchStartX(event.nativeEvent.pageX);
  };

  // 处理触摸结束
  const handleTouchEnd = (event: GestureResponderEvent) => {
    const touchEndX = event.nativeEvent.pageX;
    const diff = touchEndX - touchStartX;

    // 如果水平滑动超过50像素，则切换月份
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        // 向右滑 -> 上个月
        goToPreviousMonth();
      } else {
        // 向左滑 -> 下个月
        goToNextMonth();
      }
    }
  };

  // 跳转到今天
  const goToToday = () => {
    if (isAnimating) {return;}

    setIsAnimating(true);
    const todayObj = dayjs();

    // 更新选中的日期为今天
    const todayDate = todayObj.format('YYYY-MM-DD');
    setSelectedDate(todayDate);

    // 通知父组件日期变化
    onSelectDate(todayDate);

    // 更新月份和年份
    setCurrentMonth(todayObj.month());
    setCurrentYear(todayObj.year());

    // 动画完成后重置状态
    setTimeout(() => setIsAnimating(false), 300);
  };

  // 在 CustomCalendar 组件中添加 useEffect 来监听 transactionDates 的变化
  useEffect(() => {
    console.log('交易日期更新:', transactionDates);
    // 如果需要，可以在这里更新日历的显示
  }, [transactionDates]);

  // 用 useImperativeHandle 正确暴露 scrollToMonth
  useImperativeHandle(ref, () => ({
    scrollToMonth: (date: Date) => {
      const d = dayjs(date);
      setCurrentYear(d.year());
      setCurrentMonth(d.month());
      setSelectedDate(d.format('YYYY-MM-DD'));
      onSelectDate(d.format('YYYY-MM-DD'));
    },
  }), [onSelectDate]);

  // 渲染日期项
  const renderDay = ({ item }: { item: DayProps }) => {
    const dateString = `${item.year}-${String(item.month + 1).padStart(2, '0')}-${String(item.day).padStart(2, '0')}`;
    const isCurrentMonth = item.month === currentMonth && item.year === currentYear;
    const isToday = dateString === today;
    const isSelected = dateString === selectedDate;
    const hasTransaction = transactionDates.includes(dateString);

    return (
      <TouchableOpacity
        style={[
          styles.dayContainer,
          !isCurrentMonth && styles.notCurrentMonth,
          isToday && styles.today,
          isSelected && styles.selected,
        ]}
        onPress={() => handleSelectDate(dateString, item.month, item.year)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.dayText,
            !isCurrentMonth && styles.notCurrentMonthText,
            isToday && styles.todayText,
            isSelected && styles.selectedText,
          ]}
        >
          {item.day}
        </Text>
        {hasTransaction && <View style={styles.transactionDot} />}
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={[styles.container, {backgroundColor: colors.primary}]}
      // onTouchStart={handleTouchStart}
      // onTouchEnd={handleTouchEnd}
    >
      {/* 日历头部 - 年月和导航按钮 */}
      <View style={styles.header}>
        <TouchableOpacity
          activeOpacity={onYearMonthPress ? 0.7 : 1}
          onPress={onYearMonthPress}
          style={{flex: 1}}
        >
          <Text style={styles.monthYearText}>{`${currentYear}年${monthNames[currentMonth]}`}</Text>
        </TouchableOpacity>
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.arrowButton}
            onPress={goToPreviousMonth}
            activeOpacity={0.7}
            disabled={isAnimating}
          >
            <View style={styles.arrowButtonCircle}>
              <Icon name="chevron-left" size={14} color={colors.primary} />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.todayButton}
            onPress={goToToday}
            activeOpacity={0.7}
            disabled={isAnimating}
          >
            <View style={styles.todayButtonCircle}>
              <Text style={styles.todayButtonText}>今天</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.arrowButton}
            onPress={goToNextMonth}
            activeOpacity={0.7}
            disabled={isAnimating}
          >
            <View style={styles.arrowButtonCircle}>
              <Icon name="chevron-right" size={14} color={colors.primary} />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* 星期标题 */}
      <View style={styles.weekdaysContainer}>
        {weekDayNames.map((day, index) => (
          <Text key={index} style={styles.weekdayText}>
            {day}
          </Text>
        ))}
      </View>

      {/* 日历网格 */}
      <FlatList
        data={generateCalendarDays()}
        renderItem={renderDay}
        keyExtractor={(item) => item.date}
        numColumns={7}
        scrollEnabled={false}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    paddingBottom: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  monthYearText: {
    marginBottom: 10,
    marginRight: -20,
    fontSize: 40,
    fontWeight: '600',
    color: COLORS.text.light,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowButton: {
    padding: 4,
  },
  arrowButtonCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.background.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  todayButton: {
    padding: 4,
    marginHorizontal: 4,
  },
  todayButtonCircle: {
    width: 40,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.background.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  todayButtonText: {
    fontSize: 10,
    lineHeight: 24,
    fontWeight: '600',
    color: COLORS.primary,
  },
  arrowButtonText: {
    fontSize: 18,
    lineHeight: 20,
    fontWeight: '300',
  },
  weekdaysContainer: {
    flexDirection: 'row',
    paddingVertical: 6,
  },
  weekdayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  dayContainer: {
    width: '14.28%',
    aspectRatio: 1.3,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 2,
  },
  dayText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text.light,
  },
  notCurrentMonth: {
    opacity: 0.5,
  },
  notCurrentMonthText: {
    color: COLORS.text.light,
  },
  today: {
    // borderWidth: 1,
    // borderColor: COLORS.background.white,
    // borderRadius: 16,
  },
  todayText: {
    color: COLORS.background.white,
    fontWeight: '600',
  },
  selected: {
    backgroundColor: COLORS.background.white,
    borderRadius: 20,
  },
  selectedText: {
    color: COLORS.text.secondary,
    fontWeight: '600',
  },
  transactionDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.functional.warning,
    marginTop: 1,
  },
});

export default CustomCalendar;
