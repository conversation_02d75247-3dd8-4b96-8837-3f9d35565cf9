import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import CustomCalendar from './CustomCalendar';
import { COLORS } from '../utils/color';
import CustomModal from './CustomModal';
import { useTheme } from '../context/ThemeContext';
interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectDate: (date: string) => void;
  selectedDate: string;
}

const DatePickerModal: React.FC<DatePickerModalProps> = ({
  visible,
  onClose,
  onSelectDate,
  selectedDate,
}) => {
  const {colors} = useTheme();
  const handleSelectDate = (date: string) => {
    onSelectDate(date);
    // onClose();
  };

  return (
    <CustomModal
      visible={visible}
      onClose={onClose}
      animationType="slide"
      position="bottom"
      closeOnBackdropPress={true}
      customStyle={{backgroundColor: colors.primary, padding: 0}}
    >
      <View style={[styles.container, {backgroundColor: colors.primary}]}>
        <View style={styles.header}>
          <Text style={styles.title}>选择日期</Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.closeButton}>关闭</Text>
          </TouchableOpacity>
        </View>

        <CustomCalendar
          onSelectDate={handleSelectDate}
          initialDate={new Date(selectedDate)}
        />
      </View>
    </CustomModal>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 30,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    fontSize: 16,
    color: '#FFFFFF',
  },
});

export default DatePickerModal;
