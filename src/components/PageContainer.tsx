import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Text,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CustomHeader from './CustomHeader';
import { COLORS } from '../utils/color';
import { useTheme } from '../context/ThemeContext';

interface PageContainerProps {
  children: React.ReactNode;
  paddingTop?: number;
  backgroundColor?: string;
  headerTitle?: string;
  showHeader?: boolean;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  showBackButton?: boolean;
  headerRight?: React.ReactNode;
}

const PageContainer: React.FC<PageContainerProps> = ({
  children,
  paddingTop,
  backgroundColor,
  headerTitle,
  showHeader = true,
  leftComponent,
  rightComponent,
  onLeftPress,
  onRightPress,
  showBackButton = true,
  headerRight,
}) => {
  // 使用 useSafeAreaInsets 获取安全区域的尺寸
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();

  // 获取状态栏高度
  // const STATUSBAR_HEIGHT = Platform.OS === 'ios'
  //   ? insets.top
  //   : StatusBar.currentHeight || 0;
  const STATUSBAR_HEIGHT = insets.top;

  return (
    <View
      style={[
        styles.container,
        {backgroundColor: backgroundColor || colors.background.light, paddingTop: STATUSBAR_HEIGHT},
      ]}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={backgroundColor || colors.background.light}
        translucent={true}
      />

      {/* 顶部安全区域填充 */}
      {/* <View style={{height: STATUSBAR_HEIGHT, backgroundColor}} /> */}

      {showHeader && (
        <CustomHeader
          title={headerTitle}
          leftComponent={leftComponent}
          rightComponent={rightComponent}
          onLeftPress={onLeftPress}
          onRightPress={onRightPress}
          backgroundColor={backgroundColor || colors.background.light}
          showBackButton={showBackButton}
        />
      )}

      <View
        style={[
          styles.content,
          // paddingTop !== undefined && {paddingTop},
          // 底部安全区域填充
          // { paddingBottom: insets.bottom },
        ]}>
        {children}
      </View>

      {headerRight && <View style={styles.headerRight}>{headerRight}</View>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  rightComponent: {
    position: 'absolute',
    right: 10,
  },
  headerRight: {
    position: 'absolute',
    right: 16,
  },
});

export default PageContainer;
