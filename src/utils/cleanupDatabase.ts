import databaseService from '../services/DatabaseService';

// 临时清理脚本 - 清理重复的默认账本
export const cleanupDuplicateAccountBooks = async () => {
  try {
    console.log('开始清理重复的默认账本...');
    await databaseService.cleanupDuplicateDefaultAccountBooks();
    console.log('清理完成！');
    return { success: true, message: '重复的默认账本已清理完成' };
  } catch (error) {
    console.error('清理失败:', error);
    return { success: false, message: '清理失败: ' + error.message };
  }
};

// 查看当前账本状态
export const checkAccountBooksStatus = async () => {
  try {
    const accountBooks = await databaseService.getAllAccountBooks();
    const defaultBooks = accountBooks.filter(book => book.isDefault);

    console.log('=== 账本状态检查 ===');
    console.log(`总账本数量: ${accountBooks.length}`);
    console.log(`默认账本数量: ${defaultBooks.length}`);

    if (defaultBooks.length > 1) {
      console.log('⚠️ 发现多个默认账本:');
      defaultBooks.forEach((book, index) => {
        console.log(`  ${index + 1}. ID: ${book.id}, 名称: ${book.name}, 创建时间: ${book.createdAt}`);
      });
    } else if (defaultBooks.length === 1) {
      console.log('✅ 默认账本正常:', defaultBooks[0]);
    } else {
      console.log('❌ 没有找到默认账本');
    }

    console.log('所有账本:');
    accountBooks.forEach((book, index) => {
      console.log(`  ${index + 1}. ${book.name} (ID: ${book.id}, 默认: ${book.isDefault ? '是' : '否'})`);
    });

    return {
      totalBooks: accountBooks.length,
      defaultBooks: defaultBooks.length,
      books: accountBooks,
    };
  } catch (error) {
    console.error('检查账本状态失败:', error);
    return { error: error.message };
  }
};

// 强制重新初始化数据库（仅用于调试）
export const forceReinitializeDatabase = async () => {
  try {
    await databaseService.forceReinitializeDatabase();

    // 检查结果
    const status = await checkAccountBooksStatus();
    return { success: true, status };
  } catch (error) {
    console.error('强制重新初始化失败:', error);
    return { success: false, error: (error as Error).message };
  }
};

// 检查数据库版本信息
export const checkDatabaseVersion = async () => {
  try {
    const versionInfo = await databaseService.getVersionInfo();
    console.log('数据库版本信息:', versionInfo);
    return versionInfo;
  } catch (error) {
    console.error('检查数据库版本失败:', error);
    return { error: (error as Error).message };
  }
};

// 测试清空所有数据功能
export const testClearAllData = async () => {
  try {
    console.log('=== 测试清空所有数据功能 ===');

    // 1. 清空前检查状态
    console.log('清空前状态:');
    const beforeStatus = await checkAccountBooksStatus();
    console.log('清空前账本数量:', beforeStatus.totalBooks);

    // 2. 执行清空操作
    console.log('开始清空所有数据...');
    await databaseService.clearAllData();
    console.log('清空操作完成');

    // 3. 清空后检查状态
    console.log('清空后状态:');
    const afterStatus = await checkAccountBooksStatus();
    console.log('清空后账本数量:', afterStatus.totalBooks);

    // 4. 检查版本信息
    const versionInfo = await checkDatabaseVersion();
    console.log('清空后版本信息:', versionInfo);

    return {
      success: true,
      beforeStatus,
      afterStatus,
      versionInfo,
    };
  } catch (error) {
    console.error('测试清空功能失败:', error);
    return { success: false, error: (error as Error).message };
  }
};

// 测试重置数据库功能
export const testResetDatabase = async () => {
  try {
    console.log('=== 测试重置数据库功能 ===');

    // 1. 重置前检查状态
    console.log('重置前状态:');
    const beforeStatus = await checkAccountBooksStatus();
    console.log('重置前账本数量:', beforeStatus.totalBooks);

    // 2. 执行重置操作
    console.log('开始重置数据库...');
    await databaseService.resetDatabase();
    console.log('重置操作完成');

    // 3. 重置后检查状态
    console.log('重置后状态:');
    const afterStatus = await checkAccountBooksStatus();
    console.log('重置后账本数量:', afterStatus.totalBooks);

    // 4. 检查版本信息
    const versionInfo = await checkDatabaseVersion();
    console.log('重置后版本信息:', versionInfo);

    return {
      success: true,
      beforeStatus,
      afterStatus,
      versionInfo,
    };
  } catch (error) {
    console.error('测试重置功能失败:', error);
    return { success: false, error: (error as Error).message };
  }
};
