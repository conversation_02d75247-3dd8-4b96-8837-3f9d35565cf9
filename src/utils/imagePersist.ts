import RNFS from 'react-native-fs';

export async function persistImageToAppDir(imageUri: string, productId: number | string) {
  if (!imageUri) {return null;}
  try {
    // 创建图片存储目录
    const imagesDir = `${RNFS.DocumentDirectoryPath}/product_images`;
    const exists = await RNFS.exists(imagesDir);
    if (!exists) {
      await RNFS.mkdir(imagesDir);
    }
    // 生成唯一文件名
    const ext = imageUri.split('.').pop()?.split('?')[0] || 'jpg';
    const fileName = `product_${productId}_${Date.now()}.${ext}`;
    const destPath = `${imagesDir}/${fileName}`;
    // 复制图片
    await RNFS.copyFile(
      imageUri.startsWith('file://') ? imageUri : `file://${imageUri}`,
      destPath
    );
    return `file://${destPath}`;
  } catch (e) {
    console.error('图片持久化失败:', e);
    return imageUri; // 失败时返回原始路径
  }
}
