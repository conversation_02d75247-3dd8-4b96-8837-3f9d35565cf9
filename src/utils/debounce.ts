/**
 * 防抖函数 - 确保函数在一定时间内只执行一次
 * @param func 要执行的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖处理后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number = 500
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  let isExecuting = false; // 标记函数是否正在执行

  return function(this: any, ...args: Parameters<T>) {
    const context = this;

    // 如果函数正在执行，直接返回
    if (isExecuting) {
      return;
    }

    // 清除之前的定时器
    if (timeout) {
      clearTimeout(timeout);
    }

    // 标记函数正在执行
    isExecuting = true;

    // 执行函数
    func.apply(context, args);

    // 设置定时器，在wait时间后才允许再次执行
    timeout = setTimeout(() => {
      isExecuting = false;
      timeout = null;
    }, wait);
  };
}
