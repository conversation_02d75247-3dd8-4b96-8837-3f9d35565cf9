/**
 * 消费行为分析与智能建议算法
 * 每日运势分析算法
 */

// 分析消费行为特征
export const analyzeConsumptionBehavior = (transactions, categories) => {
  if (!transactions || transactions.length === 0) {
    return {
      topCategories: [],
      traits: [],
      growthRate: 1.0,
    };
  }

  // 按分类统计支出
  const categoryExpenses = {};
  const categoryMap = {};
  let totalExpense = 0;

  // 创建分类映射
  categories.forEach(category => {
    categoryMap[category.id] = category;
  });

  // 只分析支出类型的交易
  const expenseTransactions = transactions.filter(t => t.type === 'expense');

  // 计算总支出
  expenseTransactions.forEach(transaction => {
    totalExpense += transaction.amount;

    if (!categoryExpenses[transaction.categoryId]) {
      categoryExpenses[transaction.categoryId] = 0;
    }

    categoryExpenses[transaction.categoryId] += transaction.amount;
  });

  // 计算每个分类的占比，并排序
  const categoriesData = Object.keys(categoryExpenses).map(categoryId => {
    const category = categoryMap[categoryId] || { name: '未知分类', icon: 'question' };
    const amount = categoryExpenses[categoryId];
    const percentage = (amount / totalExpense) * 100;

    return {
      id: categoryId,
      name: category.name,
      icon: category.icon,
      amount,
      percentage,
      color: getCategoryColor(category.name),
    };
  }).sort((a, b) => b.amount - a.amount);

  // 取前5个分类
  const topCategories = categoriesData.slice(0, 5);

  // 分析消费习惯特征
  const traits = analyzeConsumptionTraits(expenseTransactions, categoriesData, categoryMap);

  // 计算消费增长率（简化版，实际应用中可能需要更复杂的算法）
  const growthRate = calculateGrowthRate(expenseTransactions);

  return {
    topCategories,
    traits,
    growthRate,
  };
};

// 分析消费习惯特征
const analyzeConsumptionTraits = (transactions, categoriesData, categoryMap) => {
  const traits = [];

  // 如果交易数据不足，返回空数组
  if (transactions.length < 5) {
    return [
      {
        title: '数据不足',
        description: '交易记录较少，无法进行全面分析。继续记录你的收支，以获得更准确的分析。',
        icon: 'chart-line',
      },
    ];
  }

  // 分析消费频率
  const dates = transactions.map(t => new Date(t.date).toDateString());
  const uniqueDates = new Set(dates);
  const frequencyRatio = uniqueDates.size / 30; // 假设一个月30天

  if (frequencyRatio > 0.7) {
    traits.push({
      title: '高频消费者',
      description: '你几乎每天都有消费记录，属于高频消费人群。',
      icon: 'bolt',
    });
  } else if (frequencyRatio < 0.3) {
    traits.push({
      title: '低频消费者',
      description: '你的消费频率较低，可能倾向于集中采购或计划性消费。',
      icon: 'calendar-check',
    });
  }

  // 分析消费时间
  const timeDistribution = analyzeTimeDistribution(transactions);
  if (timeDistribution.evening > 0.5) {
    traits.push({
      title: '夜间消费偏好',
      description: '你有超过一半的消费发生在晚上，可能需要注意夜间冲动消费。',
      icon: 'moon',
    });
  } else if (timeDistribution.morning > 0.4) {
    traits.push({
      title: '早间消费习惯',
      description: '你倾向于在早上进行消费，这通常表明有计划性的消费习惯。',
      icon: 'sun',
    });
  }

  // 分析消费类型偏好
  if (categoriesData.length > 0) {
    const topCategory = categoriesData[0];
    if (topCategory.percentage > 40) {
      traits.push({
        title: `${topCategory.name}消费占比高`,
        description: `${topCategory.name}类消费占总支出的${topCategory.percentage.toFixed(1)}%，是你的主要支出方向。`,
        icon: topCategory.icon || 'tag',
      });
    }

    // 检查是否有多样化的消费
    if (categoriesData.length >= 5 && categoriesData[4].percentage > 5) {
      traits.push({
        title: '多元化消费习惯',
        description: '你的消费分布在多个类别，消费结构较为均衡。',
        icon: 'layer-group',
      });
    }
  }

  // 分析消费金额特征
  const amounts = transactions.map(t => t.amount);
  const avgAmount = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
  const maxAmount = Math.max(...amounts);

  if (maxAmount > avgAmount * 5) {
    traits.push({
      title: '大额消费倾向',
      description: '你有明显的大额消费记录，最高单笔消费金额远高于平均水平。',
      icon: 'money-bill-wave',
    });
  }

  // 分析周期性消费
  const periodicTraits = analyzePeriodicConsumption(transactions);
  if (periodicTraits) {
    traits.push(periodicTraits);
  }

  // 限制特征数量，最多返回4个
  return traits.slice(0, 4);
};

// 分析消费时间分布
const analyzeTimeDistribution = (transactions) => {
  let morning = 0; // 6-12点
  let afternoon = 0; // 12-18点
  let evening = 0; // 18-24点
  let night = 0; // 0-6点

  transactions.forEach(transaction => {
    if (!transaction.time) {return;}

    const [hours, minutes] = transaction.time.split(':').map(Number);

    if (hours >= 6 && hours < 12) {
      morning++;
    } else if (hours >= 12 && hours < 18) {
      afternoon++;
    } else if (hours >= 18 && hours < 24) {
      evening++;
    } else {
      night++;
    }
  });

  const total = morning + afternoon + evening + night;

  return {
    morning: morning / total,
    afternoon: afternoon / total,
    evening: evening / total,
    night: night / total,
  };
};

// 分析周期性消费
const analyzePeriodicConsumption = (transactions) => {
  // 按日期排序
  const sortedTransactions = [...transactions].sort((a, b) => new Date(a.date) - new Date(b.date));

  // 检查是否有固定日期的消费
  const dayOfMonthMap = {};

  sortedTransactions.forEach(transaction => {
    const date = new Date(transaction.date);
    const dayOfMonth = date.getDate();

    if (!dayOfMonthMap[dayOfMonth]) {
      dayOfMonthMap[dayOfMonth] = 0;
    }

    dayOfMonthMap[dayOfMonth]++;
  });

  // 查找出现频率最高的日期
  let maxDay = 0;
  let maxCount = 0;

  Object.keys(dayOfMonthMap).forEach(day => {
    if (dayOfMonthMap[day] > maxCount) {
      maxDay = parseInt(day);
      maxCount = dayOfMonthMap[day];
    }
  });

  // 如果某个日期的消费次数超过总月数的一半，认为是周期性消费
  const totalMonths = Math.ceil(sortedTransactions.length / 30); // 粗略估计月数

  if (maxCount >= totalMonths / 2 && maxCount >= 3) {
    return {
      title: '固定日期消费',
      description: `你倾向于在每月${maxDay}日前后进行消费，可能与工资发放或账单日期相关。`,
      icon: 'calendar-day',
    };
  }

  return null;
};

// 计算消费增长率
const calculateGrowthRate = (transactions) => {
  if (transactions.length < 10) {return 1.0;} // 数据不足，返回默认值

  // 按月份分组
  const monthlyExpenses = {};

  transactions.forEach(transaction => {
    const date = new Date(transaction.date);
    const yearMonth = `${date.getFullYear()}-${date.getMonth() + 1}`;

    if (!monthlyExpenses[yearMonth]) {
      monthlyExpenses[yearMonth] = 0;
    }

    monthlyExpenses[yearMonth] += transaction.amount;
  });

  // 按时间排序
  const sortedMonths = Object.keys(monthlyExpenses).sort();

  // 如果月份数量少于2，无法计算增长率
  if (sortedMonths.length < 2) {return 1.0;}

  // 计算前半段和后半段的平均消费
  const halfIndex = Math.floor(sortedMonths.length / 2);

  const firstHalfMonths = sortedMonths.slice(0, halfIndex);
  const secondHalfMonths = sortedMonths.slice(halfIndex);

  const firstHalfTotal = firstHalfMonths.reduce((sum, month) => sum + monthlyExpenses[month], 0);
  const secondHalfTotal = secondHalfMonths.reduce((sum, month) => sum + monthlyExpenses[month], 0);

  const firstHalfAvg = firstHalfTotal / firstHalfMonths.length;
  const secondHalfAvg = secondHalfTotal / secondHalfMonths.length;

  // 计算增长率
  if (firstHalfAvg === 0) {return 1.0;}

  const growthRate = secondHalfAvg / firstHalfAvg;

  // 限制增长率在合理范围内
  return Math.max(0.5, Math.min(growthRate, 1.5));
};

// 计算财务健康评分
export const calculateHealthScore = (transactions, yearlyTotal) => {
  if (!transactions || transactions.length === 0) {
    return {
      totalScore: 75,
      scoreDescription: '数据不足，无法进行准确评分',
      factors: [],
    };
  }

  // 计算各项评分因素
  const factors = [];

  // 1. 收支平衡因素
  const balanceRatio = yearlyTotal.income > 0 ? yearlyTotal.expense / yearlyTotal.income : 2;
  let balanceScore = 0;
  let balanceDescription = '';

  if (balanceRatio <= 0.5) {
    balanceScore = 100;
    balanceDescription = '收入远高于支出，财务状况非常健康';
  } else if (balanceRatio <= 0.7) {
    balanceScore = 90;
    balanceDescription = '收入明显高于支出，有良好的储蓄习惯';
  } else if (balanceRatio <= 0.9) {
    balanceScore = 80;
    balanceDescription = '收入高于支出，财务状况良好';
  } else if (balanceRatio <= 1.0) {
    balanceScore = 70;
    balanceDescription = '收支基本平衡，建议增加储蓄';
  } else if (balanceRatio <= 1.1) {
    balanceScore = 60;
    balanceDescription = '支出略高于收入，需要注意控制消费';
  } else if (balanceRatio <= 1.3) {
    balanceScore = 40;
    balanceDescription = '支出明显高于收入，财务状况不佳';
  } else {
    balanceScore = 20;
    balanceDescription = '支出远高于收入，财务状况堪忧';
  }

  factors.push({
    name: '收支平衡度',
    score: balanceScore,
    description: balanceDescription,
    color: getScoreColor(balanceScore),
  });

  // 2. 消费稳定性因素
  const expenseTransactions = transactions.filter(t => t.type === 'expense');
  const monthlyExpenses = {};

  expenseTransactions.forEach(transaction => {
    const date = new Date(transaction.date);
    const yearMonth = `${date.getFullYear()}-${date.getMonth() + 1}`;

    if (!monthlyExpenses[yearMonth]) {
      monthlyExpenses[yearMonth] = 0;
    }

    monthlyExpenses[yearMonth] += transaction.amount;
  });

  const monthlyValues = Object.values(monthlyExpenses);

  let stabilityScore = 0;
  let stabilityDescription = '';

  if (monthlyValues.length < 2) {
    stabilityScore = 75;
    stabilityDescription = '数据不足，无法评估消费稳定性';
  } else {
    // 计算变异系数 (标准差/平均值)
    const avg = monthlyValues.reduce((sum, val) => sum + val, 0) / monthlyValues.length;
    const variance = monthlyValues.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / monthlyValues.length;
    const stdDev = Math.sqrt(variance);
    const cv = stdDev / avg;

    if (cv < 0.1) {
      stabilityScore = 100;
      stabilityDescription = '月度支出非常稳定，有良好的消费计划';
    } else if (cv < 0.2) {
      stabilityScore = 90;
      stabilityDescription = '月度支出较为稳定，消费习惯良好';
    } else if (cv < 0.3) {
      stabilityScore = 80;
      stabilityDescription = '月度支出波动在合理范围内';
    } else if (cv < 0.4) {
      stabilityScore = 70;
      stabilityDescription = '月度支出有一定波动，建议更好地规划消费';
    } else if (cv < 0.5) {
      stabilityScore = 60;
      stabilityDescription = '月度支出波动较大，消费计划性不足';
    } else if (cv < 0.7) {
      stabilityScore = 40;
      stabilityDescription = '月度支出波动明显，消费习惯不稳定';
    } else {
      stabilityScore = 20;
      stabilityDescription = '月度支出波动极大，缺乏消费规划';
    }
  }

  factors.push({
    name: '消费稳定性',
    score: stabilityScore,
    description: stabilityDescription,
    color: getScoreColor(stabilityScore),
  });

  // 3. 消费多样性因素
  const categoryExpenses = {};
  let totalExpense = 0;

  expenseTransactions.forEach(transaction => {
    if (!categoryExpenses[transaction.categoryId]) {
      categoryExpenses[transaction.categoryId] = 0;
    }

    categoryExpenses[transaction.categoryId] += transaction.amount;
    totalExpense += transaction.amount;
  });

  const categoryPercentages = Object.values(categoryExpenses).map(amount => amount / totalExpense);

  let diversityScore = 0;
  let diversityDescription = '';

  if (categoryPercentages.length <= 1) {
    diversityScore = 60;
    diversityDescription = '消费类别较少，建议拓展生活领域';
  } else {
    // 计算消费集中度
    const maxPercentage = Math.max(...categoryPercentages);

    if (maxPercentage > 0.7) {
      diversityScore = 40;
      diversityDescription = '消费过于集中在单一类别，生活多样性不足';
    } else if (maxPercentage > 0.5) {
      diversityScore = 60;
      diversityDescription = '消费相对集中，可以适当拓展其他领域';
    } else if (maxPercentage > 0.3) {
      diversityScore = 80;
      diversityDescription = '消费分布较为均衡，生活多样性良好';
    } else {
      diversityScore = 100;
      diversityDescription = '消费分布非常均衡，生活领域丰富多彩';
    }
  }

  factors.push({
    name: '消费多样性',
    score: diversityScore,
    description: diversityDescription,
    color: getScoreColor(diversityScore),
  });

  // 4. 大额消费控制因素
  const amounts = expenseTransactions.map(t => t.amount);
  const avgAmount = amounts.length > 0 ? amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length : 0;

  // 计算大额消费比例
  const largeExpenseCount = amounts.filter(amount => amount > avgAmount * 3).length;
  const largeExpenseRatio = amounts.length > 0 ? largeExpenseCount / amounts.length : 0;

  let controlScore = 0;
  let controlDescription = '';

  if (largeExpenseRatio === 0) {
    controlScore = 100;
    controlDescription = '没有明显的大额消费，消费控制能力极佳';
  } else if (largeExpenseRatio < 0.05) {
    controlScore = 90;
    controlDescription = '大额消费比例很低，消费控制能力优秀';
  } else if (largeExpenseRatio < 0.1) {
    controlScore = 80;
    controlDescription = '大额消费比例较低，消费控制能力良好';
  } else if (largeExpenseRatio < 0.15) {
    controlScore = 70;
    controlDescription = '大额消费在合理范围内，消费控制能力尚可';
  } else if (largeExpenseRatio < 0.2) {
    controlScore = 60;
    controlDescription = '大额消费比例略高，需要加强消费控制';
  } else if (largeExpenseRatio < 0.3) {
    controlScore = 40;
    controlDescription = '大额消费比例高，消费控制能力不足';
  } else {
    controlScore = 20;
    controlDescription = '大额消费比例过高，消费控制能力差';
  }

  factors.push({
    name: '消费控制力',
    score: controlScore,
    description: controlDescription,
    color: getScoreColor(controlScore),
  });

  // 计算总分（各因素加权平均）
  const weights = [0.4, 0.2, 0.2, 0.2]; // 收支平衡权重最高
  const totalScore = Math.round(
    factors.reduce((sum, factor, index) => sum + factor.score * weights[index], 0)
  );

  // 生成总体评价
  let scoreDescription = '';

  if (totalScore >= 90) {
    scoreDescription = '财务状况极佳，继续保持良好的消费习惯';
  } else if (totalScore >= 80) {
    scoreDescription = '财务状况良好，有稳健的消费和储蓄习惯';
  } else if (totalScore >= 70) {
    scoreDescription = '财务状况健康，可以在部分方面进一步改善';
  } else if (totalScore >= 60) {
    scoreDescription = '财务状况一般，需要关注收支平衡和消费控制';
  } else if (totalScore >= 50) {
    scoreDescription = '财务状况不佳，建议调整消费习惯和提高储蓄';
  } else {
    scoreDescription = '财务状况堪忧，需要认真规划财务并控制支出';
  }

  return {
    totalScore,
    scoreDescription,
    factors,
  };
};

// 生成智能财务建议
export const generateSmartSuggestions = (transactions, categories, behaviorAnalysis, healthScore, yearlyTotal) => {
  if (!transactions || transactions.length === 0) {
    return [
      {
        title: '开始记录你的收支',
        description: '持续记录收支是理财的第一步，帮助你了解自己的消费习惯。',
        icon: 'pencil',
        color: '#007AFF',
      },
    ];
  }

  const suggestions = [];

  // 1. 基于收支平衡的建议
  const balanceFactor = healthScore.factors.find(f => f.name === '收支平衡度');

  if (balanceFactor && balanceFactor.score < 70) {
    suggestions.push({
      title: '控制支出，增加储蓄',
      description: '你的支出接近或超过收入，建议制定预算计划，减少非必要开支，增加储蓄比例。',
      icon: 'piggy-bank',
      color: '#FF3B30',
    });
  } else if (yearlyTotal.expense > yearlyTotal.income * 0.9) {
    suggestions.push({
      title: '提高储蓄比例',
      description: '你的支出占收入比例较高，建议将收入的20%作为储蓄，提高财务安全感。',
      icon: 'sack-dollar',
      color: '#FF9500',
    });
  }

  // 2. 基于消费类别的建议
  if (behaviorAnalysis.topCategories.length > 0) {
    const topCategory = behaviorAnalysis.topCategories[0];

    if (topCategory.percentage > 40) {
      suggestions.push({
        title: `优化${topCategory.name}支出`,
        description: `${topCategory.name}占你总支出的${topCategory.percentage.toFixed(1)}%，建议寻找更经济的替代方案或优惠渠道。`,
        icon: topCategory.icon || 'tag',
        color: '#5856D6',
      });
    }
  }

  // 3. 基于消费稳定性的建议
  const stabilityFactor = healthScore.factors.find(f => f.name === '消费稳定性');

  if (stabilityFactor && stabilityFactor.score < 60) {
    suggestions.push({
      title: '制定月度预算计划',
      description: '你的月度支出波动较大，建议制定详细的月度预算计划，并严格执行。',
      icon: 'calendar-days',
      color: '#FF9500',
    });
  }

  // 4. 基于消费控制力的建议
  const controlFactor = healthScore.factors.find(f => f.name === '消费控制力');

  if (controlFactor && controlFactor.score < 60) {
    suggestions.push({
      title: '控制冲动消费',
      description: '你有较多大额消费记录，建议对超过平均消费3倍的支出进行24小时冷静期考虑。',
      icon: 'hourglass-half',
      color: '#FF3B30',
    });
  }

  // 5. 基于消费时间的建议
  const expenseTransactions = transactions.filter(t => t.type === 'expense');
  const timeDistribution = analyzeTimeDistribution(expenseTransactions);

  if (timeDistribution.evening > 0.4 || timeDistribution.night > 0.2) {
    suggestions.push({
      title: '注意夜间消费习惯',
      description: '你在晚间的消费比例较高，夜间消费往往容易冲动，建议提前规划购物清单。',
      icon: 'moon',
      color: '#5856D6',
    });
  }

  // 6. 投资建议
  if (yearlyTotal.balance > yearlyTotal.income * 0.3 && yearlyTotal.income > 0) {
    suggestions.push({
      title: '考虑适当投资理财',
      description: '你有较高的储蓄比例，可以考虑将部分闲置资金用于稳健的投资理财产品，提高资金效率。',
      icon: 'chart-line',
      color: '#34C759',
    });
  }

  // 7. 消费多样性建议
  const diversityFactor = healthScore.factors.find(f => f.name === '消费多样性');

  if (diversityFactor && diversityFactor.score < 60) {
    suggestions.push({
      title: '拓展生活领域',
      description: '你的消费类别较为集中，可以尝试拓展新的兴趣爱好和生活领域，丰富生活体验。',
      icon: 'compass',
      color: '#007AFF',
    });
  }

  // 8. 基于增长率的建议
  if (behaviorAnalysis.growthRate > 1.2) {
    suggestions.push({
      title: '关注消费增长趋势',
      description: '你的消费呈现明显上升趋势，建议关注增长原因，避免生活成本不断攀升。',
      icon: 'arrow-trend-up',
      color: '#FF3B30',
    });
  }

  // 如果建议不足，添加通用建议
  if (suggestions.length < 3) {
    suggestions.push({
      title: '定期回顾财务状况',
      description: '建议每月抽出时间回顾收支情况，及时调整财务计划，保持良好的财务习惯。',
      icon: 'rotate',
      color: '#007AFF',
    });
  }

  // 最多返回4条建议
  return suggestions.slice(0, 4);
};

// 辅助函数：根据分类名称生成颜色
const getCategoryColor = (categoryName) => {
  const colors = [
    '#FF3B30', // 红色
    '#FF9500', // 橙色
    '#FFCC00', // 黄色
    '#34C759', // 绿色
    '#5AC8FA', // 浅蓝
    '#007AFF', // 蓝色
    '#5856D6', // 紫色
    '#AF52DE', // 紫罗兰
    '#FF2D55', // 粉红
    '#A2845E', // 棕色
  ];

  // 使用字符串的字符码总和作为哈希值
  const hash = categoryName.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);

  return colors[hash % colors.length];
};

// 辅助函数：根据分数生成颜色
const getScoreColor = (score) => {
  if (score >= 90) {return '#34C759';} // 绿色
  if (score >= 70) {return '#30D158';} // 浅绿
  if (score >= 60) {return '#FFCC00';} // 黄色
  if (score >= 40) {return '#FF9500';} // 橙色
  return '#FF3B30'; // 红色
};

// ==================== 每日运势分析算法 ====================

// 导入文案库
import {
  CAREER_TEXTS,
  WEALTH_TEXTS,
  LOVE_TEXTS,
  HEALTH_TEXTS,
  STUDY_TEXTS,
  OVERALL_TEXTS,
  ZODIAC_TRAITS_MAP,
  ELEMENT_CAREER_MAP,
  ELEMENT_INVESTMENT_MAP,
  MEIHUA_INTERPRETATIONS,
  DAYAN_FORTUNE_DETAILS,
  CHANGING_LINE_ADVICE,
} from './fortuneTexts.js';

// 十二生肖数据 - 支持任意年份计算
const CHINESE_ZODIAC = [
  {name: '鼠', index: 0, element: '水', luckyNumbers: [2, 3], luckyColors: ['蓝色', '金色', '绿色'], traits: ['机智', '灵活', '适应力强']},
  {name: '牛', index: 1, element: '土', luckyNumbers: [1, 9], luckyColors: ['黄色', '橙色', '红色'], traits: ['勤劳', '踏实', '责任心强']},
  {name: '虎', index: 2, element: '木', luckyNumbers: [1, 3, 4], luckyColors: ['蓝色', '灰色', '橙色'], traits: ['勇敢', '自信', '领导力强']},
  {name: '兔', index: 3, element: '木', luckyNumbers: [3, 4, 6], luckyColors: ['红色', '粉色', '紫色'], traits: ['温和', '谨慎', '艺术天赋']},
  {name: '龙', index: 4, element: '土', luckyNumbers: [1, 6, 7], luckyColors: ['金色', '银色', '灰色'], traits: ['威严', '创新', '理想主义']},
  {name: '蛇', index: 5, element: '火', luckyNumbers: [2, 8, 9], luckyColors: ['黑色', '红色', '黄色'], traits: ['智慧', '神秘', '直觉敏锐']},
  {name: '马', index: 6, element: '火', luckyNumbers: [2, 3, 7], luckyColors: ['黄色', '绿色', '紫色'], traits: ['热情', '自由', '行动力强']},
  {name: '羊', index: 7, element: '土', luckyNumbers: [3, 9, 4], luckyColors: ['绿色', '红色', '紫色'], traits: ['温柔', '善良', '创造力强']},
  {name: '猴', index: 8, element: '金', luckyNumbers: [1, 8, 7], luckyColors: ['白色', '金色', '蓝色'], traits: ['聪明', '活泼', '多才多艺']},
  {name: '鸡', index: 9, element: '金', luckyNumbers: [5, 7, 8], luckyColors: ['金色', '棕色', '黄色'], traits: ['勤奋', '准时', '注重细节']},
  {name: '狗', index: 10, element: '土', luckyNumbers: [3, 4, 9], luckyColors: ['绿色', '红色', '紫色'], traits: ['忠诚', '正义', '保护欲强']},
  {name: '猪', index: 11, element: '水', luckyNumbers: [2, 5, 8], luckyColors: ['黄色', '灰色', '棕色'], traits: ['善良', '慷慨', '享受生活']},
];

// 六十甲子纳音五行表（更精确的五行计算）
const JIAZI_NAYIN = {
  0: '海中金', 1: '海中金', 2: '炉中火', 3: '炉中火', 4: '大林木', 5: '大林木',
  6: '路旁土', 7: '路旁土', 8: '剑锋金', 9: '剑锋金', 10: '山头火', 11: '山头火',
  12: '涧下水', 13: '涧下水', 14: '城头土', 15: '城头土', 16: '白蜡金', 17: '白蜡金',
  18: '杨柳木', 19: '杨柳木', 20: '井泉水', 21: '井泉水', 22: '屋上土', 23: '屋上土',
  24: '霹雳火', 25: '霹雳火', 26: '松柏木', 27: '松柏木', 28: '长流水', 29: '长流水',
  30: '沙中金', 31: '沙中金', 32: '山下火', 33: '山下火', 34: '平地木', 35: '平地木',
  36: '壁上土', 37: '壁上土', 38: '金箔金', 39: '金箔金', 40: '覆灯火', 41: '覆灯火',
  42: '天河水', 43: '天河水', 44: '大驿土', 45: '大驿土', 46: '钗钏金', 47: '钗钏金',
  48: '桑柘木', 49: '桑柘木', 50: '大溪水', 51: '大溪水', 52: '沙中土', 53: '沙中土',
  54: '天上火', 55: '天上火', 56: '石榴木', 57: '石榴木', 58: '大海水', 59: '大海水',
};

// 八卦数据
const BAGUA = [
  {name: '乾', number: 1, element: '金', direction: '西北', meaning: '天', traits: ['刚健', '领导', '创造']},
  {name: '兑', number: 2, element: '金', direction: '西', meaning: '泽', traits: ['喜悦', '沟通', '收获']},
  {name: '离', number: 3, element: '火', direction: '南', meaning: '火', traits: ['光明', '智慧', '美丽']},
  {name: '震', number: 4, element: '木', direction: '东', meaning: '雷', traits: ['震动', '奋发', '新生']},
  {name: '巽', number: 5, element: '木', direction: '东南', meaning: '风', traits: ['柔顺', '渗透', '传播']},
  {name: '坎', number: 6, element: '水', direction: '北', meaning: '水', traits: ['智慧', '流动', '险阻']},
  {name: '艮', number: 7, element: '土', direction: '东北', meaning: '山', traits: ['稳重', '止静', '积累']},
  {name: '坤', number: 8, element: '土', direction: '西南', meaning: '地', traits: ['包容', '承载', '滋养']},
];

// 十二星座数据
const CONSTELLATIONS = [
  {name: '水瓶座', dateRange: {start: '01-20', end: '02-18'}, element: '风', luckyNumbers: [4, 13], luckyColors: ['蓝色', '银色']},
  {name: '双鱼座', dateRange: {start: '02-19', end: '03-20'}, element: '水', luckyNumbers: [7, 16], luckyColors: ['海蓝色', '紫色']},
  {name: '白羊座', dateRange: {start: '03-21', end: '04-19'}, element: '火', luckyNumbers: [6, 7], luckyColors: ['红色', '橙色']},
  {name: '金牛座', dateRange: {start: '04-20', end: '05-20'}, element: '土', luckyNumbers: [1, 9], luckyColors: ['绿色', '粉色']},
  {name: '双子座', dateRange: {start: '05-21', end: '06-20'}, element: '风', luckyNumbers: [3, 5], luckyColors: ['黄色', '橙色']},
  {name: '巨蟹座', dateRange: {start: '06-21', end: '07-22'}, element: '水', luckyNumbers: [2, 7], luckyColors: ['白色', '银色']},
  {name: '狮子座', dateRange: {start: '07-23', end: '08-22'}, element: '火', luckyNumbers: [1, 3], luckyColors: ['金色', '橙色']},
  {name: '处女座', dateRange: {start: '08-23', end: '09-22'}, element: '土', luckyNumbers: [3, 27], luckyColors: ['灰色', '米色']},
  {name: '天秤座', dateRange: {start: '09-23', end: '10-22'}, element: '风', luckyNumbers: [6, 15], luckyColors: ['蓝色', '绿色']},
  {name: '天蝎座', dateRange: {start: '10-23', end: '11-21'}, element: '水', luckyNumbers: [4, 13], luckyColors: ['深红色', '黑色']},
  {name: '射手座', dateRange: {start: '11-22', end: '12-21'}, element: '火', luckyNumbers: [9, 18], luckyColors: ['紫色', '蓝色']},
  {name: '摩羯座', dateRange: {start: '12-22', end: '01-19'}, element: '土', luckyNumbers: [8, 10], luckyColors: ['棕色', '黑色']},
];

// 五行相生相克关系
const WUXING_RELATIONS = {
  '金': {generates: '水', destroys: '木', generatedBy: '土', destroyedBy: '火'},
  '木': {generates: '火', destroys: '土', generatedBy: '水', destroyedBy: '金'},
  '水': {generates: '木', destroys: '火', generatedBy: '金', destroyedBy: '土'},
  '火': {generates: '土', destroys: '金', generatedBy: '木', destroyedBy: '水'},
  '土': {generates: '金', destroys: '水', generatedBy: '火', destroyedBy: '木'},
};

// 计算生肖 - 支持任意年份
export const calculateChineseZodiac = (birthYear) => {
  // 1900年是鼠年，以此为基准计算
  const baseYear = 1900; // 鼠年
  const yearDiff = (birthYear - baseYear) % 12;
  const adjustedIndex = yearDiff < 0 ? yearDiff + 12 : yearDiff;
  return CHINESE_ZODIAC[adjustedIndex];
};

// 计算六十甲子纳音五行
export const calculateNayinWuxing = (birthYear) => {
  const jiazi60 = (birthYear - 1864) % 60; // 1864年是甲子年
  const adjustedIndex = jiazi60 < 0 ? jiazi60 + 60 : jiazi60;
  return JIAZI_NAYIN[adjustedIndex];
};

// 梅花易数算法 - 基于时间起卦
export const calculateMeihuaYishu = (year, month, day, hour = 12) => {
  // 上卦：年月日之和除以8取余
  const upperGua = (year + month + day) % 8;

  // 下卦：年月日时之和除以8取余
  const lowerGua = (year + month + day + hour) % 8;

  // 动爻：年月日时之和除以6取余
  const changingLine = (year + month + day + hour) % 6;

  // 获取卦象
  const upperGuaInfo = BAGUA[upperGua];
  const lowerGuaInfo = BAGUA[lowerGua];

  // 计算变卦
  const changedUpperGua = changingLine < 3 ? (upperGua + 1) % 8 : upperGua;
  const changedLowerGua = changingLine >= 3 ? (lowerGua + 1) % 8 : lowerGua;

  return {
    originalGua: {
      upper: upperGuaInfo,
      lower: lowerGuaInfo,
      changingLine: changingLine + 1, // 爻位从1开始
    },
    changedGua: {
      upper: BAGUA[changedUpperGua],
      lower: BAGUA[changedLowerGua],
    },
    interpretation: interpretMeihuaGua(upperGuaInfo, lowerGuaInfo, changingLine),
  };
};

// 大衍之数算法 - 简化版
export const calculateDayanZhishu = (birthInfo, currentDate) => {
  const { year, month = 1, day = 1 } = birthInfo;
  const today = new Date(currentDate);

  // 计算天数差
  const birthDate = new Date(year, month - 1, day);
  const daysDiff = Math.floor((today - birthDate) / (1000 * 60 * 60 * 24));

  // 大衍之数为50，用49进行演算
  const dayanNumber = 49;

  // 第一变：分二
  const firstDivision = Math.floor(dayanNumber / 2);
  const remainder1 = dayanNumber % 2;

  // 第二变：挂一
  const secondStep = firstDivision - 1;

  // 第三变：揲四
  const thirdStep = secondStep % 4;
  const finalNumber = thirdStep === 0 ? 4 : thirdStep;

  // 结合出生信息和当前日期
  const personalFactor = (year + month + day + today.getDate()) % 8;
  const timeFactor = (today.getMonth() + 1 + today.getDate()) % 6;

  return {
    dayanResult: finalNumber,
    personalGua: BAGUA[personalFactor],
    timeGua: BAGUA[timeFactor % 8],
    fortuneLevel: calculateFortuneLevel(finalNumber, personalFactor, timeFactor),
    daysDiff,
  };
};

// 梅花易数卦象解释 - 使用文案库
const interpretMeihuaGua = (upperGua, lowerGua, changingLine) => {
  const guaName = upperGua.name + lowerGua.name;

  // 从文案库获取卦象解释
  const guaInterpretation = MEIHUA_INTERPRETATIONS[guaName] || {
    main: '卦象变化，需要综合分析。宜保持平常心，顺应自然。',
    advice: '宜：保持平常心、顺应自然。忌：急躁冒进、强求结果。',
  };

  // 从文案库获取动爻建议
  const lineAdvice = CHANGING_LINE_ADVICE[changingLine + 1] || {
    name: '动爻',
    advice: '需要根据具体情况灵活应对。',
    focus: '灵活应变',
  };

  return {
    mainInterpretation: guaInterpretation.main,
    changingLineAdvice: lineAdvice.advice,
    guaAdvice: guaInterpretation.advice,
    elements: {
      upper: upperGua.element,
      lower: lowerGua.element,
      relationship: getElementRelationship(upperGua.element, lowerGua.element),
    },
    lineDetails: {
      name: lineAdvice.name,
      meaning: lineAdvice.meaning,
      focus: lineAdvice.focus,
    },
  };
};

// 计算运势等级 - 使用文案库
const calculateFortuneLevel = (dayanResult, personalFactor, timeFactor) => {
  const score = (dayanResult * 10 + personalFactor * 5 + timeFactor * 3) % 100;

  let level = '平';
  if (score >= 80) {level = '大吉';}
  else if (score >= 65) {level = '中吉';}
  else if (score >= 50) {level = '小吉';}
  else if (score >= 35) {level = '平';}
  else if (score >= 20) {level = '小凶';}
  else {level = '凶';}

  // 从文案库获取详细信息
  const fortuneDetails = DAYAN_FORTUNE_DETAILS[level] || {
    description: '运势一般，保持平常心',
    advice: '顺其自然，保持积极心态',
    suitable: ['日常事务'],
    avoid: ['冒险行为'],
  };

  return {
    level,
    description: fortuneDetails.description,
    advice: fortuneDetails.advice,
    suitable: fortuneDetails.suitable,
    avoid: fortuneDetails.avoid,
    score,
  };
};

// 计算五行元素关系
const getElementRelationship = (element1, element2) => {
  if (element1 === element2) {return '同气相求';}

  const relations = WUXING_RELATIONS[element1];
  if (relations.generates === element2) {return '相生有利';}
  if (relations.generatedBy === element2) {return '得到助力';}
  if (relations.destroys === element2) {return '需要克制';}
  if (relations.destroyedBy === element2) {return '受到压制';}

  return '关系平和';
};

// 从纳音中提取五行元素
const extractElementFromNayin = (nayinWuxing) => {
  if (nayinWuxing.includes('金')) {return '金';}
  if (nayinWuxing.includes('木')) {return '木';}
  if (nayinWuxing.includes('水')) {return '水';}
  if (nayinWuxing.includes('火')) {return '火';}
  if (nayinWuxing.includes('土')) {return '土';}
  return '土'; // 默认
};

// 高级运势分数计算
const calculateAdvancedFortuneScores = (zodiac, constellation, wuxingScore, meihuaResult, dayanResult, date) => {
  const baseScore = 60;
  const dateBonus = (date.getDate() % 10) * 2;
  const monthBonus = ((date.getMonth() + 1) % 4) * 3;

  // 梅花易数影响
  const meihuaBonus = meihuaResult.originalGua.changingLine * 2;

  // 大衍之数影响
  const dayanBonus = dayanResult.fortuneLevel.level === '大吉' ? 20 :
                    dayanResult.fortuneLevel.level === '中吉' ? 15 :
                    dayanResult.fortuneLevel.level === '小吉' ? 10 : 0;

  // 生肖运势分数
  const zodiacScore = Math.min(95, baseScore + wuxingScore - 60 + dateBonus + meihuaBonus);

  // 星座各维度分数
  let loveScore = baseScore + monthBonus + (meihuaResult.interpretation.elements.relationship === '相生有利' ? 10 : 0);
  let careerScore = baseScore + dateBonus + dayanBonus;
  let wealthScore = baseScore + (wuxingScore - 60) + meihuaBonus;
  let healthScore = baseScore + ((date.getDay() % 3) * 5) + (dayanResult.dayanResult * 2);

  const normalizeScore = (score) => Math.max(30, Math.min(95, score));
  const overall = Math.round((zodiacScore + loveScore + careerScore + wealthScore + healthScore) / 5);

  return {
    zodiac: normalizeScore(zodiacScore),
    love: normalizeScore(loveScore),
    career: normalizeScore(careerScore),
    wealth: normalizeScore(wealthScore),
    health: normalizeScore(healthScore),
    overall: normalizeScore(overall),
  };
};

// 高级幸运数字生成
const generateAdvancedLuckyNumbers = (zodiac, constellation, meihuaResult, date) => {
  const baseNumbers = [...zodiac.luckyNumbers, ...constellation.luckyNumbers];
  const dateNumber = date.getDate();
  const monthNumber = date.getMonth() + 1;

  // 梅花易数数字
  const meihuaNumbers = [
    meihuaResult.originalGua.upper.number,
    meihuaResult.originalGua.lower.number,
    meihuaResult.originalGua.changingLine,
  ];

  const personalNumbers = [
    (dateNumber + monthNumber) % 10,
    (dateNumber * monthNumber) % 10,
  ];

  const allNumbers = [...new Set([...baseNumbers, ...meihuaNumbers, ...personalNumbers])];
  return allNumbers.slice(0, 8).sort((a, b) => a - b);
};

// 高级运势建议生成 - 使用文案库
const generateAdvancedFortuneAdvice = (fortuneScores, zodiac, constellation, meihuaResult, dayanResult) => {
  const suggestions = [];

  // 基于大衍之数的建议
  suggestions.push({
    type: '大衍之数',
    level: dayanResult.fortuneLevel.level,
    text: dayanResult.fortuneLevel.description,
  });

  // 基于梅花易数的建议
  suggestions.push({
    type: '梅花易数',
    level: 'normal',
    text: meihuaResult.interpretation.mainInterpretation,
  });

  // 基于动爻的建议
  suggestions.push({
    type: '爻象指引',
    level: 'normal',
    text: meihuaResult.interpretation.changingLineAdvice,
  });

  // 基于生肖特质的建议 - 使用文案库
  const zodiacTraits = ZODIAC_TRAITS_MAP[zodiac.name] || zodiac.traits;
  if (zodiacTraits && zodiacTraits.length > 0) {
    const trait = zodiacTraits[Math.floor(Math.random() * zodiacTraits.length)];
    suggestions.push({
      type: '生肖特质',
      level: 'good',
      text: `发挥你${trait}的特质，将有助于今日运势的提升。`,
    });
  }

  return suggestions.slice(0, 4);
};

// 生成事业运势分析 - 使用文案库
export const generateCareerAnalysis = (fortuneData) => {
  const score = fortuneData.constellation?.scores.career || fortuneData.overall;
  const element = fortuneData.wuxing.userElement;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  // 从文案库获取对应的分析文本
  const texts = CAREER_TEXTS[scoreLevel][element] || CAREER_TEXTS[scoreLevel]['金'];
  const randomIndex = Math.floor(Math.random() * texts.length);

  return texts[randomIndex];
};

// 生成财运分析 - 使用文案库
export const generateWealthAnalysis = (fortuneData) => {
  const score = fortuneData.constellation?.scores.wealth || fortuneData.overall;
  const element = fortuneData.wuxing.userElement;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  const texts = WEALTH_TEXTS[scoreLevel][element] || WEALTH_TEXTS[scoreLevel]['金'];
  const randomIndex = Math.floor(Math.random() * texts.length);

  return texts[randomIndex];
};

// 生成感情分析 - 使用文案库
export const generateLoveAnalysis = (fortuneData) => {
  const score = fortuneData.constellation?.scores.love || fortuneData.overall;
  const zodiacTraits = ZODIAC_TRAITS_MAP[fortuneData.zodiac.name] || fortuneData.zodiac.traits;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  if (scoreLevel === 'high' && zodiacTraits && zodiacTraits.length > 0) {
    // 高分时根据性格特质选择文案
    const trait = zodiacTraits[Math.floor(Math.random() * zodiacTraits.length)];
    const traitTexts = LOVE_TEXTS[scoreLevel][trait];
    if (traitTexts && traitTexts.length > 0) {
      return traitTexts[Math.floor(Math.random() * traitTexts.length)];
    }
  }

  // 其他情况使用通用文案
  const texts = LOVE_TEXTS[scoreLevel] || LOVE_TEXTS.medium;
  const randomIndex = Math.floor(Math.random() * texts.length);

  return texts[randomIndex];
};

// 生成健康分析 - 使用文案库
export const generateHealthAnalysis = (fortuneData) => {
  const score = fortuneData.constellation?.scores.health || fortuneData.overall;
  const element = fortuneData.wuxing.userElement;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  if (scoreLevel === 'high') {
    const texts = HEALTH_TEXTS[scoreLevel][element] || HEALTH_TEXTS[scoreLevel]['金'];
    const randomIndex = Math.floor(Math.random() * texts.length);
    return texts[randomIndex];
  } else {
    // 中低分使用通用文案
    const texts = HEALTH_TEXTS[scoreLevel] || HEALTH_TEXTS.medium;
    const randomIndex = Math.floor(Math.random() * texts.length);
    return texts[randomIndex];
  }
};

// 生成学业分析 - 使用文案库
export const generateStudyAnalysis = (fortuneData) => {
  const score = fortuneData.overall;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  const texts = STUDY_TEXTS[scoreLevel] || STUDY_TEXTS.medium;
  const randomIndex = Math.floor(Math.random() * texts.length);

  return texts[randomIndex];
};

// 生成综合分析 - 使用文案库
export const generateOverallAnalysis = (fortuneData) => {
  const score = fortuneData.overall;

  let scoreLevel = 'low';
  if (score >= 80) {scoreLevel = 'high';}
  else if (score >= 60) {scoreLevel = 'medium';}

  const texts = OVERALL_TEXTS[scoreLevel] || OVERALL_TEXTS.medium;
  const randomIndex = Math.floor(Math.random() * texts.length);

  return texts[randomIndex];
};

// 计算星座
export const calculateConstellation = (birthMonth, birthDay) => {
  const dateStr = `${birthMonth.toString().padStart(2, '0')}-${birthDay.toString().padStart(2, '0')}`;

  for (const constellation of CONSTELLATIONS) {
    const {start, end} = constellation.dateRange;

    // 处理跨年的摩羯座
    if (constellation.name === '摩羯座') {
      if (dateStr >= start || dateStr <= end) {
        return constellation;
      }
    } else {
      if (dateStr >= start && dateStr <= end) {
        return constellation;
      }
    }
  }

  return CONSTELLATIONS[0]; // 默认返回水瓶座
};

// 计算五行属性（简化版，基于出生年份）
export const calculateWuxing = (birthYear) => {
  const zodiac = calculateChineseZodiac(birthYear);
  return zodiac.element;
};

// 计算当日五行
const calculateDayWuxing = (date) => {
  const elements = ['金', '木', '水', '火', '土'];
  const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
  return elements[dayOfYear % 5];
};

// 计算五行关系得分
const calculateWuxingScore = (userElement, dayElement) => {
  if (userElement === dayElement) {
    return 85; // 同属性，较好
  }

  const relation = WUXING_RELATIONS[userElement];
  if (relation.generates === dayElement) {
    return 95; // 我生日，最佳
  } else if (relation.generatedBy === dayElement) {
    return 90; // 日生我，很好
  } else if (relation.destroys === dayElement) {
    return 60; // 我克日，一般
  } else if (relation.destroyedBy === dayElement) {
    return 40; // 日克我，较差
  }

  return 75; // 其他情况
};

// 运势模板数据
const FORTUNE_TEMPLATES = {
  zodiac: {
    excellent: ['今日运势极佳，万事如意', '贵人运旺盛，事业有成', '财运亨通，投资有利'],
    good: ['今日运势良好，诸事顺利', '工作进展顺利，人际和谐', '财运稳定，适合理财'],
    normal: ['今日运势平稳，保持平常心', '工作按部就班，避免冒险', '财运一般，量入为出'],
    poor: ['今日运势欠佳，谨慎行事', '工作可能遇到阻碍，需要耐心', '财运不佳，避免投资'],
    bad: ['今日运势较差，宜静不宜动', '工作压力较大，注意调节', '财运低迷，控制支出'],
  },
  constellation: {
    love: ['感情运势上升，单身有机会', '恋人关系甜蜜，感情稳定', '桃花运旺，魅力四射'],
    career: ['工作积极主动，表现出色', '事业运势良好，升职有望', '团队合作愉快，效率提升'],
    wealth: ['财运稳定上升，收入增加', '投资机会出现，把握时机', '理财有道，收益可观'],
    health: ['身体健康，精力充沛', '注意休息，劳逸结合', '适量运动，保持活力'],
  },
};

// 生成幸运数字
const generateLuckyNumbers = (zodiac, constellation, date) => {
  const baseNumbers = [...zodiac.luckyNumbers, ...constellation.luckyNumbers];
  const dateNumber = date.getDate();
  const monthNumber = date.getMonth() + 1;

  // 结合日期生成个性化幸运数字
  const personalNumbers = [
    (dateNumber + monthNumber) % 10,
    (dateNumber * monthNumber) % 10,
    (dateNumber + zodiac.luckyNumbers[0]) % 10,
  ];

  // 去重并排序
  const allNumbers = [...new Set([...baseNumbers, ...personalNumbers])];
  return allNumbers.slice(0, 6).sort((a, b) => a - b);
};

// 生成幸运颜色
const generateLuckyColors = (zodiac, constellation, wuxingElement) => {
  const colors = [...zodiac.luckyColors, ...constellation.luckyColors];

  // 根据五行添加对应颜色
  const wuxingColors = {
    '金': ['白色', '银色', '金色'],
    '木': ['绿色', '青色', '蓝色'],
    '水': ['黑色', '蓝色', '灰色'],
    '火': ['红色', '橙色', '紫色'],
    '土': ['黄色', '棕色', '米色'],
  };

  if (wuxingColors[wuxingElement]) {
    colors.push(...wuxingColors[wuxingElement]);
  }

  // 去重
  return [...new Set(colors)].slice(0, 4);
};

// 主要的每日运势计算函数 - 整合专业算法
export const calculateDailyFortune = (userBirthInfo, currentDate = new Date()) => {
  if (!userBirthInfo || !userBirthInfo.year) {
    return null;
  }

  const today = new Date(currentDate);

  // 1. 计算生肖（支持任意年份）
  const zodiac = calculateChineseZodiac(userBirthInfo.year);

  // 2. 计算星座（如果有月日信息）
  let constellation = null;
  if (userBirthInfo.month && userBirthInfo.day) {
    constellation = calculateConstellation(userBirthInfo.month, userBirthInfo.day);
  }

  // 3. 计算纳音五行（更精确）
  const nayinWuxing = calculateNayinWuxing(userBirthInfo.year);
  const wuxingElement = extractElementFromNayin(nayinWuxing);
  const dayWuxing = calculateDayWuxing(today);
  const wuxingScore = calculateWuxingScore(wuxingElement, dayWuxing);

  // 4. 梅花易数算法
  const meihuaResult = calculateMeihuaYishu(
    userBirthInfo.year,
    userBirthInfo.month || 1,
    userBirthInfo.day || 1,
    today.getHours()
  );

  // 5. 大衍之数算法
  const dayanResult = calculateDayanZhishu(userBirthInfo, today);

  // 6. 综合计算运势分数
  const fortuneScores = calculateAdvancedFortuneScores(
    zodiac,
    constellation,
    wuxingScore,
    meihuaResult,
    dayanResult,
    today
  );

  // 7. 生成幸运元素
  const luckyNumbers = constellation ?
    generateAdvancedLuckyNumbers(zodiac, constellation, meihuaResult, today) :
    [...zodiac.luckyNumbers, ...meihuaResult.originalGua.upper.traits.map((_, i) => i + 1)];

  const luckyColors = constellation ?
    generateLuckyColors(zodiac, constellation, wuxingElement) :
    zodiac.luckyColors;

  // 8. 生成专业运势建议
  const suggestions = generateAdvancedFortuneAdvice(
    fortuneScores,
    zodiac,
    constellation,
    meihuaResult,
    dayanResult
  );

  return {
    date: today.toISOString().split('T')[0],
    zodiac: {
      name: zodiac.name,
      element: zodiac.element,
      traits: zodiac.traits,
      score: fortuneScores.zodiac,
    },
    constellation: constellation ? {
      name: constellation.name,
      element: constellation.element,
      scores: {
        love: fortuneScores.love,
        career: fortuneScores.career,
        wealth: fortuneScores.wealth,
        health: fortuneScores.health,
      },
    } : null,
    wuxing: {
      userElement: wuxingElement,
      nayinWuxing: nayinWuxing,
      dayElement: dayWuxing,
      score: wuxingScore,
      relation: getWuxingRelationText(wuxingElement, dayWuxing),
    },
    meihua: {
      originalGua: meihuaResult.originalGua,
      changedGua: meihuaResult.changedGua,
      interpretation: meihuaResult.interpretation,
    },
    dayan: {
      fortuneLevel: dayanResult.fortuneLevel,
      personalGua: dayanResult.personalGua,
      timeGua: dayanResult.timeGua,
    },
    overall: fortuneScores.overall,
    luckyNumbers,
    luckyColors,
    suggestions,
  };
};

// 计算各维度运势分数
const calculateFortuneScores = (zodiac, constellation, wuxingScore, date) => {
  const baseScore = 60;
  const dateBonus = (date.getDate() % 10) * 2; // 日期影响
  const monthBonus = ((date.getMonth() + 1) % 4) * 3; // 月份影响

  // 生肖运势分数
  const zodiacScore = Math.min(95, baseScore + wuxingScore - 60 + dateBonus);

  // 星座各维度分数（如果有星座信息）
  let loveScore = baseScore + monthBonus;
  let careerScore = baseScore + dateBonus;
  let wealthScore = baseScore + (wuxingScore - 60);
  let healthScore = baseScore + ((date.getDay() % 3) * 5);

  // 确保分数在合理范围内
  const normalizeScore = (score) => Math.max(30, Math.min(95, score));

  const overall = Math.round((zodiacScore + loveScore + careerScore + wealthScore + healthScore) / 5);

  return {
    zodiac: normalizeScore(zodiacScore),
    love: normalizeScore(loveScore),
    career: normalizeScore(careerScore),
    wealth: normalizeScore(wealthScore),
    health: normalizeScore(healthScore),
    overall: normalizeScore(overall),
  };
};

// 获取五行关系描述
const getWuxingRelationText = (userElement, dayElement) => {
  if (userElement === dayElement) {
    return '同气相求，运势平稳';
  }

  const relation = WUXING_RELATIONS[userElement];
  if (relation.generates === dayElement) {
    return '我生日元，运势旺盛';
  } else if (relation.generatedBy === dayElement) {
    return '日生我身，贵人相助';
  } else if (relation.destroys === dayElement) {
    return '我克日元，需要谨慎';
  } else if (relation.destroyedBy === dayElement) {
    return '日克我身，宜守不宜攻';
  }

  return '五行平和，运势一般';
};

// 生成运势建议
const generateFortuneAdvice = (fortuneScores, zodiac, constellation) => {
  const suggestions = [];

  // 基于生肖运势的建议
  if (fortuneScores.zodiac >= 80) {
    suggestions.push({
      type: '生肖运势',
      level: 'excellent',
      text: FORTUNE_TEMPLATES.zodiac.excellent[Math.floor(Math.random() * FORTUNE_TEMPLATES.zodiac.excellent.length)],
    });
  } else if (fortuneScores.zodiac >= 60) {
    suggestions.push({
      type: '生肖运势',
      level: 'good',
      text: FORTUNE_TEMPLATES.zodiac.good[Math.floor(Math.random() * FORTUNE_TEMPLATES.zodiac.good.length)],
    });
  } else {
    suggestions.push({
      type: '生肖运势',
      level: 'normal',
      text: FORTUNE_TEMPLATES.zodiac.normal[Math.floor(Math.random() * FORTUNE_TEMPLATES.zodiac.normal.length)],
    });
  }

  // 基于星座运势的建议（如果有星座信息）
  if (constellation) {
    if (fortuneScores.wealth >= 70) {
      suggestions.push({
        type: '财运建议',
        level: 'good',
        text: FORTUNE_TEMPLATES.constellation.wealth[Math.floor(Math.random() * FORTUNE_TEMPLATES.constellation.wealth.length)],
      });
    }

    if (fortuneScores.career >= 70) {
      suggestions.push({
        type: '事业建议',
        level: 'good',
        text: FORTUNE_TEMPLATES.constellation.career[Math.floor(Math.random() * FORTUNE_TEMPLATES.constellation.career.length)],
      });
    }
  }

  // 通用建议
  suggestions.push({
    type: '今日宜忌',
    level: 'normal',
    text: fortuneScores.overall >= 70 ? '今日宜：投资理财、重要决策、社交活动' : '今日忌：重大投资、冲动消费、争执冲突',
  });

  return suggestions.slice(0, 3); // 最多返回3条建议
};
