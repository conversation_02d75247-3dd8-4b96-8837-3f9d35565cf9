// colors.js
export const COLORS = {
  // 主色系
  primary: '#4E7D96', // 深青色/蓝绿色
  secondary: '#f4f6fb',

  // 背景色
  background: {
    light: '#E3EDF2', // 浅蓝色/浅灰色
    white: '#FFFFFF',
    dark: '#0A0D25', // 深蓝色/近黑色
    lightGray: '#e8edf4',
  },

  // 文本颜色
  text: {
    primary: '#0A0D25', // 深蓝色/近黑色
    secondary: '#4E7D96', // 深青色
    light: '#FFFFFF',
    gray: '#6B7280',
    lightGray: '#9CA3AF',
  },

  // 功能色
  functional: {
    success: '#00E676', // 绿色
    warning: '#FFAB40', // 黄色
    error: '#FF5252', // 红色
    info: '#40C4FF', // 蓝色
  },

  // 收支
  income: '#00E676', // 绿色
  expense: '#FF5252', // 红色

  // 边框和分隔线
  border: {
    light: '#E3EDF2', // 浅蓝色/浅灰色
    medium: '#D1D5DB',
  },

  // 渐变色
  gradient: {
    primary: ['#4E7D96', '#3A6075'], // 深青色渐变
    secondary: ['#FF844B', '#FF6B3D'], // 橙色渐变
    background: ['#E3EDF2', '#D6E4ED'], // 浅蓝色渐变
  },

  // 阴影
  shadow: {
    color: 'rgba(10, 13, 37, 0.1)', // 基于深蓝色的透明阴影
  },
};

// 主题配置
export const THEME = {
  // 圆角大小
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    extraLarge: 16,
    circle: 9999,
  },

  // 间距
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },

  // 字体大小
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
  },

  // 字体粗细
  fontWeight: {
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

// 辅助函数
export const hexToRgba = (hex, alpha = 1) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};
