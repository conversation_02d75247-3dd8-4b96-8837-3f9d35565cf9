// utils/permission.ts
import {PermissionsAndroid, Platform, Alert} from 'react-native';
import RNFS from 'react-native-fs';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {saveDocuments, pick} from '@react-native-documents/picker';
import Share from 'react-native-share'; // 确保使用的是 react-native-share 库
import dayjs from 'dayjs';

/**
 * 请求存储权限（适配 Android 版本）
 */
export const requestStoragePermission = async () => {
  if (Platform.OS === 'android') {
    if (Platform.Version >= 33) {
      // Android 13+ 使用新权限
      const status = await request(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES);
      return status === RESULTS.GRANTED;
    } else {
      // Android <13 使用传统权限
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: '存储权限请求',
          message: '应用需要存储权限以保存文件',
          buttonPositive: '同意',
        },
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
  }
  return true; // iOS 或其他平台暂不处理
};

/**
 * 保存 CSV 文件到 Downloads 目录
 * @param csvContent CSV 字符串
 * @param fileName 文件名（无需后缀）
 */
export const saveCSVFile = async (csvContent: string, fileName: string) => {
  try {
    // 1. 检查权限
    const hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      return;
    }

    // 2. 生成文件路径
    const downloadsPath = RNFS.DocumentDirectoryPath;
    console.log('000-downloadsPath', downloadsPath);
    const filePath = `${downloadsPath}/${fileName}_${dayjs().format(
      'YYYY-MM-DD',
    )}-${new Date().getTime()}.csv`;
    // 3. 写入文件
    await RNFS.writeFile(filePath, csvContent, 'utf8');
    const [{uri: targetUri}] = await saveDocuments({
      sourceUris: [
        `content://com.android.providers.downloads.documents/document/${filePath}`,
      ],
      mimeType: 'text/csv',
      copy: false,
      fileName: fileName,
    });

    // 4. 提示用户
    Alert.alert('保存成功', `文件已保存至：${targetUri}`, [
      {text: '确定', onPress: () => console.log('OK Pressed')},
    ]);
    return targetUri;
  } catch (error) {
    Alert.alert('保存失败', error.message);
    throw error;
  }
};

// 分享CSV文件（专门用于账单数据）
export const shareFile = async (content, fileName) => {
  try {
    // 文件名
    const downloadsPath = RNFS.DownloadDirectoryPath;
    const filePath = `${downloadsPath}/${fileName}_${dayjs().format(
      'YYYY-MM-DD',
    )}-${new Date().getTime()}.csv`;
    // 3. 写入文件
    await RNFS.writeFile(filePath, content, 'utf8');
    await Share.open({
      url: `file://${filePath}`,
      type: 'text/csv',
      filename: fileName,
      failOnCancel: false,
    });
  } catch (error) {
    Alert.alert('分享失败', error.message);
    throw error;
  }
};

// 分享JSON备份文件（专门用于其他数据备份）
export const shareBackupFile = async (content, fileName) => {
  try {
    // 确保文件名包含时间戳（如果还没有的话）
    const finalFileName = fileName.includes('_')
      ? fileName
      : `${fileName}_${dayjs().format('YYYY-MM-DD')}-${new Date().getTime()}`;

    // 确保文件扩展名是 .json
    const jsonFileName = finalFileName.endsWith('.json')
      ? finalFileName
      : `${finalFileName}.json`;

    // 文件路径
    const downloadsPath = RNFS.DownloadDirectoryPath;
    const filePath = `${downloadsPath}/${jsonFileName}`;

    // 写入JSON文件
    await RNFS.writeFile(filePath, content, 'utf8');

    // 分享文件
    await Share.open({
      url: `file://${filePath}`,
      type: 'application/json',
      filename: jsonFileName,
      failOnCancel: false,
    });
  } catch (error) {
    Alert.alert('备份分享失败', error.message);
    throw error;
  }
};

// 写入CSV文件
export const writeFile = async (content, fileName) => {
  try {
    // 文件名
    const downloadsPath = RNFS.DownloadDirectoryPath;
    const filePath = `${downloadsPath}/${fileName}_${dayjs().format(
      'YYYY-MM-DD',
    )}-${new Date().getTime()}.csv`;
    // 3. 写入文件
    await RNFS.writeFile(filePath, content, 'utf8');
  } catch (error) {
    Alert.alert('分享失败', error.message);
    throw error;
  }
};

// 获取文件（专门用于账单数据导入）
export const getFileFromDocuments = async (source = 'snowball') => {
  try {
    const [pickResult] = await pick();
    const {uri, name} = pickResult;

    // 严格的文件类型验证
    if (source === 'miaomiao') {
      if (!name.includes('喵喵记账')) {
        return {
          error: '请选择喵喵记账导出的文件',
        };
      }
      if (!name.endsWith('.csv')) {
        return {
          error: '请选择正确格式文件',
        };
      }
    } else {
      if (!name.includes('雪球记账')) {
        return {
          error: '请选择雪球记账导出的CSV文件',
        };
      }
      // 雪球记账账单数据只支持CSV格式
      if (!name.endsWith('.csv')) {
        return {
          error: '请选择CSV格式的账单文件，JSON文件请使用"数据恢复"功能',
        };
      }
    }

    // 读取文件内容
    const fileContent = await RNFS.readFile(uri, 'utf8');

    // 根据数据源类型处理数据
    if (source === 'miaomiao') {
      // 处理喵喵记账数据
      return parseMiaomiaoData(fileContent);
    } else {
      // 处理雪球记账数据（默认）
      return parseSnowballData(fileContent);
    }
  } catch (err) {
    console.log('----err', err);
    throw err;
  }
};

// 专门用于选择和读取其他数据备份文件的函数
export const getBackupFileFromDocuments = async () => {
  try {
    const [pickResult] = await pick();
    const {uri, name} = pickResult;

    // 检查文件类型
    if (!name.endsWith('.json')) {
      return {
        error: '请选择JSON格式的备份文件',
      };
    }

    // 检查是否为其他数据备份文件
    if (!name.includes('其他数据备份') && !name.includes('雪球记账')) {
      return {
        error: '请选择雪球记账的其他数据备份文件',
      };
    }

    // 读取文件内容
    const fileContent = await RNFS.readFile(uri, 'utf8');

    try {
      const backupData = JSON.parse(fileContent);

      // 验证备份文件格式
      if (!backupData.data || !backupData.version) {
        return {
          error: '备份文件格式不正确，缺少必要的数据结构',
        };
      }

      // 检查是否为其他数据备份
      if (backupData.dataType !== 'other') {
        return {
          error: '请选择其他数据备份文件，账单数据请使用"导入账单"功能',
        };
      }

      // 验证数据内容
      const {data} = backupData;
      if (
        !data.creditCards &&
        !data.installmentPlans &&
        !data.loanRecords &&
        !data.products
      ) {
        return {
          error: '备份文件中没有找到支持的数据类型',
        };
      }

      return backupData;
    } catch (parseError) {
      return {
        error: '备份文件格式错误或已损坏，无法解析JSON内容',
      };
    }
  } catch (err) {
    console.log('----选择备份文件失败', err);
    throw err;
  }
};

// 解析喵喵记账数据
const parseMiaomiaoData = fileContent => {
  try {
    // 将表格数据转换为JSON
    const rows = fileContent.split('\n');
    const headers = rows[0].split(','); // 使用制表符分隔
    const jsonData = [];
    // 从第二行开始遍历(跳过表头)
    for (let i = 1; i < rows.length; i++) {
      if (!rows[i]) {
        continue;
      } // 跳过空行

      const values = rows[i].split(',');
      const entry = {};
      headers.forEach((item, index) => {
        let value = values[index] || '';
        entry[item.trim()] = value.trim();
      });
      jsonData.push(entry);
    }
    // 转换为交易记录格式
    const transactions = jsonData.map(item => {
      // 解析日期 (格式可能是 "2021-12-31 00:00:00")
      let date = '';
      const dateStr = item['时间'] || '';
      if (dateStr) {
        const dateParts = dateStr.split(' ')[0].split('-');
        if (dateParts.length === 3) {
          date = `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}`;
        }
      }

      // 处理金额和类型
      const amountStr = item['金额'] || '0';
      const amount = Math.abs(parseFloat(amountStr)).toString();
      const type = parseFloat(amountStr) < 0 ? 'expense' : 'income';

      // 确保有分类名称
      const category =
        item['分类'] || (type === 'expense' ? '其他支出' : '其他收入');

      return {
        date: date || new Date().toISOString().split('T')[0],
        type: type,
        amount: amount,
        category: category,
        categoryName: category, // 添加 categoryName 字段
        note: item['备注'] || '',
      };
    });
    console.log('----transactions', transactions);

    return {
      transactions: transactions,
    };
  } catch (error) {
    console.error('解析喵喵记账数据失败', error);
    throw new Error(`解析数据失败: ${error.message}`);
  }
};

// 解析雪球记账数据（保持现有逻辑）
const parseSnowballData = fileContent => {
  try {
    // 将CSV转换为JSON
    const rows = fileContent.split('\n');
    const headers = rows[0].split(',');
    const jsonData = [];

    // 从第二行开始遍历(跳过表头)
    for (let i = 1; i < rows.length; i++) {
      if (!rows[i]) {
        continue;
      } // 跳过空行

      const values = rows[i].split(',');
      const entry = {};

      headers.forEach((header, index) => {
        // 处理带引号的字段
        let value = values[index] || '';
        if (value.startsWith('"') && value.endsWith('"')) {
          value = value.slice(1, -1).replace(/""/g, '"');
        }
        entry[header.trim()] = value.trim();
      });

      jsonData.push(entry);
    }
    console.log('---jsonData', jsonData);
    // 转换数据格式
    const transactions = jsonData.map(item => {
      const type = item.类型 === '支出' ? 'expense' : 'income';
      const category =
        item.分类 || (type === 'expense' ? '其他支出' : '其他收入');
      const familyId = item['家庭账单id'] || '';
      const familyName = item['家庭账单'] || '';
      const accountBookId = item['账本id'] || '';
      const accountBookName = item['账本'] || '';
      return {
        date: item.日期 || '',
        type: type,
        amount: item.金额 ? String(item.金额).replace(/"/g, '') : '0',
        category: category,
        categoryName: category, // 添加 categoryName 字段
        note: item.备注 || '',
        familyId,
        familyName,
        accountBookId: accountBookId || 1,
        accountBookName: accountBookName || '默认账本', // 添加 accountBookName 字段
      };
    });

    return {
      transactions: transactions,
    };
  } catch (error) {
    console.error('解析雪球记账数据失败', error);
    throw new Error(`解析数据失败: ${error.message}`);
  }
};
