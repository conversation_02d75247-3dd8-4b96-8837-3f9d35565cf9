// 运势算法测试文件
import { calculateDailyFortune } from './algorithm.js';

// 测试函数
export const testFortuneCalculation = () => {
  console.log('🔮 开始测试运势算法...');
  
  // 测试用例1: 1990年出生的用户
  const testUser1 = {
    year: 1990,
    month: 6,
    day: 15
  };
  
  console.log('\n📅 测试用例1: 1990年6月15日出生');
  const result1 = calculateDailyFortune(testUser1);
  if (result1) {
    console.log('✅ 生肖:', result1.zodiac.name, '(' + result1.zodiac.element + ')');
    console.log('✅ 星座:', result1.constellation?.name || '未知');
    console.log('✅ 纳音五行:', result1.wuxing.nayinWuxing);
    console.log('✅ 综合运势:', result1.overall + '分');
    console.log('✅ 梅花易数:', result1.meihua.originalGua.upper.name + result1.meihua.originalGua.lower.name);
    console.log('✅ 大衍之数:', result1.dayan.fortuneLevel.level);
    console.log('✅ 幸运数字:', result1.luckyNumbers.join(', '));
    console.log('✅ 建议数量:', result1.suggestions.length);
  } else {
    console.log('❌ 测试失败: 无法计算运势');
  }
  
  // 测试用例2: 1960年之前出生的用户
  const testUser2 = {
    year: 1955,
    month: 3,
    day: 8
  };
  
  console.log('\n📅 测试用例2: 1955年3月8日出生 (测试年份扩展)');
  const result2 = calculateDailyFortune(testUser2);
  if (result2) {
    console.log('✅ 生肖:', result2.zodiac.name, '(' + result2.zodiac.element + ')');
    console.log('✅ 星座:', result2.constellation?.name || '未知');
    console.log('✅ 纳音五行:', result2.wuxing.nayinWuxing);
    console.log('✅ 综合运势:', result2.overall + '分');
    console.log('✅ 梅花易数:', result2.meihua.originalGua.upper.name + result2.meihua.originalGua.lower.name);
    console.log('✅ 大衍之数:', result2.dayan.fortuneLevel.level);
  } else {
    console.log('❌ 测试失败: 无法计算运势');
  }
  
  // 测试用例3: 2030年之后出生的用户
  const testUser3 = {
    year: 2035,
    month: 12,
    day: 25
  };
  
  console.log('\n📅 测试用例3: 2035年12月25日出生 (测试未来年份)');
  const result3 = calculateDailyFortune(testUser3);
  if (result3) {
    console.log('✅ 生肖:', result3.zodiac.name, '(' + result3.zodiac.element + ')');
    console.log('✅ 星座:', result3.constellation?.name || '未知');
    console.log('✅ 纳音五行:', result3.wuxing.nayinWuxing);
    console.log('✅ 综合运势:', result3.overall + '分');
    console.log('✅ 梅花易数:', result3.meihua.originalGua.upper.name + result3.meihua.originalGua.lower.name);
    console.log('✅ 大衍之数:', result3.dayan.fortuneLevel.level);
  } else {
    console.log('❌ 测试失败: 无法计算运势');
  }
  
  // 测试用例4: 只有年份信息的用户
  const testUser4 = {
    year: 1988
  };
  
  console.log('\n📅 测试用例4: 1988年出生 (只有年份)');
  const result4 = calculateDailyFortune(testUser4);
  if (result4) {
    console.log('✅ 生肖:', result4.zodiac.name, '(' + result4.zodiac.element + ')');
    console.log('✅ 星座:', result4.constellation?.name || '未提供月日信息');
    console.log('✅ 纳音五行:', result4.wuxing.nayinWuxing);
    console.log('✅ 综合运势:', result4.overall + '分');
    console.log('✅ 梅花易数:', result4.meihua.originalGua.upper.name + result4.meihua.originalGua.lower.name);
    console.log('✅ 大衍之数:', result4.dayan.fortuneLevel.level);
  } else {
    console.log('❌ 测试失败: 无法计算运势');
  }
  
  console.log('\n🎉 运势算法测试完成！');
  
  // 返回测试结果供进一步分析
  return {
    test1: result1,
    test2: result2,
    test3: result3,
    test4: result4
  };
};

// 测试文案库覆盖率
export const testTextCoverage = () => {
  console.log('\n📚 测试文案库覆盖率...');
  
  const elements = ['金', '木', '水', '火', '土'];
  const scores = [90, 70, 45]; // 高、中、低分
  
  elements.forEach(element => {
    scores.forEach(score => {
      const testUser = { year: 1990, month: 6, day: 15 };
      const result = calculateDailyFortune(testUser);
      
      if (result) {
        // 模拟不同的分数和元素组合
        result.wuxing.userElement = element;
        result.overall = score;
        result.constellation = { scores: { career: score, wealth: score, love: score, health: score } };
        
        console.log(`✅ ${element}元素 ${score}分: 文案生成正常`);
      }
    });
  });
  
  console.log('📚 文案库覆盖率测试完成！');
};

// 性能测试
export const testPerformance = () => {
  console.log('\n⚡ 开始性能测试...');
  
  const testUser = { year: 1990, month: 6, day: 15 };
  const iterations = 100;
  
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    calculateDailyFortune(testUser);
  }
  
  const endTime = Date.now();
  const totalTime = endTime - startTime;
  const avgTime = totalTime / iterations;
  
  console.log(`✅ ${iterations}次计算总耗时: ${totalTime}ms`);
  console.log(`✅ 平均每次计算耗时: ${avgTime.toFixed(2)}ms`);
  console.log(`✅ 每秒可处理: ${Math.round(1000 / avgTime)}次计算`);
  
  if (avgTime < 10) {
    console.log('🚀 性能优秀！');
  } else if (avgTime < 50) {
    console.log('👍 性能良好！');
  } else {
    console.log('⚠️ 性能需要优化');
  }
  
  console.log('⚡ 性能测试完成！');
};

// 导出所有测试函数
export const runAllTests = () => {
  testFortuneCalculation();
  testTextCoverage();
  testPerformance();
};
