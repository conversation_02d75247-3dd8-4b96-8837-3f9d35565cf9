import React, {createContext, useState, useContext, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {COLORS} from '../utils/color';

// 预设的主题配置选项
export const THEME_CONFIGS = {
  ocean: {
    name: '海洋蓝',
    description: '深邃如海，宁静致远',
    primary: '#4E7D96',
    gradient: ['#4E7D96', '#3A6075'],
    accent: '#5AC8FA',
    category: 'classic',
  },
  sky: {
    name: '天空蓝',
    description: '清澈明亮，自由无限',
    primary: '#007AFF',
    gradient: ['#007AFF', '#0056CC'],
    accent: '#40C4FF',
    category: 'vibrant',
  },
  forest: {
    name: '森林绿',
    description: '生机盎然，自然清新',
    primary: '#34C759',
    gradient: ['#34C759', '#28A745'],
    accent: '#4CAF50',
    category: 'nature',
  },
  sunset: {
    name: '日落橙',
    description: '温暖如阳，活力四射',
    primary: '#FF9500',
    gradient: ['#FF9500', '#FF8C00'],
    accent: '#FFB74D',
    category: 'warm',
  },
  lavender: {
    name: '薰衣草',
    description: '优雅浪漫，梦幻迷人',
    primary: '#AF52DE',
    gradient: ['#AF52DE', '#9C27B0'],
    accent: '#BA68C8',
    category: 'elegant',
  },
  rose: {
    name: '玫瑰红',
    description: '热情似火，魅力无限',
    primary: '#FF3B30',
    gradient: ['#FF3B30', '#E53E3E'],
    accent: '#FF6B6B',
    category: 'passionate',
  },
  mint: {
    name: '薄荷绿',
    description: '清新淡雅，舒缓心情',
    primary: '#00D4AA',
    gradient: ['#00D4AA', '#00B894'],
    accent: '#26D0CE',
    category: 'fresh',
  },
  midnight: {
    name: '午夜蓝',
    description: '神秘深邃，高贵典雅',
    primary: '#2C3E50',
    gradient: ['#2C3E50', '#34495E'],
    accent: '#3498DB',
    category: 'dark',
  },
};

// 为了向后兼容，保留原有的 THEME_COLORS
export const THEME_COLORS = Object.fromEntries(
  Object.entries(THEME_CONFIGS).map(([key, config]) => [key, config.primary])
);

export type ThemeKey = keyof typeof THEME_CONFIGS;
export type ThemeColorKey = keyof typeof THEME_COLORS; // 保持向后兼容

interface ThemeConfig {
  name: string;
  description: string;
  primary: string;
  gradient: string[];
  accent: string;
  category: string;
}

interface ThemeContextType {
  currentTheme: ThemeKey;
  themeConfig: ThemeConfig;
  themeColor: string;
  changeTheme: (theme: ThemeKey) => void;
  colors: typeof COLORS;
}

// 创建上下文
const ThemeContext = createContext<ThemeContextType>({
  currentTheme: 'ocean',
  themeConfig: THEME_CONFIGS.ocean,
  themeColor: THEME_CONFIGS.ocean.primary,
  changeTheme: () => {},
  colors: COLORS,
});

// 主题提供者组件
export const ThemeProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>('ocean');
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>(THEME_CONFIGS.ocean);
  const [themeColor, setThemeColor] = useState<string>(THEME_CONFIGS.ocean.primary);
  const [colors, setColors] = useState(COLORS);

  // 从存储中加载主题设置
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('app_theme');
        if (savedTheme && savedTheme in THEME_CONFIGS) {
          updateTheme(savedTheme as ThemeKey);
        }
      } catch (error) {
        console.error('加载主题设置失败:', error);
      }
    };

    loadTheme();
  }, []);

  // 更新主题色和颜色对象
  const updateTheme = (theme: ThemeKey) => {
    const newThemeConfig = THEME_CONFIGS[theme];
    setCurrentTheme(theme);
    setThemeConfig(newThemeConfig);
    setThemeColor(newThemeConfig.primary);

    // 更新全局颜色对象，只修改primary颜色
    const updatedColors = {
      ...COLORS,
      primary: newThemeConfig.primary,
      // 根据需要可以添加其他依赖于主题色的颜色
      secondary: `${newThemeConfig.primary}10`, // 10%透明度的主题色
    };

    setColors(updatedColors);
  };

  // 切换主题并保存设置
  const changeTheme = async (theme: ThemeKey) => {
    try {
      await AsyncStorage.setItem('app_theme', theme);
      updateTheme(theme);
    } catch (error) {
      console.error('保存主题设置失败:', error);
    }
  };

  return (
    <ThemeContext.Provider
      value={{currentTheme, themeConfig, themeColor, changeTheme, colors}}>
      {children}
    </ThemeContext.Provider>
  );
};

// 自定义钩子，方便在组件中使用
export const useTheme = () => useContext(ThemeContext);
