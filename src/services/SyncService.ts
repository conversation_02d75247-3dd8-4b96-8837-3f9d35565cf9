import NetInfo from '@react-native-community/netinfo';
import BleManager from 'react-native-ble-manager';
import { NativeEventEmitter, NativeModules, Platform, PermissionsAndroid } from 'react-native';
import { EventEmitter } from 'events';
import databaseService from './DatabaseService';

// 定义同步数据类型
export interface SyncData {
  deviceId: string;
  deviceName: string;
  familyId: number;
  familyName: string;
  transactions: any[];
  lastSyncTime: string;
}

// 定义同步状态
export enum SyncStatus {
  IDLE = 'idle',
  SCANNING = 'scanning',
  CONNECTING = 'connecting',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// 蓝牙服务和特征UUID
const SERVICE_UUID = '0000180f-0000-1000-8000-00805f9b34fb'; // 使用标准电池服务UUID作为示例
const DATA_CHARACTERISTIC_UUID = '00002a19-0000-1000-8000-00805f9b34fb'; // 使用电池电量特征UUID

class SyncService {
  private bleManagerEmitter: NativeEventEmitter;
  private eventEmitter: EventEmitter;
  private isScanning: boolean = false;
  private isSyncing: boolean = false;
  private discoveredDevices: Map<string, any> = new Map();
  private connectedDevices: Map<string, any> = new Map();
  private syncTimeout: NodeJS.Timeout | null = null;
  private lastSyncTimestamp: number = 0;
  private static instance: SyncService;

  // 单例模式
  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  private constructor() {
    this.eventEmitter = new EventEmitter();
    this.initializeBle();
  }

  // 初始化蓝牙
  private async initializeBle(): Promise<void> {
    try {
      // 初始化BleManager
      await BleManager.start({ showAlert: false });
      console.log('BleManager初始化成功');

      // 设置NativeEventEmitter监听蓝牙事件
      this.bleManagerEmitter = new NativeEventEmitter(NativeModules.BleManager);

      // 监听设备发现事件
      this.bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        this.handleDiscoverPeripheral
      );

      // 监听连接状态变化
      this.bleManagerEmitter.addListener(
        'BleManagerConnectPeripheral',
        this.handleConnectPeripheral
      );

      this.bleManagerEmitter.addListener(
        'BleManagerDisconnectPeripheral',
        this.handleDisconnectPeripheral
      );

      console.log('蓝牙事件监听器已设置');
    } catch (error) {
      console.error('初始化蓝牙失败', error);
    }
  }

  // 开始同步过程
  public async startSync(): Promise<boolean> {
    try {
      if (this.isSyncing) {
        console.log('同步已经在进行中');
        return false;
      }

      // 请求必要的权限
      if (Platform.OS === 'android') {
        await this.requestAndroidPermissions();
      }

      this.isSyncing = true;
      this.lastSyncTimestamp = Date.now();
      this.emitSyncStatus(SyncStatus.SCANNING, '正在扫描设备...');

      // 清理之前的发现和连接
      this.discoveredDevices.clear();
      this.connectedDevices.clear();

      // 检查网络状态，决定使用WiFi还是蓝牙同步
      const networkInfo = await NetInfo.fetch();
      console.log('当前网络状态:', networkInfo);

      if (networkInfo.type === 'wifi' && networkInfo.isConnected) {
        console.log('使用WiFi同步');
        await this.startWifiSync();
      } else {
        console.log('使用蓝牙同步');
        await this.startBluetoothSync();
      }

      // 设置超时，避免同步过程永远不结束
      this.syncTimeout = setTimeout(() => {
        if (this.isSyncing) {
          this.stopSync(SyncStatus.FAILED, '同步超时');
        }
      }, 60000); // 60秒超时

      return true;
    } catch (error) {
      console.error('开始同步失败', error);
      this.stopSync(SyncStatus.FAILED, '开始同步失败: ' + error.message);
      return false;
    }
  }

  // 停止同步
  public stopSync(status: SyncStatus = SyncStatus.IDLE, message: string = ''): void {
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout);
      this.syncTimeout = null;
    }

    if (this.isScanning) {
      this.stopScan();
    }

    // 断开所有连接的设备
    this.disconnectAllDevices();

    this.isSyncing = false;
    this.emitSyncStatus(status, message);
  }

  // 断开所有设备连接
  private async disconnectAllDevices(): Promise<void> {
    for (const deviceId of this.connectedDevices.keys()) {
      try {
        await BleManager.disconnect(deviceId);
        console.log('已断开设备连接:', deviceId);
      } catch (error) {
        console.error('断开设备连接失败:', deviceId, error);
      }
    }
    this.connectedDevices.clear();
  }

  // WiFi同步实现
  private async startWifiSync(): Promise<void> {
    try {
      // 获取家庭信息
      const family = await databaseService.getFamilyInfo();
      if (!family) {
        throw new Error('用户未加入家庭');
      }

      // 获取本地设备信息
      const deviceId = await databaseService.getDeviceId();
      const memberInfo = await databaseService.getFamilyMemberByDeviceId(deviceId);
      const deviceName = memberInfo?.name || '未命名设备';

      // 准备要同步的数据
      const transactions = await this.getTransactionsToSync(family.id);

      // 获取本机IP地址
      const networkInfo = await NetInfo.fetch();
      const localIp = networkInfo.details?.ipAddress;
      if (!localIp) {
        throw new Error('无法获取本机IP地址');
      }

      console.log('本机IP地址:', localIp);
      this.emitSyncStatus(SyncStatus.SCANNING, '正在搜索局域网内的设备...');

      // 1. 启动本地UDP服务器，监听来自同一家庭的其他设备的广播
      const server = await this.startUdpServer(localIp, family.id, deviceId);

      // 2. 广播本机信息到局域网，让其他设备发现
      await this.broadcastDeviceInfo(localIp, {
        deviceId,
        deviceName,
        familyId: family.id,
        familyName: family.name,
        lastSyncTime: memberInfo?.lastSyncTime || '',
      });

      // 等待一段时间收集设备信息
      await this.delay(5000);

      // 3. 关闭UDP服务器
      if (server) {
        server.close();
      }

      // 检查是否发现了设备
      if (this.discoveredDevices.size === 0) {
        this.stopSync(SyncStatus.FAILED, '未发现任何家庭成员设备');
        return;
      }

      this.emitSyncStatus(
        SyncStatus.CONNECTING,
        `发现 ${this.discoveredDevices.size} 台设备，正在连接...`
      );

      // 4. 与每个发现的设备建立TCP连接进行数据交换
      const syncPromises = [];
      for (const [deviceId, device] of this.discoveredDevices.entries()) {
        syncPromises.push(this.syncWithDeviceOverTcp(device, {
          deviceId,
          deviceName,
          familyId: family.id,
          familyName: family.name,
          transactions,
          lastSyncTime: new Date().toISOString(),
        }));
      }

      // 等待所有同步完成
      await Promise.all(syncPromises);

      // 更新同步时间
      await this.updateLastSyncTime(deviceId);

      // 同步完成
      this.stopSync(
        SyncStatus.COMPLETED,
        `同步完成，已与 ${this.discoveredDevices.size} 台设备同步`
      );
    } catch (error) {
      console.error('WiFi同步失败', error);
      this.stopSync(SyncStatus.FAILED, '同步失败: ' + (error as Error).message);
    }
  }

  // 启动UDP服务器监听广播
  private async startUdpServer(localIp: string, familyId: number, deviceId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      let socket = null;

      try {
        const dgram = require('react-native-udp');
        socket = dgram.createSocket('udp4');

        // 监听错误
        socket.on('error', (err) => {
          console.error('UDP服务器错误:', err);
          reject(err);
        });

        // 监听关闭
        socket.on('close', () => {
          console.log('UDP服务器已关闭');
        });

        // 监听成功绑定
        socket.on('listening', () => {
          const address = socket.address();
          console.log(`UDP服务器正在监听: ${address.address}:${address.port}`);
          resolve(socket);
        });

        // 处理接收到的消息
        socket.on('message', (msg, rinfo) => {
          try {
            const msgStr = msg.toString();
            const data = JSON.parse(msgStr);

            console.log('收到消息:', data, '来自:', rinfo.address);

            // 检查是否来自同一家庭的设备且不是自己
            if (data.familyId === familyId && data.deviceId !== deviceId) {
              // 添加到已发现设备列表
              this.discoveredDevices.set(data.deviceId, {
                id: data.deviceId,
                name: data.deviceName || 'Unknown Device',
                ip: rinfo.address,
                port: rinfo.port,
                syncData: data,
              });

              // 发送设备发现事件
              this.emitDiscoveredDevice({
                id: data.deviceId,
                name: data.deviceName || 'Unknown Device',
                ip: rinfo.address,
                port: rinfo.port,
              });

              // 发送回复
              const replyString = JSON.stringify({
                deviceId,
                message: 'SYNC_ACKNOWLEDGE',
                familyId,
              });

              socket.send(
                replyString,
                0,
                replyString.length,
                rinfo.port,
                rinfo.address,
                (err) => {
                  if (err) {
                    console.error('发送回复失败:', err);
                  }
                }
              );
            }
          } catch (e) {
            console.error('处理消息失败:', e);
          }
        });

        // 绑定到固定端口
        console.log('正在绑定UDP服务器...');
        socket.bind(9898);

      } catch (error) {
        console.error('启动UDP服务器失败:', error);
        reject(error);
      }
    });
  }

  // 广播设备信息到局域网
  private async broadcastDeviceInfo(localIp: string, deviceInfo: any): Promise<void> {
    let socket = null;
    try {
      const dgram = require('react-native-udp');
      socket = dgram.createSocket('udp4');

      // 获取广播地址
      const broadcastIp = this.getBroadcastAddress(localIp);
      const broadcastPort = 9898;

      // 将消息转换为字符串
      const messageString = JSON.stringify(deviceInfo);

      // 使用Promise包装socket绑定和发送过程
      await new Promise((resolve, reject) => {
        // 监听错误
        socket.on('error', (err) => {
          console.error('广播socket错误:', err);
          try { socket.close(); } catch (e) {}
          reject(err);
        });

        // 监听关闭事件
        socket.on('close', () => {
          console.log('广播socket已关闭');
        });

        // 监听绑定成功事件
        socket.on('listening', () => {
          console.log('广播socket已绑定，地址:', socket.address());

          // 设置广播模式
          try {
            socket.setBroadcast(true);
            console.log('已设置广播模式');

            // 发送广播消息
            socket.send(
              messageString,
              0,
              messageString.length,
              broadcastPort,
              broadcastIp,
              (err) => {
                if (err) {
                  console.error('发送广播失败:', err);
                  try { socket.close(); } catch (e) {}
                  reject(err);
                } else {
                  console.log('广播已发送到:', broadcastIp + ':' + broadcastPort);

                  // 在广播发送后5秒关闭socket
                  setTimeout(() => {
                    if (socket) {
                      try {
                        socket.close();
                        console.log('广播socket已正常关闭');
                      } catch (e) {
                        console.error('关闭socket失败:', e);
                      }
                    }
                    resolve(true);
                  }, 5000);
                }
              }
            );
          } catch (e) {
            console.error('设置广播模式失败:', e);
            try { socket.close(); } catch (e) {}
            reject(e);
          }
        });

        // 绑定socket
        console.log('正在绑定广播socket...');
        socket.bind(0);  // 让系统自动分配端口
      });

      console.log('广播发送完成');
    } catch (error) {
      console.error('广播设备信息失败:', error);
      // 在出错时尝试关闭socket - 移动到这里
      if (socket) {
        try { socket.close(); } catch (e) {}
      }
    }
    // 移除原有的finally块，所有socket关闭操作都已经移到各个合适的位置
  }

  // 获取广播地址
  private getBroadcastAddress(ipAddress: string): string {
    try {
      // 确保IP地址是有效的
      if (!ipAddress || typeof ipAddress !== 'string') {
        console.log('无效的IP地址，使用默认广播地址');
        return '***************';
      }

      // 将最后一个段替换为255
      const ipParts = ipAddress.split('.');
      if (ipParts.length === 4) {
        ipParts[3] = '255';
        return ipParts.join('.');
      }

      return '***************'; // 默认广播地址
    } catch (error) {
      console.error('获取广播地址失败:', error);
      return '***************'; // 出错时返回默认广播地址
    }
  }

  // 与设备通过TCP进行数据同步
  private async syncWithDeviceOverTcp(device: any, syncData: SyncData): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        // 此处需要使用React Native兼容的TCP库
        // 例如: react-native-tcp-socket
        // 以下是伪代码，实际实现需要根据所用库进行调整

        const TcpSocket = require('react-native-tcp-socket');
        const options = {
          host: device.ip,
          port: 9899, // 使用不同于UDP的端口
          timeout: 10000, // 超时时间
        };

        console.log('正在连接设备:', device.name, '地址:', device.ip);
        this.emitSyncStatus(SyncStatus.SYNCING, `正在与 ${device.name} 同步...`);

        // 建立TCP连接
        const client = TcpSocket.createConnection(options, async () => {
          console.log('已连接到设备:', device.name);

          // 发送同步数据
          const dataStr = JSON.stringify(syncData);
          client.write(dataStr);

          // 监听接收到的数据
          let receivedData = '';
          client.on('data', (data: Buffer) => {
            receivedData += data.toString();

            // 检查是否接收到完整的JSON对象
            try {
              const parsedData = JSON.parse(receivedData);
              console.log('收到来自设备的数据:', parsedData);

              // 处理接收到的数据
              this.processReceivedData(parsedData)
                .then(() => {
                  // 关闭连接
                  client.destroy();

                  console.log('与设备 ' + device.name + ' 同步完成');
                  resolve();
                })
                .catch(error => {
                  console.error('处理接收数据失败:', error);
                  client.destroy();
                  reject(error);
                });
            } catch (e) {
              // JSON不完整，继续接收更多数据
            }
          });
        });

        // 处理错误
        client.on('error', (err: any) => {
          console.error('TCP连接错误:', err);
          reject(err);
        });

        // 处理连接关闭
        client.on('close', () => {
          console.log('TCP连接关闭');
        });

      } catch (error) {
        console.error('TCP同步失败:', error);
        reject(error);
      }
    });
  }

  // 蓝牙同步实现
  private async startBluetoothSync(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        const enabled = await BleManager.enableBluetooth();
        console.log('蓝牙已启用:', enabled);
      }

      // 获取家庭信息
      const family = await databaseService.getFamilyInfo();
      if (!family) {
        throw new Error('用户未加入家庭');
      }

      // 开始扫描设备
      this.startScan();

      // 设置扫描超时，10秒后如果没有发现设备，则停止扫描
      setTimeout(() => {
        if (this.isScanning) {
          this.stopScan();

          if (this.discoveredDevices.size === 0) {
            this.stopSync(SyncStatus.FAILED, '未发现任何设备');
          } else {
            // 连接发现的设备
            this.connectDiscoveredDevices();
          }
        }
      }, 10000);
    } catch (error) {
      console.error('蓝牙同步失败', error);
      this.stopSync(SyncStatus.FAILED, '蓝牙同步失败: ' + error.message);
    }
  }

  // 开始蓝牙扫描
  private async startScan(): Promise<void> {
    if (this.isScanning) {
      return;
    }

    try {
      this.isScanning = true;
      this.emitSyncStatus(SyncStatus.SCANNING, '正在扫描蓝牙设备...');

      await BleManager.scan([], 20, true);
      console.log('蓝牙扫描开始');
    } catch (error) {
      console.error('开始蓝牙扫描失败', error);
      this.isScanning = false;
      this.emitSyncStatus(SyncStatus.FAILED, '扫描蓝牙设备失败');
    }
  }

  // 停止蓝牙扫描
  private async stopScan(): Promise<void> {
    if (!this.isScanning) {
      return;
    }

    try {
      await BleManager.stopScan();
      console.log('蓝牙扫描已停止');
      this.isScanning = false;
    } catch (error) {
      console.error('停止蓝牙扫描失败', error);
      this.isScanning = false;
    }
  }

  // 连接发现的设备
  private async connectDiscoveredDevices(): Promise<void> {
    if (this.discoveredDevices.size === 0) {
      this.emitSyncStatus(SyncStatus.FAILED, '未发现任何可连接的设备');
      return;
    }

    this.emitSyncStatus(
      SyncStatus.CONNECTING,
      `正在连接 ${this.discoveredDevices.size} 台设备...`
    );

    // 尝试连接每个发现的设备
    for (const [deviceId, device] of this.discoveredDevices.entries()) {
      try {
        await this.connectDevice(deviceId, device);
      } catch (error) {
        console.error(`连接设备 ${deviceId} 失败:`, error);
      }
    }

    // 检查是否有成功连接的设备
    if (this.connectedDevices.size > 0) {
      this.emitSyncStatus(
        SyncStatus.SYNCING,
        `连接成功，正在与 ${this.connectedDevices.size} 台设备同步...`
      );
      this.syncWithConnectedDevices();
    } else {
      this.stopSync(SyncStatus.FAILED, '未能连接到任何设备');
    }
  }

  // 连接特定设备
  private async connectDevice(deviceId: string, device: any): Promise<void> {
    try {
      await BleManager.connect(deviceId);
      console.log('已连接到设备:', deviceId);

      // 发现设备服务
      const peripheralInfo = await BleManager.retrieveServices(deviceId);
      console.log('设备服务:', peripheralInfo);

      // 检查设备是否支持我们需要的服务和特征
      if (this.deviceSupportsOurService(peripheralInfo)) {
        this.connectedDevices.set(deviceId, device);
        console.log('设备已添加到连接列表:', deviceId);
      } else {
        console.log('设备不支持所需服务，断开连接:', deviceId);
        await BleManager.disconnect(deviceId);
      }
    } catch (error) {
      console.error('连接设备失败:', deviceId, error);
      throw error;
    }
  }

  // 检查设备是否支持我们需要的服务
  private deviceSupportsOurService(peripheralInfo: any): boolean {
    // 实际实现中，应该检查设备是否提供我们的自定义服务
    // 这里简化实现，假设所有设备都支持
    return true;
  }

  // 与已连接设备同步数据
  private async syncWithConnectedDevices(): Promise<void> {
    try {
      // 获取家庭和设备信息
      const family = await databaseService.getFamilyInfo();
      if (!family) {
        throw new Error('用户未加入家庭');
      }

      const deviceId = await databaseService.getDeviceId();
      const memberInfo = await databaseService.getFamilyMemberByDeviceId(deviceId);
      const deviceName = memberInfo?.name || '未命名设备';

      // 获取要同步的交易数据
      const transactions = await this.getTransactionsToSync(family.id);

      // 准备同步数据
      const syncData: SyncData = {
        deviceId,
        deviceName,
        familyId: family.id,
        familyName: family.name,
        transactions,
        lastSyncTime: new Date().toISOString(),
      };

      // 序列化数据以通过蓝牙传输
      const syncDataString = JSON.stringify(syncData);

      // 分片传输数据（蓝牙特性有字节限制）
      const chunks = this.chunkString(syncDataString, 512); // 每个块512字节

      // 与每个已连接设备同步
      for (const [connectedDeviceId, device] of this.connectedDevices.entries()) {
        try {
          // 发送数据块数量
          await BleManager.write(
            connectedDeviceId,
            SERVICE_UUID,
            DATA_CHARACTERISTIC_UUID,
            this.stringToBytes(`CHUNKS:${chunks.length}`)
          );

          // 逐个发送数据块
          for (let i = 0; i < chunks.length; i++) {
            await BleManager.write(
              connectedDeviceId,
              SERVICE_UUID,
              DATA_CHARACTERISTIC_UUID,
              this.stringToBytes(chunks[i])
            );

            // 在发送每个块后暂停一下，避免数据丢失
            await this.delay(100);
          }

          // 发送完成标记
          await BleManager.write(
            connectedDeviceId,
            SERVICE_UUID,
            DATA_CHARACTERISTIC_UUID,
            this.stringToBytes('DONE')
          );

          // 接收对方的数据
          await this.receiveDataFromDevice(connectedDeviceId);

        } catch (error) {
          console.error(`与设备 ${connectedDeviceId} 同步失败:`, error);
        }
      }

      // 所有设备都处理完毕，更新同步时间
      await this.updateLastSyncTime(deviceId);

      // 完成同步
      this.stopSync(
        SyncStatus.COMPLETED,
        `同步完成，已与 ${this.connectedDevices.size} 台设备同步`
      );

    } catch (error) {
      console.error('同步数据失败:', error);
      this.stopSync(SyncStatus.FAILED, '同步数据失败: ' + error.message);
    }
  }

  // 从设备接收数据
  private async receiveDataFromDevice(deviceId: string): Promise<void> {
    try {
      // 这里应该实现从设备读取数据的逻辑
      // 由于实际蓝牙通信复杂度较高，这里使用模拟数据

      // 模拟接收到的数据
      const remoteData: SyncData = {
        deviceId: `remote-${deviceId}`,
        deviceName: '远程设备',
        familyId: 1, // 假设与当前用户同一家庭
        familyName: '我的家庭',
        transactions: [
          // 模拟一些交易数据
          {
            id: Math.floor(Math.random() * 10000) + 1000,
            amount: 89.5,
            note: '餐厅晚餐',
            date: new Date().toISOString().split('T')[0],
            categoryId: 1,
            type: 'expense',
            familyId: 1,
            familyName: '我的家庭',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ],
        lastSyncTime: new Date().toISOString(),
      };

      // 处理接收到的数据
      await this.processReceivedData(remoteData);

    } catch (error) {
      console.error(`从设备 ${deviceId} 接收数据失败:`, error);
      throw error;
    }
  }

  // 处理接收到的数据
  public async processReceivedData(data: SyncData): Promise<void> {
    try {
      console.log(`处理来自 ${data.deviceName} 的数据, ${data.transactions.length} 条交易`);

      // 验证数据有效性
      if (!data.transactions || !Array.isArray(data.transactions)) {
        console.error('接收到无效的交易数据');
        return;
      }

      // 检查是否与当前用户家庭匹配
      const family = await databaseService.getFamilyInfo();
      if (!family || family.id !== data.familyId) {
        console.error('接收到不匹配的家庭数据');
        return;
      }

      // 将每个交易保存到数据库
      for (const transaction of data.transactions) {
        try {
          // 检查交易是否已存在
          const existingTransaction = await databaseService.getTransactionById(transaction.id);

          if (!existingTransaction) {
            // 添加新交易
            await databaseService.addTransaction({
              ...transaction,
              familyId: family.id,
              familyName: family.name,
            });
            console.log(`已添加新交易: ${transaction.id}`);
          } else {
            // 交易已存在，根据需要更新
            // 这里可以添加更新逻辑
            console.log(`交易已存在: ${transaction.id}`);
          }
        } catch (txError) {
          console.error('保存交易失败:', txError);
        }
      }

      // 更新远程设备的同步时间
      await databaseService.updateFamilyMemberSyncTime(data.deviceId, data.lastSyncTime);

    } catch (error) {
      console.error('处理接收数据失败:', error);
      throw error;
    }
  }

  // 更新最后同步时间
  private async updateLastSyncTime(deviceId: string): Promise<void> {
    try {
      const now = new Date().toISOString();
      await databaseService.updateFamilyMemberSyncTime(deviceId, now);
      console.log('最后同步时间已更新:', now);
    } catch (error) {
      console.error('更新同步时间失败:', error);
    }
  }

  // 获取需要同步的交易数据
  private async getTransactionsToSync(familyId: number): Promise<any[]> {
    try {
      // 获取上次同步时间
      const deviceId = await databaseService.getDeviceId();
      const memberInfo = await databaseService.getFamilyMemberByDeviceId(deviceId);
      const lastSyncTime = memberInfo?.lastSyncTime;

      // 获取自上次同步以来的家庭交易
      let transactions = [];
      try {
        if (lastSyncTime) {
          transactions = await databaseService.getTransactionsSince(lastSyncTime, familyId);
        } else {
          // 首次同步，获取所有家庭交易
          transactions = await databaseService.getTransactionsByFamilyId(familyId);
        }

        console.log(`待同步交易: ${transactions.length}条`);
      } catch (syncError) {
        console.error('获取交易记录失败:', syncError);
        // 返回空数组，避免同步过程完全失败
        transactions = [];
      }

      return transactions;
    } catch (error) {
      console.error('获取同步数据失败:', error);
      return [];
    }
  }

  // 将字符串分割成块
  private chunkString(str: string, length: number): string[] {
    const chunks = [];
    let i = 0;
    while (i < str.length) {
      chunks.push(str.slice(i, i + length));
      i += length;
    }
    return chunks;
  }

  // 字符串转字节数组
  private stringToBytes(str: string): number[] {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i));
    }
    return bytes;
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 请求Android所需权限
  private async requestAndroidPermissions(): Promise<void> {
    if (Platform.OS !== 'android') {return;}

    try {
      // 请求位置权限（蓝牙扫描需要）
      if (parseInt(Platform.Version.toString()) >= 31) { // Android 12+
        await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ]);
      } else {
        await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ]);
      }
    } catch (error) {
      console.error('请求权限失败:', error);
    }
  }

  // 处理发现外围设备事件
  private handleDiscoverPeripheral = (peripheral: any) => {
    // 移除名称检查，记录所有设备
    console.log('发现设备:', peripheral.name || '未命名设备', peripheral.id, 'RSSI:', peripheral.rssi);
    this.discoveredDevices.set(peripheral.id, peripheral);

    // 发送设备发现事件
    this.emitDiscoveredDevice({
      id: peripheral.id,
      name: peripheral.name || '未命名设备',
      rssi: peripheral.rssi,
    });
  };

  // 处理设备连接事件
  private handleConnectPeripheral = (peripheral: any) => {
    console.log('设备已连接:', peripheral.peripheral);
  };

  // 处理设备断开连接事件
  private handleDisconnectPeripheral = (peripheral: any) => {
    console.log('设备已断开连接:', peripheral.peripheral);
    this.connectedDevices.delete(peripheral.peripheral);
  };

  // 获取当前同步状态
  public getSyncStatus(): SyncStatus {
    if (this.isScanning) {return SyncStatus.SCANNING;}
    if (this.isSyncing) {
      if (this.connectedDevices.size > 0) {return SyncStatus.SYNCING;}
      return SyncStatus.CONNECTING;
    }
    return SyncStatus.IDLE;
  }

  // 发送同步状态变化事件
  public emitSyncStatus(status: SyncStatus, message: string = ''): void {
    console.log(`同步状态: ${status}, ${message}`);
    this.eventEmitter.emit('syncStatusChanged', { status, message });
  }

  // 发送设备发现事件
  public emitDiscoveredDevice(device: any): void {
    // 添加到已发现设备列表
    this.discoveredDevices.set(device.id, device);
    // 发送设备发现事件
    this.eventEmitter.emit('deviceDiscovered', device);
  }

  // 添加同步状态监听器
  public addSyncStatusListener(callback: (data: { status: SyncStatus, message: string }) => void): void {
    this.eventEmitter.on('syncStatusChanged', callback);
  }

  // 移除同步状态监听器
  public removeSyncStatusListener(callback: (data: { status: SyncStatus, message: string }) => void): void {
    this.eventEmitter.off('syncStatusChanged', callback);
  }

  // 添加设备发现监听器
  public addDeviceDiscoveredListener(callback: (device: any) => void): void {
    this.eventEmitter.on('deviceDiscovered', callback);
  }

  // 移除设备发现监听器
  public removeDeviceDiscoveredListener(callback: (device: any) => void): void {
    this.eventEmitter.off('deviceDiscovered', callback);
  }

  // 清理资源
  public cleanup(): void {
    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    // 清理蓝牙监听器
    if (this.bleManagerEmitter) {
      this.bleManagerEmitter.removeAllListeners('BleManagerDiscoverPeripheral');
      this.bleManagerEmitter.removeAllListeners('BleManagerConnectPeripheral');
      this.bleManagerEmitter.removeAllListeners('BleManagerDisconnectPeripheral');
    }

    // 停止扫描和同步
    if (this.isScanning) {
      BleManager.stopScan().catch(error => {
        console.error('停止蓝牙扫描失败', error);
      });
    }

    this.disconnectAllDevices();

    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout);
      this.syncTimeout = null;
    }
  }

  // 添加一个方法来启动定期后台同步
  public startAutoSync(intervalMinutes: number = 30): void {
    // 每隔指定时间自动尝试同步
    setInterval(() => {
      if (!this.isSyncing) {
        this.startSync().catch(error => {
          console.error('自动同步失败:', error);
        });
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`已启用自动同步，间隔${intervalMinutes}分钟`);
  }
}

// 导出单例实例
const syncService = SyncService.getInstance();
export default syncService;
