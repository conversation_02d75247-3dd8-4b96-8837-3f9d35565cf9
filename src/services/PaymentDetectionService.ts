import { NativeModules, NativeEventEmitter, Platform, Alert } from 'react-native';
import { navigate } from '../navigation/RootNavigation';

const { PaymentDetection } = NativeModules;
console.log('NativeModules:', Object.keys(NativeModules)); // 调试用
console.log('PaymentDetection 模块:', PaymentDetection);
if (PaymentDetection) {
  console.log('PaymentDetection 方法:', Object.keys(PaymentDetection));
}

// 创建一个安全的模块访问方式
const safePaymentDetection = PaymentDetection || {
  checkAccessibilityPermission: async () => {
    console.warn('PaymentDetection 模块不可用，无法检查无障碍权限');
    return false;
  },
  checkNotificationPermission: async () => {
    console.warn('PaymentDetection 模块不可用，无法检查通知权限');
    return false;
  },
  openAccessibilitySettings: () => {
    console.warn('PaymentDetection 模块不可用，无法打开无障碍设置');
  },
  openNotificationSettings: () => {
    console.warn('PaymentDetection 模块不可用，无法打开通知设置');
  },
  checkOverlayPermission: async () => {
    console.warn('PaymentDetection 模块不可用，无法检查悬浮窗权限');
    return false;
  },
  openOverlaySettings: () => {
    console.warn('PaymentDetection 模块不可用，无法打开悬浮窗设置');
  },
  getLaunchParams: async () => {
    console.warn('PaymentDetection 模块不可用，无法获取启动参数');
    return null;
  },
  getInitialProps: async () => {
    console.warn('PaymentDetection 模块不可用，无法获取启动参数');
    return null;
  },
  testPaymentDetection: async () => {
    console.warn('PaymentDetection 模块不可用，无法进行测试');
    return null;
  },
};

// 如果模块不可用，创建一个空的事件发射器
const paymentEventEmitter = PaymentDetection
  ? new NativeEventEmitter(PaymentDetection)
  : { addListener: () => ({ remove: () => {} }) };

class PaymentDetectionService {
  private static instance: PaymentDetectionService;
  private eventListener: any = null;

  private constructor() {
    // 私有构造函数，防止直接实例化
  }

  public static getInstance(): PaymentDetectionService {
    if (!PaymentDetectionService.instance) {
      PaymentDetectionService.instance = new PaymentDetectionService();
    }
    return PaymentDetectionService.instance;
  }

  public async checkPermissions(): Promise<boolean> {
    try {
      console.log('检查权限...');

      // 检查无障碍权限
      const hasAccessibilityPermission = await safePaymentDetection.checkAccessibilityPermission();
      console.log('无障碍权限:', hasAccessibilityPermission);

      // 检查通知权限
      const hasNotificationPermission = await safePaymentDetection.checkNotificationPermission();
      console.log('通知权限:', hasNotificationPermission);

      // 检查悬浮窗权限
      const hasOverlayPermission = await safePaymentDetection.checkOverlayPermission();
      console.log('悬浮窗权限:', hasOverlayPermission);

      // 如果任何一个权限没有，引导用户开启
      // if (!hasAccessibilityPermission) {
      //   console.log('引导用户开启无障碍权限');
      //   Alert.alert(
      //     '需要无障碍权限',
      //     '为了自动检测支付成功，需要开启无障碍权限。请在设置中找到"snow"并启用。',
      //     [
      //       {text: '取消', style: 'cancel'},
      //       {
      //         text: '去设置',
      //         onPress: () => safePaymentDetection.openAccessibilitySettings(),
      //       },
      //     ],
      //   );
      //   return false;
      // }

      // if (!hasNotificationPermission) {
      //   console.log('引导用户开启通知权限');
      //   Alert.alert(
      //     '需要通知权限',
      //     '为了自动检测支付通知，需要开启通知权限。请在设置中找到"snow"并启用。',
      //     [
      //       {text: '取消', style: 'cancel'},
      //       {
      //         text: '去设置',
      //         onPress: () => safePaymentDetection.openNotificationSettings(),
      //       },
      //     ],
      //   );
      //   return false;
      // }

      // if (!hasOverlayPermission) {
      //   console.log('引导用户开启悬浮窗权限');
      //   Alert.alert(
      //     '需要悬浮窗权限',
      //     '为了在支付成功后显示记账提示，需要开启悬浮窗权限。',
      //     [
      //       {text: '取消', style: 'cancel'},
      //       {
      //         text: '去设置',
      //         onPress: () => safePaymentDetection.openOverlaySettings(),
      //       },
      //     ],
      //   );
      //   return false;
      // }

      // 改进权限检查逻辑：支持多种检测模式
      // 模式1：完整模式（无障碍 + 通知 + 悬浮窗）
      // 模式2：通知模式（仅通知权限）
      // 模式3：无障碍模式（无障碍 + 悬浮窗）

      const fullMode = hasAccessibilityPermission && hasNotificationPermission && hasOverlayPermission;
      const notificationOnlyMode = hasNotificationPermission;
      const accessibilityOnlyMode = hasAccessibilityPermission;

      console.log('检测模式可用性:', {
        fullMode,
        notificationOnlyMode,
        accessibilityOnlyMode,
      });

      // 至少需要一种检测方式可用
      return fullMode || notificationOnlyMode || accessibilityOnlyMode;
    } catch (error) {
      console.error('检查权限失败:', error);
      return false;
    }
  }

  public async checkAccessibilityPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      return await safePaymentDetection.checkAccessibilityPermission();
    } catch (error) {
      console.error('检查无障碍权限失败', error);
      return false;
    }
  }

  public async checkNotificationPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      return await safePaymentDetection.checkNotificationPermission();
    } catch (error) {
      console.error('检查通知权限失败', error);
      return false;
    }
  }

  public openAccessibilitySettings(): void {
    if (Platform.OS !== 'android') {
      return;
    }

    safePaymentDetection.openAccessibilitySettings();
  }

  public openNotificationSettings(): void {
    if (Platform.OS !== 'android') {
      return;
    }

    safePaymentDetection.openNotificationSettings();
  }

  public startListening(): void {
    console.log('开始监听支付事件...');

    // 添加事件监听器
    this.eventListener = paymentEventEmitter.addListener(
      'PaymentSuccess',
      this.handlePaymentSuccess,
    );

    // 通知原生模块开始监听
    if (Platform.OS === 'android') {
      safePaymentDetection.startListening();
      console.log('已通知原生模块开始监听');
    }
  }

  public stopListening(): void {
    if (this.eventListener) {
      this.eventListener.remove();
      this.eventListener = null;
    }
  }

  // 添加防重复弹窗机制
  private lastAlertTime = 0;
  private readonly ALERT_COOLDOWN = 3000; // 3秒内不重复弹窗

  public handlePaymentSuccess = (data: { platform: string, amount: string }) => {
    const { platform, amount } = data;
    console.log('处理支付事件:', platform, amount);

    // 防止重复弹窗
    const currentTime = Date.now();
    if (currentTime - this.lastAlertTime < this.ALERT_COOLDOWN) {
      console.log('忽略重复的支付提示，距上次弹窗:', currentTime - this.lastAlertTime, 'ms');
      return;
    }
    this.lastAlertTime = currentTime;

    // 显示自动记账提示
    Alert.alert(
      '检测到支付完成',
      `您刚刚通过${platform === 'alipay' ? '支付宝' : '微信'}支付了 ¥${amount}，是否记录这笔支出？`,
      [
        {
          text: '记一笔',
          onPress: () => {
            console.log('用户选择记账，导航参数:', {
              type: 'expense',
              amount: parseFloat(amount),
              autoDetected: true,
              platform: platform,
            });

            // 使用setTimeout确保Alert完全关闭后再导航
            setTimeout(() => {
              try {
                navigate('addTransaction', {
                  type: 'expense',
                  amount: parseFloat(amount),
                  autoDetected: true,
                  platform: platform,
                });
                console.log('导航命令已发送');
              } catch (error) {
                console.error('导航失败:', error);
              }
            }, 100);
          },
        },
        {
          text: '忽略',
          style: 'cancel',
          onPress: () => console.log('用户选择忽略'),
        },
      ]
    );
  };

  public async checkOverlayPermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      if (safePaymentDetection.checkOverlayPermission) {
        return await safePaymentDetection.checkOverlayPermission();
      } else {
        console.warn('checkOverlayPermission 方法不可用');
        return false;
      }
    } catch (error) {
      console.error('检查悬浮窗权限失败', error);
      return false;
    }
  }

  public openOverlaySettings(): void {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      if (safePaymentDetection.openOverlaySettings) {
        safePaymentDetection.openOverlaySettings();
      } else {
        console.warn('openOverlaySettings 方法不可用');
      }
    } catch (error) {
      console.error('打开悬浮窗设置失败', error);
    }
  }

  public async getLaunchParams(): Promise<any> {
    try {
      if (Platform.OS === 'android' && safePaymentDetection.getLaunchParams) {
        return await safePaymentDetection.getLaunchParams();
      }
      return null;
    } catch (error) {
      console.error('获取启动参数失败:', error);
      return null;
    }
  }

  public async getInitialProps(): Promise<any> {
    try {
      if (Platform.OS === 'android' && safePaymentDetection.getInitialProps) {
        return await safePaymentDetection.getInitialProps();
      }
      return null;
    } catch (error) {
      console.error('获取启动参数失败:', error);
      return null;
    }
  }

  public listenForNewIntent(callback: () => void): () => void {
    if (Platform.OS !== 'android') {
      return () => {};
    }

    const subscription = paymentEventEmitter.addListener('NewIntent', callback);
    return () => subscription.remove();
  }

  // 新增：测试功能
  public async testPaymentDetection(testType: 'alipay' | 'wechat' | 'permissions'): Promise<any> {
    try {
      if (Platform.OS === 'android' && safePaymentDetection.testPaymentDetection) {
        return await safePaymentDetection.testPaymentDetection(testType);
      }
      return null;
    } catch (error) {
      console.error('测试支付检测失败:', error);
      return null;
    }
  }

  // 新增：获取详细的权限状态
  public async getDetailedPermissionStatus(): Promise<any> {
    try {
      if (Platform.OS === 'android') {
        const accessibility = await safePaymentDetection.checkAccessibilityPermission();
        const notification = await safePaymentDetection.checkNotificationPermission();
        const overlay = await safePaymentDetection.checkOverlayPermission();

        return {
          accessibility,
          notification,
          overlay,
          fullMode: accessibility && notification && overlay,
          notificationOnlyMode: notification,
          accessibilityOnlyMode: accessibility,
        };
      }
      return null;
    } catch (error) {
      console.error('获取权限状态失败:', error);
      return null;
    }
  }
}

export default PaymentDetectionService.getInstance();
