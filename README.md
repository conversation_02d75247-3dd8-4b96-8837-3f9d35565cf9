# Snow 财务管理应用

## 项目概述

Snow 是一款功能全面的个人财务管理应用，基于 React Native 开发，支持 iOS 和 Android 平台。应用提供直观的界面和丰富的功能，帮助用户轻松管理日常收支、分期账单、信用卡和商品价格追踪等。

## 主要功能

### 1. 交易记录管理
- **记账功能**：支持记录日常收入和支出
- **分类管理**：预设多种收支分类，支持自定义分类
- **备注功能**：为每笔交易添加详细备注
- **日期选择**：灵活选择交易日期
- **计算器功能**：内置计算器，方便计算金额
- **家庭账本**：支持个人和家庭账本切换

### 2. 分期账单管理
- **多种分期类型**：支持房贷、车贷、消费贷、信用卡、其他等多种分期类型
- **分期模式**：支持等额本金、递减还款、递增还款三种模式
- **还款预览**：直观显示首期、末期和平均每期还款金额
- **自动记账**：可开启自动记账功能，更改分期还款状态时自动添加/删除对应交易记录
- **还款进度**：可视化显示还款进度

### 3. 信用卡管理
- **信用卡列表**：管理多张信用卡
- **账单日提醒**：显示下次账单日和还款日
- **还款倒计时**：显示距离还款日的剩余天数
- **自定义卡片颜色**：个性化信用卡显示

### 4. 商品价格追踪
- **商品列表**：记录感兴趣的商品
- **价格历史**：追踪商品在不同平台的价格变化
- **购买状态**：标记商品是否已购买
- **价格对比**：比较不同平台的价格

### 5. 数据分析与预算
- **月度统计**：查看月度收支情况
- **消费行为分析**：智能分析消费习惯
- **预算设置**：设置日/月/年预算
- **健康评分**：评估财务健康状况

### 6. 其他功能
- **自动记账**：支持自动检测支付宝/微信支付记录
- **数据导入导出**：支持数据备份与恢复
- **主题设置**：支持自定义应用主题
- **家庭成员管理**：添加和管理家庭成员

## 主要页面

### 首页与导航
- **首页 (HomeScreen)**：显示日常收支记录和月度统计
- **底部导航栏**：提供账单、记账、我的三个主要导航选项

### 交易相关页面
- **添加交易 (AddTransactionScreen)**：记录新的收支
- **交易详情 (TransactionScreen)**：查看交易详情
- **月度详情 (MonthDetailScreen)**：查看月度收支统计

### 分期账单页面
- **分期管理 (InstallmentPlansScreen)**：显示所有分期账单
- **添加分期 (AddInstallmentPlanScreen)**：创建新的分期账单
- **分期详情 (InstallmentPlanDetailsScreen)**：查看分期详情和还款状态

### 信用卡页面
- **信用卡列表 (CreditCardListScreen)**：显示所有信用卡
- **添加信用卡 (CreditCardAddScreen)**：添加新的信用卡
- **信用卡详情 (CreditCardDetailScreen)**：查看信用卡详情

### 商品追踪页面
- **商品列表 (ProductListScreen)**：显示所有追踪的商品
- **商品详情 (ProductDetailScreen)**：查看商品详情和价格历史
- **编辑商品 (ProductEditScreen)**：编辑商品信息

### 设置与个人页面
- **个人中心 (ProfileScreen)**：用户个人设置和功能入口
- **分类设置 (CategorySettingsScreen)**：管理收支分类
- **主题设置 (ThemeSettingsScreen)**：自定义应用主题
- **家庭成员 (FamilyMembersScreen)**：管理家庭成员

## 技术栈

- **前端框架**：React Native
- **状态管理**：React Context API
- **数据存储**：SQLite (react-native-sqlite-storage)
- **UI组件**：自定义组件 + @ant-design/react-native
- **导航**：React Navigation
- **图标**：react-native-vector-icons/FontAwesome6
- **日期处理**：dayjs

## 安装与使用

### 环境要求
- Node.js
- React Native 开发环境
- iOS/Android 开发环境

### 安装步骤

1. 克隆仓库
```bash
git clone [仓库地址]
cd snow
```

2. 安装依赖
```bash
yarn install
# 或
npm install
```

3. iOS 额外步骤
```bash
cd ios && pod install
```

4. 运行应用
```bash
# iOS
npx react-native run-ios

# Android
npx react-native run-android
```

### 如何打包
```bash
npm run build
cd android
./gradlew assembleRelease
```
apk位置：android/app/build/outputs/apk/release/app-release.apk

## 贡献指南

欢迎提交问题和功能请求。如果您想贡献代码，请遵循以下步骤：

1. Fork 仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开一个 Pull Request